package cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 产品授权新增/修改 Request VO")
@Data
public class AuthProductSaveReqVO {

    @Schema(description = "主键id",  example = "24194")
    private Long id;

    @Schema(description = "项目id", example = "9616")
    private Long projectId;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称", example = "王五")
    private String productName;

    @Schema(description = "厂商", example = "张三")
    private String firmName;

    @Schema(description = "产品型号")
    private String productModel;

    @Schema(description = "节点类型（0直连，1网关，2网关子设备）", example = "2")
    private Integer nodeType;

}