package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 仪器类型模板-测量分量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentModelTemplateRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25238")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "仪器类型模板id", example = "8059")
    @ExcelProperty("仪器类型模板id")
    private Long instrumentId;

    @Schema(description = "分量名称", example = "赵六")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String thingUnit;

    @Schema(description = "数据类型，1：整数型，2：浮点型，3：双精度", example = "1")
    @ExcelProperty("数据类型，1：整数型，2：浮点型，3：双精度")
    private Integer dataType;

    @Schema(description = "下限")
    @ExcelProperty("下限")
    private String downLimit;

    @Schema(description = "上限")
    @ExcelProperty("上限")
    private String upLimit;

    @Schema(description = "小数位")
    @ExcelProperty("小数位")
    private Integer decimalLimit;

    @Schema(description = "分量类型，1：原始值，2：中间值，3：成果值", example = "2")
    @ExcelProperty("分量类型，1：原始值，2：中间值，3：成果值")
    private Integer thingType;

    @Schema(description = "权重，数字越小越靠前")
    @ExcelProperty("权重，数字越小越靠前")
    private Integer thingWeight;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}