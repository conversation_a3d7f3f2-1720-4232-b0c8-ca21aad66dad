package cn.powerchina.bjy.link.dam.dal.mysql.instrumentaccount;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.device.bo.DeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentaccount.InstrumentAccountDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import org.apache.ibatis.annotations.Mapper;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.*;

/**
 * 仪器台账 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentAccountMapper extends BaseMapperX<InstrumentAccountDO> {

    default PageResult<InstrumentAccountDO> selectPage(InstrumentAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstrumentAccountDO>()
                .eqIfPresent(InstrumentAccountDO::getProductionNumber, reqVO.getProductionNumber())
                .eqIfPresent(InstrumentAccountDO::getProjectId,reqVO.getProjectId())
                .eqIfPresent(InstrumentAccountDO::getInstrumentModel, reqVO.getInstrumentModel())
                .eqIfPresent(InstrumentAccountDO::getInstrumentId, reqVO.getInstrumentId())
                .eqIfPresent(InstrumentAccountDO::getInstrumentManufacturer, reqVO.getInstrumentManufacturer())
                .eqIfPresent(InstrumentAccountDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(InstrumentAccountDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InstrumentAccountDO::getId));
    }

    default PageResult<InstrumentAccountPageRespVO> selectAccountPage(InstrumentAccountPageReqVO reqVO) {
        MPJLambdaWrapperX<InstrumentAccountDO> wrapper = (MPJLambdaWrapperX<InstrumentAccountDO>) new MPJLambdaWrapperX<InstrumentAccountDO>()
                .selectAll(InstrumentAccountDO.class)
                .selectAs(InstrumentDO::getInstrumentName, InstrumentAccountPageRespVO::getInstrumentName)
                .leftJoin(InstrumentDO.class, "p", on -> on.eq(InstrumentAccountDO::getInstrumentId, InstrumentDO::getId))
                .eqIfExists(InstrumentAccountDO::getProjectId, reqVO.getProjectId())
                .likeIfExists(InstrumentAccountDO::getProductionNumber, reqVO.getProductionNumber())
                .eqIfExists(InstrumentAccountDO::getProjectId,reqVO.getProjectId())
                .likeIfExists(InstrumentAccountDO::getInstrumentModel, reqVO.getInstrumentModel())
                .eqIfExists(InstrumentAccountDO::getInstrumentId, reqVO.getInstrumentId())
                .likeIfExists(InstrumentAccountDO::getInstrumentManufacturer, reqVO.getInstrumentManufacturer())
                .eqIfExists(InstrumentAccountDO::getRemarks, reqVO.getRemarks())
                .eqIfExists(InstrumentDO::getInstrumentName, reqVO.getInstrumentName())
                .orderByDesc(InstrumentAccountDO::getId);
        return selectJoinPage(reqVO, InstrumentAccountPageRespVO.class, wrapper);
    }

}