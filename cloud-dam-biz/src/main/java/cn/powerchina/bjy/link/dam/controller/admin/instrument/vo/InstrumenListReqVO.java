package cn.powerchina.bjy.link.dam.controller.admin.instrument.vo;

import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 仪器类型模板新增/修改 Request VO")
@Data
public class InstrumenListReqVO {
    @Schema(description = "仪器类型模板新增参数")
    private InstrumentRespVO instrumentSaveReqVO;

    @Schema(description = "计算参数List")
    private List<InstrumentParamSaveReqVO> instrumentParamSaveReqVOS;

    @Schema(description = "测量分量List")
    private List<InstrumentModelRespVO> instrumentModelSaveReqVOS;
}