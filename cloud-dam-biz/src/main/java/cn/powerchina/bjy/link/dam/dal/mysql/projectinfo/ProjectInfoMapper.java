package cn.powerchina.bjy.link.dam.dal.mysql.projectinfo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectinfo.ProjectInfoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 项目工程信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectInfoMapper extends BaseMapperX<ProjectInfoDO> {

    default PageResult<ProjectInfoDO> selectPage(ProjectInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectInfoDO>()
                .eqIfPresent(ProjectInfoDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectInfoDO::getProjectInfo, reqVO.getProjectInfo())
                .betweenIfPresent(ProjectInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProjectInfoDO::getId));
    }

}