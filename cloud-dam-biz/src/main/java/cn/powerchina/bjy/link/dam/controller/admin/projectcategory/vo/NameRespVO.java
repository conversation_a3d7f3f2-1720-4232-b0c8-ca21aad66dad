package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 仪器类型和工程部位名称 Response VO")
@Data
@Builder
public class NameRespVO {

    @Schema(description = "仪器类型名称")
    private String instrumentName;

    @Schema(description = "工程部位")
    private String structCategoryName;

    @Schema(description = "工程部位")
    private Integer measureItem;
}
