package cn.powerchina.bjy.link.dam.service.iotdevice;

import cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice.IotDeviceDO;

import java.util.List;

/**
 * 物联网平台同步的设备 Service 接口
 *
 * <AUTHOR>
 */
public interface IotDeviceService {

    /**
     * 新增
     * @param iotDeviceDO 物联网平台库的设备
     */
    void createIotDevice(IotDeviceDO iotDeviceDO);

    /**
     * 根据物联网平台库的设备id删除
     * @param iotId 物联网平台库的设备id
     */
    void deleteByIotId(Long iotId);

    /**
     * 根据物联网平台库的设备id更新
     * @param iotDeviceDO 物联网平台库的设备
     */
    void updateByIotId(IotDeviceDO iotDeviceDO);

    /**
     * 根据设备编码，产品编码更新设备连接状态
     * @param iotDeviceDO 物联网平台库的设备（包含“设备编码”，“产品编码”，“连接状态”以及“最后上线时间”）
     */
    void updateLinkState(IotDeviceDO iotDeviceDO);

    /**
     * 根据物联网平台库的设备id获取
     * @param iotId 物联网平台库的设备id
     * @return 物联网平台同步的设备
     */
    IotDeviceDO getByIotId(Long iotId);

    /**
     * 获取所有物联网平台同步的设备
     * @return 物联网平台同步的设备列表
     */
    List<IotDeviceDO> list();

    /**
     * 根据物联网平台的设备id获取
     * @param iotIdList 物联网平台库的设备id列表
     * @return 物联网平台同步的设备列表
     */
    List<IotDeviceDO> listByIotId(List<Long> iotIdList);

    /**
     * 根据父设备编码列表获取
     * @param parentCodeList 父设备编码列表
     * @return 物联网平台同步的设备列表
     */
    List<IotDeviceDO> listByParentCode(List<String> parentCodeList);
}
