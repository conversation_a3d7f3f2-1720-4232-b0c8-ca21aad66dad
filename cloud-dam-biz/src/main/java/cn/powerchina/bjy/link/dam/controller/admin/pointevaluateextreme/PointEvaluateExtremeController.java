package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeResultVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluateextreme.PointEvaluateExtremeDO;
import cn.powerchina.bjy.link.dam.service.pointevaluateextreme.PointEvaluateExtremeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 测点评价指标极值")
@RestController
@RequestMapping("/dam/point-evaluate-extreme")
@Validated
public class PointEvaluateExtremeController {

    @Resource
    private PointEvaluateExtremeService pointEvaluateExtremeService;

    @PostMapping("/create")
    @Operation(summary = "创建测点评价指标极值")
    // @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:create')")
    public CommonResult<String> createPointEvaluateExtreme(@Valid @RequestBody PointEvaluateExtremeSaveReqVO createReqVO) {
        String message = pointEvaluateExtremeService.createPointEvaluateExtreme(createReqVO);
        if (Objects.isNull(message) || "".equals(message)) {
            return success(null);
        } else {
            return success(message);
        }
    }

    @PutMapping("/calculating")
    @Operation(summary = "计算极值")
    // @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:create')")
    public CommonResult<PointEvaluateExtremeResultVO> calculating(@Valid @RequestBody PointEvaluateExtremeReqVO evaluateExtremeReqVO) {
        return success(pointEvaluateExtremeService.calculating(evaluateExtremeReqVO));
    }

//    @PutMapping("/update")
//    @Operation(summary = "更新测点评价指标极值")
//   // @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:update')")
//    public CommonResult<Boolean> updatePointEvaluateExtreme(@Valid @RequestBody PointEvaluateExtremeSaveReqVO updateReqVO) {
//        pointEvaluateExtremeService.updatePointEvaluateExtreme(updateReqVO);
//        return success(true);
//    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测点评价指标极值")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:delete')")
    public CommonResult<Boolean> deletePointEvaluateExtreme(@RequestParam("id") Long id) {
        pointEvaluateExtremeService.deletePointEvaluateExtreme(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测点评价指标极值")
    @Parameter(name = "evaluateExtremeId", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:query')")
    public CommonResult<PointEvaluateExtremeRespVO> getPointEvaluateExtreme(@RequestParam("evaluateExtremeId") Long evaluateExtremeId) {
        PointEvaluateExtremeDO pointEvaluateExtreme = pointEvaluateExtremeService.getPointEvaluateExtreme(evaluateExtremeId);
        if (Objects.nonNull(pointEvaluateExtreme) && Objects.isNull(pointEvaluateExtreme.getDataType())) {
            pointEvaluateExtreme.setDataType(0);
        }
        return success(BeanUtils.toBean(pointEvaluateExtreme, PointEvaluateExtremeRespVO.class));
    }

//    @GetMapping("/page")
//    @Operation(summary = "获得测点评价指标极值分页")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:query')")
//    public CommonResult<PageResult<PointEvaluateExtremeRespVO>> getPointEvaluateExtremePage(@Valid PointEvaluateExtremePageReqVO pageReqVO) {
//        PageResult<PointEvaluateExtremeDO> pageResult = pointEvaluateExtremeService.getPointEvaluateExtremePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, PointEvaluateExtremeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出测点评价指标极值 Excel")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:export')")
//    public void exportPointEvaluateExtremeExcel(@Valid PointEvaluateExtremePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<PointEvaluateExtremeDO> list = pointEvaluateExtremeService.getPointEvaluateExtremePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "测点评价指标极值.xls", "数据", PointEvaluateExtremeRespVO.class,
//                        BeanUtils.toBean(list, PointEvaluateExtremeRespVO.class));
//    }

}