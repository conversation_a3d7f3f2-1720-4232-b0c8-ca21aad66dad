package cn.powerchina.bjy.link.dam.service.projectinfo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.bo.ProjectInfoBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectinfo.ProjectInfoDO;
import jakarta.validation.Valid;

/**
 * 项目工程信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectInfoService {

    /**
     * 创建项目工程信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveProjectInfo(@Valid ProjectInfoSaveReqVO createReqVO);

    /**
     * 删除项目工程信息
     *
     * @param id 编号
     */
    void deleteProjectInfo(Long id);

    /**
     * 获得项目工程信息
     *
     * @param id 编号
     * @return 项目工程信息
     */
    ProjectInfoDO getProjectInfo(Long id);

    /**
     * 获得项目工程信息
     *
     * @param projectId 项目id
     * @return 项目工程信息
     */
    ProjectInfoDO getProjectInfoByProjectId(Long projectId);

    /**
     * 获得项目工程信息
     *
     * @param projectId 项目id
     * @return 项目工程信息
     */
    ProjectInfoBO getProjectInfoBO(Long projectId);

    /**
     * 获得项目工程信息分页
     *
     * @param pageReqVO 分页查询
     * @return 项目工程信息分页
     */
    PageResult<ProjectInfoDO> getProjectInfoPage(ProjectInfoPageReqVO pageReqVO);

    /**
     * 删除工程信息
     *
     * @param projectId
     */
    void deleteProjectInfoByProjectId(Long projectId);
}