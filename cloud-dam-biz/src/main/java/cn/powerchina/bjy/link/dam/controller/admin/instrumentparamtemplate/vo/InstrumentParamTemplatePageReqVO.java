package cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 仪器类型模板-计算参数分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InstrumentParamTemplatePageReqVO extends PageParam {

    @Schema(description = "仪器类型模板id", example = "17436")
    private Long instrumentId;

    @Schema(description = "参数名称", example = "张三")
    private String thingName;

    @Schema(description = "参数标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    private String thingUnit;

    @Schema(description = "小数位")
    private Integer decimalLimit;

    @Schema(description = "权重，数字越小越靠前")
    private Integer thingWeight;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}