package cn.powerchina.bjy.link.dam.service.index;

import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexHeadBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexPointDeviceBO;

import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
public interface IndexService {

    /**
     * 获取首页信息
     *
     * @param projectId
     * @return
     */
    IndexBO findIndexInfo(Long projectId);

    /**
     * 获取首页头部信息
     *
     * @param projectId
     * @return
     */
    IndexHeadBO findIndexHeadInfo(Long projectId);

    /**
     * 获得首页仪器类型图表信息
     *
     * @param projectId
     * @return
     */
    List<IndexPointDeviceBO> findIndexInstrumentMiddle(Long projectId);

    /**
     * 获得首页仪器类型列表信息
     *
     * @param projectId
     * @return
     */
    List<IndexPointDeviceBO> findIndexInstrumentList(Long projectId);


    Boolean readIndexData();
}
