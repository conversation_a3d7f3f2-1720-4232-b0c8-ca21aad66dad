package cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import java.util.*;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 公式关联分量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FormulaModelRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9598")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "测点公式id", example = "10774")
    @ExcelProperty("测点公式id")
    private Long pointFormulaId;

    @Schema(description = "分量名称", example = "31788")
    private String thingName;

    @Schema(description = "分量id", example = "31788")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "取值条件，1：无，2：相对测值，3：首次测值：所有测次中第一次测值，4：时间范围内测值")
    @ExcelProperty("取值条件，1：无，2：相对测值，3：首次测值：所有测次中第一次测值，4：时间范围内测值")
    private Integer dataCondition;

    @Schema(description = "数值或1：之前，2：之后")
    @ExcelProperty("数值或1：之前，2：之后")
    private Integer dataValue;

    @Schema(description = "第n条测值或单位，1：分钟，2：小时，3：天")
    @ExcelProperty("第n条测值或单位，1：分钟，2：小时，3：天")
    private Integer dataUnit;

    @Schema(description = "指定时间")
    @ExcelProperty("指定时间")
    private LocalDateTime specifyTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}