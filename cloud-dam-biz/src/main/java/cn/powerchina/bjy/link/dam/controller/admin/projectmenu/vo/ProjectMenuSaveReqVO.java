package cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 项目菜单保存 Request VO")
@Data
public class ProjectMenuSaveReqVO {

    @Schema(description = "项目ID")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "菜单ID")
    @NotEmpty(message = "请至少选择一个菜单")
    private List<Long> menuIds;

}