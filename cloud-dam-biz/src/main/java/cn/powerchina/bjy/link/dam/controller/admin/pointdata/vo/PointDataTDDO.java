package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 测点数据 DO
 *
 * <AUTHOR>
 */
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class PointDataTDDO  {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 监测时间
     */
    private Long pointTime;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 采集类型(1：自动化采集，2：人工录入）
     */
    private Integer dataType;
    /**
     * 数据状态(0：未审核，1：正常数据，2：异常数据，3：错误数据）
     */
    private Integer dataStatus;

    private String creator;

    private Long createTime;

}