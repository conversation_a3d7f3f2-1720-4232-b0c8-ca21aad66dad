package cn.powerchina.bjy.link.dam.service.pointparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.InstrumentRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointparam.PointParamDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 测点计算参数 Service 接口
 *
 * <AUTHOR>
 */
public interface PointParamService {

    /**
     * 创建测点计算参数
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointParam(@Valid PointParamSaveReqVO createReqVO);

    /**
     * 更新测点计算参数
     *
     * @param updateReqVO 更新信息
     */
    void updatePointParam(@Valid PointParamSaveReqVO updateReqVO);

    /**
     * 删除测点计算参数
     *
     * @param id 编号
     */
    void deletePointParam(Long id);

    /**
     * 获得测点计算参数
     *
     * @param id 编号
     * @return 测点计算参数
     */
    PointParamDO getPointParam(Long id);

    /**
     * 获得测点计算参数分页
     *
     * @param pageReqVO 分页查询
     * @return 测点计算参数分页
     */
    PageResult<PointParamPageVO> getPointParamPage(PointParamPageReqVO pageReqVO);

    /**
     * 获得测点计算参数的动态表头
     *
     * @param
     * @return
     */
    List<PointParamTableRespVO> getPointParamTable(Long categoryId);

    /**
     * 获取仪器设备定义的参数列表
     *
     * @param pointId
     * @return
     */
    List<InstrumentParamDO> getPointInstrumentParams(Long pointId);

    /**
     * 判断时间段是否重合
     *
     * @param effectiveTimeVOList
     * @param newTimeVO
     * @return
     */
    boolean checkTimeOverLap(List<EffectiveTimeVO> effectiveTimeVOList, EffectiveTimeVO newTimeVO);

    /**
     * 根据测点id查询测点参数集合
     *
     * @param pointId
     * @return
     */
    List<PointParamDO> getPointParamByPointId(Long pointId);

    void updateListPointParam(List<PointParamSaveReqVO> updateReqVO);
}