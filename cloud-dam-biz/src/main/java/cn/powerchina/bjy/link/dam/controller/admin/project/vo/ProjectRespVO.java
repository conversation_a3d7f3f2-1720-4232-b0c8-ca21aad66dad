package cn.powerchina.bjy.link.dam.controller.admin.project.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 项目管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目名称")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区名称")
    private String areaName;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "详细地址")
    @ExcelProperty("详细地址")
    private String address;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    @Schema(description = "维度")
    @ExcelProperty("维度")
    private String latitude;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "项目管理员id")
    @ExcelProperty("项目管理员id")
    private String managerUserId;

    @Schema(description = "项目管理员名称")
    @ExcelProperty("项目管理员名称")
    private String managerUserName;

    @Schema(description = "项目管理员手机号")
    @ExcelProperty("项目管理员手机号")
    private String managerMobile;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}