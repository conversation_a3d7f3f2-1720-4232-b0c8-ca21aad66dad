package cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * @Description: 描述
 * @Author: z<PERSON>qiang
 * @CreateDate: 2024/10/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginReqVO {

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "登录账号不能为空")
    @Length(min = 3, max = 16, message = "账号长度为 3-16 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;
}
