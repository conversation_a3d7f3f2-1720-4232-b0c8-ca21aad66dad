package cn.powerchina.bjy.link.dam.service.pointparam;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointformula.PointFormulaDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointparam.PointParamDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointparam.PointParamMapper;
import cn.powerchina.bjy.link.dam.enums.ApplyTypeEnum;
import cn.powerchina.bjy.link.dam.enums.PointParamsTableEnum;
import cn.powerchina.bjy.link.dam.service.instrumentparam.InstrumentParamService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import com.alibaba.nacos.common.utils.CollectionUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 测点计算参数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointParamServiceImpl implements PointParamService {

    @Resource
    private PointParamMapper pointParamMapper;

    @Resource
    private PointService pointService;

    @Resource
    private InstrumentParamService instrumentParamService;

    @Resource
    private ProjectCategoryService projectCategoryService;

    @Override
    public Long createPointParam(PointParamSaveReqVO createReqVO) {
        getListAndCheckTime(createReqVO);
        // 插入
        PointParamDO pointParam = BeanUtils.toBean(createReqVO, PointParamDO.class);
        pointParamMapper.insert(pointParam);
        // 返回
        return pointParam.getId();
    }

    @Override
    public void updatePointParam(PointParamSaveReqVO updateReqVO) {
        // 校验存在
        validateUpdatePointParamExists(updateReqVO.getId());
        getListAndCheckTime(updateReqVO);
        // 更新
        PointParamDO updateObj = BeanUtils.toBean(updateReqVO, PointParamDO.class);
        pointParamMapper.updateById(updateObj);
    }

    @Override
    public void updateListPointParam(List<PointParamSaveReqVO> updateReqVO) {
        List<PointParamSaveReqVO> updateVO = updateReqVO.stream().filter(up -> null != up.getId() && !"".equals(up.getId())).collect(Collectors.toList());
        List<PointParamSaveReqVO> insertVO = updateReqVO.stream().filter(up -> null == up.getId() || "".equals(up.getId())).collect(Collectors.toList());
        List<PointParamDO> updateDO = BeanUtils.toBean(updateVO, PointParamDO.class);
        List<PointParamDO> insertDO = BeanUtils.toBean(insertVO, PointParamDO.class);

        List<Long> idList = updateVO.stream()
                .map(PointParamSaveReqVO::getId)  // 提取 id
                .collect(Collectors.toList());

        // 校验存在
        if (idList.size()>0){
            validatePointParamListExists(idList);
        }
        for (PointParamSaveReqVO pointParamSaveReqVO : updateReqVO) {
            getListAndCheckTime(pointParamSaveReqVO);
        }
        //校验本次提交时间是否有重复的
        for (PointParamSaveReqVO pointParamSaveReqVO : updateReqVO) {
            // 假设 removedVO 是要移除的对象
            List<PointParamSaveReqVO> newList = updateReqVO.stream()
                    .filter(vo -> !vo.equals(pointParamSaveReqVO))
                    .collect(Collectors.toList());
            if (checkTimeOverLap(BeanUtils.toBean(newList, EffectiveTimeVO.class), BeanUtils.toBean(pointParamSaveReqVO, EffectiveTimeVO.class))){
                throw exception(POINT_PARAM_TIME_LAP);
            }
        }
        // 插入
        pointParamMapper.insertBatch(insertDO);
        // 更新
        pointParamMapper.updateBatch(updateDO);
    }

    private void validatePointParamListExists(List<Long> idList) {
        if (pointParamMapper.selectList(new LambdaQueryWrapperX<PointParamDO>()
                .in(PointParamDO::getId, idList)) == null) {
            throw exception(POINT_PARAM_NOT_EXISTS);
        }
    }

    @Override
    public void deletePointParam(Long id) {
        // 校验存在
        validatePointParamExists(id);
        // 删除
        pointParamMapper.deleteById(id);
    }

    private void validatePointParamExists(Long id) {
        PointParamDO pointParamDO = pointParamMapper.selectById(id);
        if (pointParamDO == null) {
            throw exception(POINT_PARAM_NOT_EXISTS);
        }
        List<PointParamDO> pointParamDOS = pointParamMapper.selectList(new LambdaQueryWrapperX<PointParamDO>()
                .eq(PointParamDO::getPointId, pointParamDO.getPointId()).eq(PointParamDO::getProjectId, pointParamDO.getProjectId()));
        if (pointParamDOS.size() <=1 ){
            throw exception(POINT_PARAM_SIZE_ERROR);
        }
    }
    private void validateUpdatePointParamExists(Long id) {
        PointParamDO pointParamDO = pointParamMapper.selectById(id);
        if (pointParamDO == null) {
            throw exception(POINT_PARAM_NOT_EXISTS);
        }
    }

    @Override
    public PointParamDO getPointParam(Long id) {
        return pointParamMapper.selectById(id);
    }

    @Override
    public PageResult<PointParamPageVO> getPointParamPage(PointParamPageReqVO pageReqVO) {
        List<Long> pointList = projectCategoryService.getPointListByCategoryId(pageReqVO.getCategoryId());
        //空集合设置值
        if (CollectionUtil.isEmpty(pointList)) {
            pointList.add(Long.MAX_VALUE);
        }
        pageReqVO.setPointIdList(pointList);
        PageResult<PointParamPageVO> pointParamDOPageResult = pointParamMapper.selectPage(pageReqVO);
        ProjectCategoryLevelBO category = getProjectCategoryLevelBO(pageReqVO.getCategoryId());
        pointParamDOPageResult.getList().forEach(item -> {
            item.setInstrumentName(category.getInstrumentCategoryDO().getCategoryName());
        });
        if(CollectionUtils.isEmpty(pointParamDOPageResult.getList()))
        {
            PointBO pointBO=pointService.getPointBO(pageReqVO.getCategoryId());
            if(pointBO!=null)
            {
                PointParamPageVO pointParamPageVO=new PointParamPageVO();
                pointParamPageVO.setPointCode(pointBO.getPointCode());
                pointParamPageVO.setInstrumentName(pointBO.getInstrumentName());
                pointParamPageVO.setPointState(pointBO.getPointState());
                pointParamPageVO.setApplyType(pointBO.getPointType());
                pointParamPageVO.setEffectiveEndTime(LocalDateTime.now());
                pointParamPageVO.setEffectiveStartTime(LocalDateTime.now());
                pointParamPageVO.setCalcParam("[]");
                List<PointParamPageVO> list=new ArrayList<>();
                list.add(pointParamPageVO);
                pointParamDOPageResult.setList(list);
            }
        }
        return pointParamDOPageResult;
    }

    @Override
    public List<PointParamTableRespVO> getPointParamTable(Long categoryId) {
        ProjectCategoryLevelBO projectCategoryLevelBO = projectCategoryService.getProjectCategoryLevelBO(categoryId);

        List<PointParamTableRespVO> paramTableList = new ArrayList<>();
        paramTableList.add(new PointParamTableRespVO(PointParamsTableEnum.POINT_CODE.getLabelName(), PointParamsTableEnum.POINT_CODE.getFieldName()));
        paramTableList.add(new PointParamTableRespVO(PointParamsTableEnum.INSTRUMENT_NAME.getLabelName(), PointParamsTableEnum.INSTRUMENT_NAME.getFieldName()));
        paramTableList.add(new PointParamTableRespVO(PointParamsTableEnum.POINT_STATE.getLabelName(), PointParamsTableEnum.POINT_STATE.getFieldName()));
        if (Objects.nonNull(projectCategoryLevelBO) && Objects.nonNull(projectCategoryLevelBO.getInstrumentCategoryDO())) {
            List<InstrumentParamDO> instrumentParamDOList = instrumentParamService.getListByInstrumentId(projectCategoryLevelBO.getInstrumentCategoryDO().getBusinessId());
            if (CollectionUtils.isNotEmpty(instrumentParamDOList)) {
                instrumentParamDOList.forEach(item -> {
                    String labelName = item.getThingName();
                    if (!StringUtils.isEmpty(item.getThingUnit())) {
                        labelName = MessageFormat.format("{0}({1})", item.getThingName(), item.getThingUnit());
                    }
                    paramTableList.add(new PointParamTableRespVO(labelName, item.getThingIdentity()));
                });
            }
        }
        paramTableList.add(new PointParamTableRespVO(PointParamsTableEnum.EFFECTIVE_START_TIME.getLabelName(), PointParamsTableEnum.EFFECTIVE_START_TIME.getFieldName()));
        paramTableList.add(new PointParamTableRespVO(PointParamsTableEnum.EFFECTIVE_END_TIME.getLabelName(), PointParamsTableEnum.EFFECTIVE_END_TIME.getFieldName()));
        paramTableList.add(new PointParamTableRespVO(PointParamsTableEnum.APPLY_TYPE.getLabelName(), PointParamsTableEnum.APPLY_TYPE.getFieldName()));

        return paramTableList;
    }

    @Override
    public List<InstrumentParamDO> getPointInstrumentParams(Long pointId) {
        ProjectCategoryLevelBO projectCategoryLevelBO = getProjectCategoryLevelBO(pointId);
        return instrumentParamService.getListByInstrumentId(projectCategoryLevelBO.getInstrumentCategoryDO().getBusinessId());
    }

    @Override
    public boolean checkTimeOverLap(List<EffectiveTimeVO> effectiveTimeVOList, EffectiveTimeVO newTimeVO) {
        for (EffectiveTimeVO item : effectiveTimeVOList) {
            if (Objects.nonNull(newTimeVO.getId()) && newTimeVO.getId().equals(item.getId())) {
                continue;
            }
            if (!(newTimeVO.getEffectiveEndTime().isBefore(item.getEffectiveStartTime()) ||
                    newTimeVO.getEffectiveStartTime().isAfter(item.getEffectiveEndTime()))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<PointParamDO> getPointParamByPointId(Long pointId) {
        return pointParamMapper.selectList(new LambdaQueryWrapperX<PointParamDO>()
                .eq(PointParamDO::getPointId, pointId));
    }

    @NotNull
    private ProjectCategoryLevelBO getProjectCategoryLevelBO(Long categoryId) {
        ProjectCategoryLevelBO projectCategoryLevelBO = projectCategoryService.getProjectCategoryLevelBO(categoryId);
        if (Objects.isNull(projectCategoryLevelBO) || Objects.isNull(projectCategoryLevelBO.getInstrumentCategoryDO())) {
            throw exception(POINT_INSTRUMENT_NOT_EXISTS);
        }
        return projectCategoryLevelBO;
    }

    /**
     * 新增数据和已有数据进行比较，判断时间段是否有重合
     *
     * @param updateReqVO
     */
    private void getListAndCheckTime(PointParamSaveReqVO updateReqVO) {
        if (Objects.nonNull(updateReqVO.getEffectiveStartTime()) && Objects.nonNull(updateReqVO.getEffectiveEndTime())) {
            if (updateReqVO.getEffectiveStartTime().isAfter(updateReqVO.getEffectiveEndTime())) {
                throw exception(POINT_PARAM_TIME_ERROR);
            }
        }
        List<Integer> applyTypeList = new ArrayList<>();
        applyTypeList.add(ApplyTypeEnum.ALL.getCode());
        ApplyTypeEnum applyTypeEnum = ApplyTypeEnum.getByCode(updateReqVO.getApplyType());
        switch (applyTypeEnum) {
            case AUTO:
                applyTypeList.add(ApplyTypeEnum.AUTO.getCode());
                break;
            case MANUAL:
                applyTypeList.add(ApplyTypeEnum.MANUAL.getCode());
                break;
            default:
                applyTypeList.add(ApplyTypeEnum.AUTO.getCode());
                applyTypeList.add(ApplyTypeEnum.MANUAL.getCode());
                break;
        }
        //校验时间段重合
        List<PointParamDO> pointParamDOList = pointParamMapper.selectList(new LambdaQueryWrapperX<PointParamDO>()
                .in(PointParamDO::getApplyType, applyTypeList).eq(PointParamDO::getPointId, updateReqVO.getPointId()).orderByDesc(PointParamDO::getEffectiveStartTime));
        //开始和结束时间，一个为空 就算是全空
        List<PointParamDO> nullList = pointParamDOList.stream().filter(item -> item.getEffectiveStartTime() == null || item.getEffectiveEndTime() == null).toList();
        if (Objects.nonNull(updateReqVO.getId())) {
            //排除本身
            nullList = nullList.stream().filter(item -> !item.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
            pointParamDOList = pointParamDOList.stream().filter(item -> !item.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(nullList)) {
            throw exception(POINT_EVALUATE_TIME_LAP);
        }
        if (null != updateReqVO.getEffectiveStartTime() && null != updateReqVO.getEffectiveEndTime()) {
            if (updateReqVO.getEffectiveStartTime().isAfter(updateReqVO.getEffectiveEndTime())) {
                throw exception(POINT_PARAM_TIME_LAP);
            }
            //排除时间为空的数据
            pointParamDOList = pointParamDOList.stream().filter(item -> item.getEffectiveStartTime() != null && item.getEffectiveEndTime() != null).toList();
            if (CollectionUtil.isNotEmpty(pointParamDOList)) {
                if (checkTimeOverLap(BeanUtils.toBean(pointParamDOList, EffectiveTimeVO.class), BeanUtils.toBean(updateReqVO, EffectiveTimeVO.class))) {
                    throw exception(POINT_PARAM_TIME_LAP);
                }
            }
        } else {
            if (CollectionUtil.isNotEmpty(pointParamDOList)) {
                throw exception(POINT_PARAM_TIME_LAP);
            }
        }
    }
}