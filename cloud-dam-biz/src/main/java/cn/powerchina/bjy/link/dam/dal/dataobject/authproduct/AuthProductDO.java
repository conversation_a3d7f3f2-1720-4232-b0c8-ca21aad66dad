package cn.powerchina.bjy.link.dam.dal.dataobject.authproduct;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 产品授权 DO
 *
 * <AUTHOR>
 */
@TableName("dam_auth_product")
@KeySequence("dam_auth_product_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthProductDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 厂商
     */
    private String firmName;
    /**
     * 产品型号
     */
    private String productModel;
    /**
     * 节点类型（0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

}