package cn.powerchina.bjy.link.dam.controller.admin.point.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "工程分类id")
    @ExcelProperty("工程分类id")
    private Long categoryId;

    @Schema(description = "组别")
    @ExcelProperty("组别")
    private String categoryGroupName;

    @Schema(description = "测点编号")
    @ExcelProperty("测点编号")
    private String pointCode;

    @Schema(description = "测点名称")
    @ExcelProperty("测点名称")
    private String pointName;

    @Schema(description = "仪器类型id")
    @ExcelProperty("仪器类型id")
    private Long instrumentId;

    @Schema(description = "仪器台账id")
    @ExcelProperty("仪器台账id")
    private Long accountId;

    @Schema(description = "仪器类型名称")
    @ExcelProperty("仪器类型名称")
    private String instrumentName;

    @Schema(description = "工程结构id")
    @ExcelProperty("工程结构id")
    private Long structCategoryId;

    @Schema(description = "工程部位")
    @ExcelProperty("工程部位")
    private String structCategoryName;

    @Schema(description = "量程规格")
    @ExcelProperty("量程规格")
    private String rangeSpecifications;

    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    @ExcelProperty("监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @Schema(description = "重要程度，1：一般，2：重要")
    @ExcelProperty("重要程度，1：一般，2：重要")
    private Integer importantLevel;

    @Schema(description = "安装时间")
    @ExcelProperty("安装时间")
    private LocalDateTime installTime;

    @Schema(description = "基准时间")
    @ExcelProperty("基准时间")
    private LocalDateTime referenceTime;

    @Schema(description = "虚拟测点，0：否，1：是")
    @ExcelProperty("虚拟测点，0：否，1：是")
    private Integer virtualPoint;

    @Schema(description = "安装位置")
    @ExcelProperty("安装位置")
    private String installPosition;

    @Schema(description = "安装高程（m）")
    @ExcelProperty("安装高程（m）")
    private String installHeight;

    @Schema(description = "埋设位置")
    @ExcelProperty("埋设位置")
    private String hidePosition;

    @Schema(description = "埋设高程")
    @ExcelProperty("埋设高程")
    private String hideHeight;

    @Schema(description = "埋设深度")
    @ExcelProperty("埋设深度")
    private String hideDeepth;

    @Schema(description = "上下桩号")
    @ExcelProperty("上下桩号")
    private String updownPile;

    @Schema(description = "左右桩号")
    @ExcelProperty("左右桩号")
    private String leftrightPile;

    @Schema(description = "X坐标（m）")
    @ExcelProperty("X坐标（m）")
    private String xCoordinate;

    @Schema(description = "Y坐标（m）")
    @ExcelProperty("Y坐标（m）")
    private String yCoordinate;

    @Schema(description = "Z坐标（m）")
    @ExcelProperty("Z坐标（m）")
    private String zCoordinate;

    @Schema(description = "测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他")
    @ExcelProperty("测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他")
    private Integer pointState;

    @Schema(description = "测量类型，1：人/自一体，2：自动化，3：人工")
    @ExcelProperty("测量类型，1：人/自一体，2：自动化，3：人工")
    private Integer pointType;

    @Schema(description = "测量性质，1：运行期，2：施工期")
    @ExcelProperty("测量性质，1：运行期，2：施工期")
    private Integer pointStage;

    @Schema(description = "人工观测频次")
    private Integer manualFrequency;

    /**
     * 自动化观测频次
     */
    @Schema(description = "自动化观测频次")
    private Integer automatedFrequency;

}