package cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 设备授权新增/修改 Request VO")
@Data
public class AuthBindDeviceReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "设备id")
    @NotEmpty(message = "请选择设备")
    private List<Long> deviceIds;

}