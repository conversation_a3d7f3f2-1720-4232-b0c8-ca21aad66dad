package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo;

import cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory.ProjectCategoryDO;
import lombok.Data;

/**
 * @Description: 查找仪器类型和工程结构
 * @Author: yhx
 * @CreateDate: 2024/9/9
 */
@Data
public class ProjectCategoryLevelBO {

    /**
     * 工程结构
     */
    private ProjectCategoryDO structCategoryDO;
    /**
     * 仪器类型
     */
    private ProjectCategoryDO instrumentCategoryDO;
}
