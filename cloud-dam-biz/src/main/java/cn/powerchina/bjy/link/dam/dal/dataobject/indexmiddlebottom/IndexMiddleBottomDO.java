package cn.powerchina.bjy.link.dam.dal.dataobject.indexmiddlebottom;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 首页中间和底部数据 DO
 *
 * <AUTHOR>
 */
@TableName("dam_index_middle_bottom")
@KeySequence("dam_index_middle_bottom_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexMiddleBottomDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 仪器类型名称
     */
    private String instrumentName;
    /**
     * 仪器数量
     */
    private Long instrumentCount;
    /**
     * 仪器占比%
     */
    private String instrumentRate;
    /**
     * 观测记录数量
     */
    private Long pointDataCount;
    /**
     * 最近观测时间
     */
    private LocalDateTime pointTimeRecent;
    /**
     * 最早观测时间
     */
    private LocalDateTime pointTimeFirst;

    /**
     * 生成时间
     */
    private String generateTime;

}
