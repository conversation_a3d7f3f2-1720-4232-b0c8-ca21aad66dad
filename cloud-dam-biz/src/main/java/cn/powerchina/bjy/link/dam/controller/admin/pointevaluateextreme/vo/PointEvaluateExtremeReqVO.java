package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点评价指标极值新增/修改 Request VO")
@Data
public class PointEvaluateExtremeReqVO {

    @Schema(description = "主键id",  example = "22086")
    private Long id;

    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27258")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "测点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31317")
    @NotNull(message = "测点id不能为空")
    private Long pointId;

    @Schema(description = "分量id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28174")
    @NotNull(message = "分量id不能为空")
    private Long instrumentModelId;

    @Schema(description = "开始时间-时间戳")
    private LocalDateTime startTime;

    @Schema(description = "结束时间（小于等于当前时间）-时间戳")
    private LocalDateTime endTime;

    @Schema(description = "采集类型(0：全部，1：自动化采集，2：人工录入）", example = "2")
    private Integer dataType;

    @Schema(description = "最值法(1-极值百分比浮动，2-极值固定值浮动，3-固定数值)", example = "2")
    private Integer extremeType;

    @Schema(description = "异常最小值(1-极小值，2-极大值)", example = "1")
    private Integer abnormalDownType;

    @Schema(description = "异常最小值(1-加，2-减)")
    private Integer abnormalDownSymbol;

    @Schema(description = "异常最小值")
    @NotNull(message = "异常最小值不能为空")
    private String abnormalDown;

    @Schema(description = "正常最小值(1-极小值，2-极大值)", example = "1")
    private Integer waringDownType;

    @Schema(description = "正常最小值(1-加，2-减)")
    private Integer waringDownSymbol;

    @Schema(description = "正常最小值")
    @NotNull(message = "正常最小值不能为空")
    private String waringDown;

    @Schema(description = "异常最大值(1-极小值，2-极大值)", example = "2")
    private Integer abnormalUpType;

    @Schema(description = "异常最大值(1-加，2-减)")
    private Integer abnormalUpSymbol;

    @Schema(description = "异常最大值")
    @NotNull(message = "异常最大值不能为空")
    private String abnormalUp;

    @Schema(description = "正常最大值(1-极小值，2-极大值)", example = "1")
    private Integer waringUpType;

    @Schema(description = "正常最大值(1-加，2-减)")
    private Integer waringUpSymbol;

    @Schema(description = "正常最大值")
    @NotNull(message = "正常最大值不能为空")
    private String waringUp;

}