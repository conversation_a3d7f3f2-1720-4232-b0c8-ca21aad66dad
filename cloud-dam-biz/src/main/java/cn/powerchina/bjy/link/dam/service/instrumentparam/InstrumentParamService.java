package cn.powerchina.bjy.link.dam.service.instrumentparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 仪器类型-计算参数 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentParamService {

    /**
     * 创建仪器类型-计算参数
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrumentParam(@Valid InstrumentParamSaveReqVO createReqVO);

    /**
     * 更新仪器类型-计算参数
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrumentParam(@Valid InstrumentParamSaveReqVO updateReqVO);

    /**
     * 删除仪器类型-计算参数
     *
     * @param id 编号
     */
    void deleteInstrumentParam(Long id);

    /**
     * 获得仪器类型-计算参数
     *
     * @param id 编号
     * @return 仪器类型-计算参数
     */
    InstrumentParamDO getInstrumentParam(Long id);

    /**
     * 获得仪器类型-计算参数分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器类型-计算参数分页
     */
    PageResult<InstrumentParamDO> getInstrumentParamPage(InstrumentParamPageReqVO pageReqVO);

    /**
     * 根据仪器类型id获取参数
     *
     * @param instrumentId
     * @return
     */
    List<InstrumentParamDO> getListByInstrumentId(Long instrumentId);

    /**
     * 验证标识符是否存在
     *
     * @param id
     * @param instrumentId
     * @param thingIdentity
     */
    void validateThingIdentityExists(Long id, Long instrumentId, String thingIdentity);

    /**
     * 验证分量名称重复
     *
     * @param id
     * @param instrumentId
     * @param name
     */
    void validateNameExists(Long id, Long instrumentId, String name);

}