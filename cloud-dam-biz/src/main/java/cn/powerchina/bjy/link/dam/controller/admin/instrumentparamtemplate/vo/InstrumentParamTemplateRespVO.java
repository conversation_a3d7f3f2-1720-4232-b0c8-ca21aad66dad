package cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 仪器类型模板-计算参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentParamTemplateRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1159")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "仪器类型模板id", example = "17436")
    @ExcelProperty("仪器类型模板id")
    private Long instrumentId;

    @Schema(description = "参数名称", example = "张三")
    @ExcelProperty("参数名称")
    private String thingName;

    @Schema(description = "参数标识符")
    @ExcelProperty("参数标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String thingUnit;

    @Schema(description = "小数位")
    @ExcelProperty("小数位")
    private Integer decimalLimit;

    @Schema(description = "权重，数字越小越靠前")
    @ExcelProperty("权重，数字越小越靠前")
    private Integer thingWeight;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}