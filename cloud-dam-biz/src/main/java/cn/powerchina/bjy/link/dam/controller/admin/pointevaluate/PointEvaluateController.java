package cn.powerchina.bjy.link.dam.controller.admin.pointevaluate;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.bo.PointEvaluateBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.*;
import cn.powerchina.bjy.link.dam.service.pointevaluate.PointEvaluateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点评价指标")
@RestController
@RequestMapping("/dam/point/evaluate")
@Validated
public class PointEvaluateController {

    @Resource
    private PointEvaluateService pointEvaluateService;

    @PostMapping("/create")
    @Operation(summary = "创建测点评价指标")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate:create')")
    public CommonResult<Long> createPointEvaluate(@Valid @RequestBody PointEvaluateSaveReqVO createReqVO) {
        return success(pointEvaluateService.createPointEvaluate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新测点评价指标")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate:update')")
    public CommonResult<Boolean> updatePointEvaluate(@Valid @RequestBody PointEvaluateSaveReqVO updateReqVO) {
        pointEvaluateService.updatePointEvaluate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测点评价指标")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate:delete')")
    public CommonResult<Boolean> deletePointEvaluate(@RequestParam("id") Long id) {
        pointEvaluateService.deletePointEvaluate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测点评价指标")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate:query')")
    public CommonResult<PointEvaluateRespVO> getPointEvaluate(@RequestParam("id") Long id) {
        PointEvaluateBO pointEvaluate = pointEvaluateService.getPointEvaluateBO(id);
        return success(BeanUtils.toBean(pointEvaluate, PointEvaluateRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得测点评价指标list集合", description = "有id入库数据，没有id分量默认的初始化数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate:query')")
    public CommonResult<List<PointEvaluatePageRespVO>> getPointEvaluatePage(@Valid PointEvaluateReqVO evaluateReqVO) {
        List<PointEvaluatePageRespVO> pageResult = pointEvaluateService.selectList(evaluateReqVO);
        return success(BeanUtils.toBean(pageResult, PointEvaluatePageRespVO.class));
    }

    @GetMapping("/get-selected-point")
    @Operation(summary = "获得已选测点")
    @Parameter(name = "extremeId", description = "极值Id", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('dam:point-evaluate-extreme:query')")
    public CommonResult<List<PointEvaluatePageRespVO>> getSelectedPoint(@RequestParam(value="extremeId",required = false) Long extremeId) {
        List<PointEvaluatePageRespVO> pointEvaluateExtreme = pointEvaluateService.getSelectedPoint(extremeId);
        return success(pointEvaluateExtreme);
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点评价指标分页")
//    @PreAuthorize("@ss.hasPermission('dam:point-evaluate:query')")
    public CommonResult<PageResult<PointEvaluatePageRespVO>> getPointEvaluatePage(@Valid PointEvaluatePageReqVO pageReqVO) {
        PageResult<PointEvaluatePageRespVO> pageResult = pointEvaluateService.getPointEvaluatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointEvaluatePageRespVO.class));
    }
}