package cn.powerchina.bjy.link.dam.service.device;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.device.bo.DeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.device.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import jakarta.validation.Valid;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 大坝设备 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceService {

    /**
     * 创建大坝设备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDevice(@Valid DeviceSaveReqVO createReqVO);

    /**
     * 更新大坝设备
     *
     * @param updateReqVO 更新信息
     */
    void updateDevice(@Valid DeviceSaveReqVO updateReqVO);

    /**
     * 删除大坝设备
     *
     * @param id 编号
     */
    void deleteDevice(Long id);

    /**
     * 批量删除大坝设备
     *
     * @param idList id列表
     */
    void deleteDeviceList(List<Long> idList);

    /**
     * 获得大坝设备
     *
     * @param id 编号
     * @return 大坝设备
     */
    DeviceDO getDevice(Long id);

    /**
     * 获得大坝设备
     *
     * @param id
     * @return
     */
    DeviceBO getDeviceBO(Long id);

    /**
     * 获得大坝设备分页
     *
     * @param pageReqVO 分页查询
     * @return 大坝设备分页
     */
    PageResult<DeviceBO> getDevicePage(DevicePageReqVO pageReqVO);

    /**
     * 获得大坝设备分页
     *
     * @param pageReqVO 分页查询
     * @return 大坝设备分页
     */
    PageResult<DeviceBO> getDeviceBOPage(DevicePageReqVO pageReqVO);

    /**
     * 增加大坝设备
     *
     * @param projectId
     * @param deviceGroupIds
     */
    List<String> addDeviceByDeviceGroupIds(Long projectId, List<Long> deviceGroupIds);

    /**
     * 根据策略查询设备数量
     *
     * @param strategyId
     * @return
     */
    Long countDeviceByStrategyId(Long strategyId);

    /**
     * 根据站点查询采集仪数量
     *
     * @param stationId
     * @return
     */
    Long countDeviceByStationId(Long stationId);

    /**
     * 根据设备编码获取设备
     *
     * @param deviceCode
     * @return
     */
    DeviceDO getDeviceDOByDeviceCode(String deviceCode);

    /**
     * 根据设备编码获取设备
     *
     * @param projectId
     * @param deviceCode
     * @return
     */
    DeviceDO getDeviceDOByProjectIdAndDeviceCode(Long projectId, String deviceCode);

    /**
     * 测点绑定与解绑
     *
     * @param reqVO
     */
    void deviceBindPoint(DeviceBindReqVO reqVO);

    /**
     * 根据项目id和测站id进行查询
     *
     * @param projectId
     * @param stationId
     * @return
     */
    List<DeviceDO> getDeviceDOListByProjectIdAndStationId(Long projectId, Long stationId);

    /**
     * 根据项目id和父设备编码查找子设备
     *
     * @param projectId
     * @param parentCode
     * @return
     */
    List<DeviceDO> getDeviceDOListByProjectIdAndParentCode(Long projectId, String parentCode);

    /**
     * 根据父设备编码查找子设备
     *
     * @param parentCode 父设备编码
     * @return 子设备列表
     */
    List<DeviceDO> listByParentCode(String parentCode);

    /**
     * 设备实时采集
     *
     * @param reqVO
     * @return
     */
    void deviceDataCollect(DeviceCollectReqVO reqVO);

    /**
     * 设备时钟同步
     * @param reqVO
     */
    void deviceClockSync(DeviceCollectReqVO reqVO);

    /**
     * 统计设备数量
     *
     * @param projectId
     * @param nodeType
     * @param startTime
     * @return
     */
    Long countDeviceByProjectIdAndNodeType(Long projectId, Integer nodeType, Date startTime);

    /**
     * 根据设备编码和通道编码获取设备
     *
     * @param deviceCode
     * @return
     */
    DeviceDO getDeviceDOByDeviceCodeAndMcuChannel(String deviceCode, String mcuChannel);

    /**
     * 根据策略获得大坝设备
     *
     * @param strategyId 策略id
     * @return 大坝设备
     */
    List<DeviceDO> getDeviceByStrategyId(Long strategyId);

    /**
     * 更新设备状态
     *
     * @param deviceCheckOnlineModel 更新信息
     */
    void updateDeviceCheckOnline(DeviceCheckOnlineModel deviceCheckOnlineModel);

    /**
     * 更新设备连接状态
     * @param deviceDO 设备信息（包含“设备编码”，“产品编码”，“连接状态”以及“最后上线时间”）
     */
    void updateLinkState(DeviceDO deviceDO);

    /**
     * 更新设备信息
     * @param deviceDOList 设备信息
     */
    void updateBatch(List<DeviceDO> deviceDOList);

    /**
     * 变更设备的电压
     *
     * @param deviceCode
     * @param voltage
     */
    void updateDeviceVoltage(String deviceCode, String voltage, Long reportTime);

    /**
     * 变更更设备的温度
     * @param deviceCode
     * @param temperature
     */
    void updateDeviceTemperature(String deviceCode, String temperature, Long reporTime);

    /**
     * 变更设备的时钟
     * @param deviceCode
     * @param deviceClockTime
     */
    void updateDeviceClockTime(String deviceCode, LocalDateTime deviceClockTime, Long reporTime);

    /**
     * 空间下没有绑定的设备列表
     *
     * @param iotPageReqVO
     * @return
     */
    PageResult<DeviceIotRespVO> spaceNoBindDevices(DeviceIotPageReqVO iotPageReqVO);

    /**
     * 获取设备详情
     * @param id
     * @return
     */
    DeviceDetailResVO getDeviceDetail(Long id);

    /**
     * 绑定网关设备，自动绑定网关下的子设备
     *
     * @param projectId   项目id
     * @param deviceIds 设备网关idid
     * @return 添加设备的产品编码
     */
    List<String> bindGatewayDevices(Long projectId, List<Long> deviceIds);

    /**
     * 删除主设备
     * @param deviceId
     */
    void deleteGatewayDevice(Long deviceId);

    void secret(DeviceSecretVO secretVO) throws Exception;
}