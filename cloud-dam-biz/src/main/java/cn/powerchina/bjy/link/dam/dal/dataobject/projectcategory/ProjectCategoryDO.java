package cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 工程分类管理 DO
 *
 * <AUTHOR>
 */
@TableName("dam_project_category")
@KeySequence("dam_project_category_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectCategoryDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 父节点id
     */
    private Long parentId;
    /**
     * 节点名称
     */
    private String categoryName;
    /**
     * 节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点，6：网关设备（采集仪），7：网关子设备（传感器））
     */
    private Integer categoryType;
    /**
     * 层级
     */
    private Integer categoryLevel;

    /**
     * 描述
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String categoryRemark;

    /**
     * 图片名称
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String imageName;

    /**
     * 图片地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String imagePath;

    /**
     * 业务id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long businessId;

}