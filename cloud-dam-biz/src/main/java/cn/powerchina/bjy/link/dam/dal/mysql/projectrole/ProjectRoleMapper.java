package cn.powerchina.bjy.link.dam.dal.mysql.projectrole;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRolePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectrole.ProjectRoleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 项目角色信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectRoleMapper extends BaseMapperX<ProjectRoleDO> {

    default PageResult<ProjectRoleDO> selectPage(ProjectRolePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectRoleDO>()
                .eqIfPresent(ProjectRoleDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(ProjectRoleDO::getRoleName, reqVO.getRoleName())
                .eqIfPresent(ProjectRoleDO::getRemark, reqVO.getRemark())
                .orderByDesc(ProjectRoleDO::getCreateTime));
    }

}