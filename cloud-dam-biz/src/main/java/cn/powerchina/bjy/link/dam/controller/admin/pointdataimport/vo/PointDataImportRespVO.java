package cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据导入 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataImportRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "数据类型，1：原始值，2：中间值，3：成果值")
    @ExcelProperty("数据类型，1：原始值，2：中间值，3：成果值")
    private Integer dataType;

    @Schema(description = "导入类型，1：追加导入，2：覆盖导入")
    @ExcelProperty("导入类型，1：追加导入，2：覆盖导入")
    private Integer importType;

    @Schema(description = "覆盖开始时间")
    @ExcelProperty("覆盖开始时间")
    private LocalDateTime startTime;

    @Schema(description = "覆盖结束时间")
    @ExcelProperty("覆盖结束时间")
    private LocalDateTime endTime;

    @Schema(description = "excel文件地址")
    @ExcelProperty("excel文件地址")
    private String filePath;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}