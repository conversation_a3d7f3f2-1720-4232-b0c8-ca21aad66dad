package cn.powerchina.bjy.link.dam.service.instrumentparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrument.InstrumentMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentparam.InstrumentParamMapper;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 仪器类型-计算参数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentParamServiceImpl implements InstrumentParamService {

    @Resource
    private InstrumentParamMapper instrumentParamMapper;

    @Resource
    private InstrumentMapper instrumentMapper;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private InstrumentService instrumentService;


    @Override
    public Long createInstrumentParam(InstrumentParamSaveReqVO createReqVO) {
        validateNameExists(createReqVO.getId(), createReqVO.getInstrumentId(), createReqVO.getThingName());
        validateThingIdentityExists(createReqVO.getId(), createReqVO.getInstrumentId(), createReqVO.getThingIdentity());
        instrumentModelService.validateNameExists(createReqVO.getId(), createReqVO.getInstrumentId(), createReqVO.getThingName());
        //判断分量中是否存在相同的标识符
        instrumentModelService.validateThingIdentityExists(null, createReqVO.getInstrumentId(), createReqVO.getThingIdentity());
        // 插入
        InstrumentParamDO instrumentParam = BeanUtils.toBean(createReqVO, InstrumentParamDO.class);
        instrumentParamMapper.insert(instrumentParam);
        // 返回
        return instrumentParam.getId();
    }

    @Override
    public void updateInstrumentParam(InstrumentParamSaveReqVO updateReqVO) {
        // 校验存在
        validateInstrumentParamExists(updateReqVO.getId());
        validateNameExists(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingName());
        instrumentModelService.validateNameExists(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingName());
        validateThingIdentityExists(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingIdentity());
        //判断分量中是否存在相同的标识符
        instrumentModelService.validateThingIdentityExists(null, updateReqVO.getInstrumentId(), updateReqVO.getThingIdentity());
        // 更新
        InstrumentParamDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentParamDO.class);
        instrumentParamMapper.updateById(updateObj);
    }

    @Override
    public void deleteInstrumentParam(Long id) {
        // 校验存在
        validateInstrumentParamExists(id);
        InstrumentParamDO instrumentParamDO = instrumentParamMapper.selectById(id);
        List<InstrumentModelDO> modelList = instrumentModelService.getListUsingByFormula(instrumentParamDO.getInstrumentId(), instrumentParamDO.getThingIdentity());
        if (!CollectionUtils.isEmpty(modelList)) {
            throw exception(INSTRUMENT_PARAMS_USING);
        }
        //校验是否绑定测点
        Boolean bindPoint = instrumentService.checkPoint(instrumentParamDO.getInstrumentId());
        if (bindPoint) {
            throw exception(INSTRUMENT_USING_POINT);
        }
        // 删除
        instrumentParamMapper.deleteById(id);
    }

    private void validateInstrumentParamExists(Long id) {
        if (instrumentParamMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_PARAM_NOT_EXISTS);
        }
    }

    @Override
    public InstrumentParamDO getInstrumentParam(Long id) {
        return instrumentParamMapper.selectById(id);
    }

    @Override
    public PageResult<InstrumentParamDO> getInstrumentParamPage(InstrumentParamPageReqVO pageReqVO) {
        return instrumentParamMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InstrumentParamDO> getListByInstrumentId(Long instrumentId) {
        validateInstrumentExists(instrumentId);
        return instrumentParamMapper.selectList(new LambdaQueryWrapperX<InstrumentParamDO>().
                eq(InstrumentParamDO::getInstrumentId, instrumentId).orderByAsc(InstrumentParamDO::getThingWeight).orderByDesc(InstrumentParamDO::getCreateTime));
    }

    private void validateInstrumentExists(Long id) {
        if (instrumentMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
    }

    /**
     * 参数名称是否存在
     *
     * @param id
     * @param name
     */
    @Override
    public void validateNameExists(Long id, Long instrumentId, String name) {
        InstrumentParamDO instrumentParamDO = instrumentParamMapper.selectOne(new LambdaQueryWrapperX<InstrumentParamDO>().
                eq(InstrumentParamDO::getThingName, name).eq(InstrumentParamDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentParamDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentParamDO.getId()))) {
//            throw exception(ErrorCodeConstants.INSTRUMENT_PARAMS_NAME_EXISTS);
            throw new ClassCastException(ErrorCodeConstants.INSTRUMENT_PARAMS_NAME_EXISTS.getCode()+":"+name);
        }
    }

    /**
     * 标识符是否存在
     *
     * @param id
     * @param instrumentId
     * @param thingIdentity
     */
    public void validateThingIdentityExists(Long id, Long instrumentId, String thingIdentity) {
        InstrumentParamDO instrumentParamDO = instrumentParamMapper.selectOne(new LambdaQueryWrapperX<InstrumentParamDO>().
                eq(InstrumentParamDO::getThingIdentity, thingIdentity).eq(InstrumentParamDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentParamDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentParamDO.getId()))) {
//            throw exception(ErrorCodeConstants.INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS);
            throw new ClassCastException(ErrorCodeConstants.INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS.getCode()+":"+thingIdentity);
        }
    }

}