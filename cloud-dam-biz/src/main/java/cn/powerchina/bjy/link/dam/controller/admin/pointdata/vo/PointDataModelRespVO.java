package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/10/17
 */
@Schema(description = "管理后台 - 折线图测量分量值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataModelRespVO {

    private Map<String, PointDataVO> modelValueMap;

    private List<PointTimeVO> pointTime;
}
