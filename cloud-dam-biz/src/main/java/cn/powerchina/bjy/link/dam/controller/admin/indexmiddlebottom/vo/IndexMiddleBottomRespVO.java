package cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 首页中间和底部数据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class IndexMiddleBottomRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28350")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目id", example = "15150")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "仪器类型名称", example = "赵六")
    @ExcelProperty("仪器类型名称")
    private String instrumentName;

    @Schema(description = "仪器数量", example = "6337")
    @ExcelProperty("仪器数量")
    private Long instrumentCount;

    @Schema(description = "仪器占比%")
    @ExcelProperty("仪器占比%")
    private String instrumentRate;

    @Schema(description = "观测记录数量", example = "20780")
    @ExcelProperty("观测记录数量")
    private Long pointDataCount;

    @Schema(description = "最近观测时间")
    @ExcelProperty("最近观测时间")
    private LocalDateTime pointTimeRecent;

    @Schema(description = "最早观测时间")
    @ExcelProperty("最早观测时间")
    private LocalDateTime pointTimeFirst;

    @Schema(description = "生成时间")
    private String generateTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
