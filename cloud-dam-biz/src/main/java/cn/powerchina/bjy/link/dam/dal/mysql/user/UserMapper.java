package cn.powerchina.bjy.link.dam.dal.mysql.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectuser.ProjectUserDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapperX<UserDO> {

    default PageResult<UserDO> selectPage(UserPageReqVO reqVO) {
        LambdaQueryWrapperX<UserDO> wrapperX = new LambdaQueryWrapperX();
        wrapperX.likeIfPresent(UserDO::getUsername, reqVO.getUsername())
                .likeIfPresent(UserDO::getName, reqVO.getName())
                .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(UserDO::getUserType, reqVO.getUserType());
        if (Objects.nonNull(reqVO.getProjectId())) {
            if (CollectionUtil.isEmpty(reqVO.getUserIdList())) {
                reqVO.setUserIdList(Arrays.asList(Long.MAX_VALUE));
            }
            wrapperX.in(UserDO::getId, reqVO.getUserIdList());
        }
        wrapperX.orderByDesc(UserDO::getCreateTime);
        return selectPage(reqVO, wrapperX);
    }

    default PageResult<UserBO> selectProjectPage(ProjectUserPageReqVO reqVO) {
        MPJLambdaWrapperX<UserDO> wrapper = (MPJLambdaWrapperX<UserDO>) new MPJLambdaWrapperX<UserDO>()
                .selectAll(UserDO.class)
                .likeIfPresent(UserDO::getUsername, reqVO.getUsername())
                .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
                .leftJoin(ProjectUserDO.class, "d", on -> on.eq(ProjectUserDO::getUserId, UserDO::getId))
                .eq(ProjectUserDO::getProjectId, reqVO.getProjectId())
                .orderByDesc(UserDO::getCreateTime);
        return selectJoinPage(reqVO, UserBO.class, wrapper);
    }

}