package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型-计算公式修改 Request VO")
@Data
public class InstrumentFormulaUpdateReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "仪器类型id")
    @NotNull(message = "仪器类型id不能为空")
    private Long instrumentId;

    @Schema(description = "计算公式")
    @NotNull(message = "计算公式不能为空")
    private String calcFormula;

    @Schema(description = "公式有效开始时间")
    @NotNull(message = "公式有效开始时间不能为空")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "公式有效结束时间")
    @NotNull(message = "公式有效结束时间不能为空")
    private LocalDateTime effectiveEndTime;

}