package cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 仪器类型-计算参数新增/修改 Request VO")
@Data
public class InstrumentParamSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1864")
    private Long id;

    @Schema(description = "项目id", example = "28666")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "仪器类型id", example = "9306")
    private Long instrumentId;

    @Schema(description = "参数名称", example = "王五")
    @NotNull(message = "参数名称不能为空")
    @Length(max = 32)
    private String thingName;

    @Schema(description = "参数标识符")
    @NotNull(message = "参数标识符不能为空")
    @Length(max = 32)
    private String thingIdentity;

    @Schema(description = "单位")
    @Length(max = 32)
    private String thingUnit;

    @Schema(description = "小数位")
    @NotNull(message = "小数位不能为空")
    @Min(value = 0,message = "必须是大于等于0的整数")
    @Max(15)
    private Integer decimalLimit;

    @Schema(description = "权重，数字越小越靠前")
    @Min(0)
    private Integer thingWeight;

}