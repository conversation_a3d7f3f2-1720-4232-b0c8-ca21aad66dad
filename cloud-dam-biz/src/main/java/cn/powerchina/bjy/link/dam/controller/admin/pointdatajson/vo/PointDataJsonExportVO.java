package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据json Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataJsonExportVO {


    @Schema(description = "测点编号", example = "24279")
    @ExcelProperty("测点编号")
    private String pointCode;

    @Schema(description = "监测时间")
    @ExcelProperty("监测时间")
    private LocalDateTime pointTime;

    @Schema(description = "分量值json")
    private String pointData;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    @ExcelProperty("采集类型(1：自动化采集，2：人工录入）")
    private Integer dataType;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    @ExcelProperty("数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）")
    private Integer dataStatus;

    @Schema(description = "审核状态")
    private Integer reviewStatus;

//    private String reviewer;

    @Schema(description = "审核人")
    private String reviewName;
}