package cn.powerchina.bjy.link.dam.dal.mysql.projectuser;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectuser.ProjectUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectUserMapper extends BaseMapperX<ProjectUserDO> {

    default PageResult<ProjectUserDO> selectPage(ProjectUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectUserDO>()
                .eqIfPresent(ProjectUserDO::getProjectId, reqVO.getProjectId())
                .orderByDesc(ProjectUserDO::getId));
    }

    /**
     * 根据用户id查询用户关联的项目名称
     * @param userId
     * @return
     */
    List<String> selectProjectNameByUserId(@Param("userId") Long userId);

    /**
     * 根据项目id查询所有用户id
     * @param projectId
     * @return
     */
    List<Long> selectUserIdByProjectId(@Param("projectId") Long projectId);

}