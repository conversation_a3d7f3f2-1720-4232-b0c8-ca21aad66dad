package cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 测点评价
 * @Author: yhx
 * @CreateDate: 2024/9/27
 */
@Data
public class PointEvaluateBO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量名称
     */
    private String thingName;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 报警上限
     */
    private String waringUp;
    /**
     * 报警下限
     */
    private String waringDown;
    /**
     * 有效开始时间
     */
    private LocalDateTime effectiveStartTime;
    /**
     * 有效结束时间
     */
    private LocalDateTime effectiveEndTime;
    /**
     * 适用类型，1：全部，2：自动化，3：人工
     */
    private Integer applyType;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 下限
     */
    private String downLimit;
    /**
     * 上限
     */
    private String upLimit;
    /**
     * 小数位
     */
    private Integer decimalLimit;

    /**
     * 异常上限
     */
    private String abnormalUp;
    /**
     * 异常下限
     */
    private String abnormalDown;
}
