package cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 仪器类型-计算参数分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InstrumentParamPageReqVO extends PageParam {

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "仪器类型id")
    @NotNull(message = "请选择仪器类型")
    private Long instrumentId;

    @Schema(description = "参数名称")
    private String thingName;

    @Schema(description = "参数标识符")
    private String thingIdentity;

}