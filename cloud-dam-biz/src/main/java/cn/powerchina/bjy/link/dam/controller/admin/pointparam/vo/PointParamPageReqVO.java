package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 测点计算参数分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointParamPageReqVO extends PageParam {

    @Schema(description = "项目id", example = "20428")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "工程分类id")
    @NotNull(message = "请选择仪器类型或分组或测点")
    private Long categoryId;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他")
    private Integer pointState;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    private Integer applyType;

    @Schema(description = "测点id", hidden = true)
    private List<Long> pointIdList;

}