package cn.powerchina.bjy.link.dam.service.formulapoint;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointListVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo.PointFormulaModelVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulapoint.FormulaPointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointparam.PointParamDO;
import cn.powerchina.bjy.link.dam.dal.mysql.formulapoint.FormulaPointMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.FORMULA_POINT_NOT_EXISTS;

/**
 * 公式关联测点 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FormulaPointServiceImpl implements FormulaPointService {

    @Resource
    private FormulaPointMapper formulaPointMapper;

    @Override
    public Long createFormulaPoint(FormulaPointSaveReqVO createReqVO) {
        // 插入
        FormulaPointDO formulaPoint = BeanUtils.toBean(createReqVO, FormulaPointDO.class);
        formulaPointMapper.insert(formulaPoint);
        // 返回
        return formulaPoint.getId();
    }

    @Override
    public void updateFormulaPoint(FormulaPointSaveReqVO updateReqVO) {
        // 校验存在
        validateFormulaPointExists(updateReqVO.getId());
        // 更新
        FormulaPointDO updateObj = BeanUtils.toBean(updateReqVO, FormulaPointDO.class);
        formulaPointMapper.updateById(updateObj);
    }

    @Override
    public void deleteFormulaPoint(Long id) {
        // 校验存在
        validateFormulaPointExists(id);
        // 删除
        formulaPointMapper.deleteById(id);
    }

    private void validateFormulaPointExists(Long id) {
        if (formulaPointMapper.selectById(id) == null) {
            throw exception(FORMULA_POINT_NOT_EXISTS);
        }
    }

    @Override
    public FormulaPointDO getFormulaPoint(Long id) {
        return formulaPointMapper.selectById(id);
    }

    @Override
    public PageResult<FormulaPointDO> getFormulaPointPage(FormulaPointPageReqVO pageReqVO) {
        return formulaPointMapper.selectPage(pageReqVO);
    }

    @Override
    public List<PointFormulaModelVO> getFormulaModelByPointFormulaId(Long pointFormulaId) {
        List<PointFormulaModelVO> pointFormulaModelVOS = formulaPointMapper.selectJoinList(PointFormulaModelVO.class, new MPJLambdaWrapperX<FormulaPointDO>()
                .selectAs(FormulaPointDO::getId, PointFormulaModelVO::getFormulaId)
                .selectAs(FormulaPointDO::getPointId, PointFormulaModelVO::getPointId)
                .selectAs(FormulaPointDO::getDataCondition, PointFormulaModelVO::getDataCondition)
                .selectAs(InstrumentModelDO::getThingIdentity, PointFormulaModelVO::getThingIdentity)
                .selectAs(InstrumentModelDO::getThingName, PointFormulaModelVO::getThingName)
                .selectAs(InstrumentModelDO::getThingUnit, PointFormulaModelVO::getThingUnit)
                .selectAs(PointDO::getPointCode, PointFormulaModelVO::getPointCode)
                .leftJoin(InstrumentModelDO.class, "model", on -> on.eq(FormulaPointDO::getInstrumentModelId, InstrumentModelDO::getId))
                .leftJoin(PointDO.class, "p", on -> on.eq(FormulaPointDO::getPointId, PointDO::getId))
                .eq(FormulaModelDO::getPointFormulaId, pointFormulaId)
                .orderByDesc(FormulaPointDO::getCreateTime));
        return pointFormulaModelVOS;
    }

    @Override
    public List<FormulaPointListVO> getFormulaPointList(Long pointFormulaId) {
        List<FormulaPointListVO> formulaPointListVOS = formulaPointMapper.selectJoinList(FormulaPointListVO.class, new MPJLambdaWrapperX<FormulaPointDO>()
                .selectAll(FormulaPointDO.class)
                .selectAs(PointDO::getPointName, FormulaPointListVO::getPointName)
                .selectAs(PointDO::getPointCode, FormulaPointListVO::getPointCode)
                .selectAs(InstrumentModelDO::getThingName, FormulaPointListVO::getThingName)
                .selectAs(InstrumentDO::getInstrumentName, FormulaPointListVO::getInstrumentName)
                .leftJoin(InstrumentModelDO.class, "a", on -> on.eq(FormulaPointDO::getInstrumentModelId, InstrumentModelDO::getId))
                .leftJoin(PointDO.class, "b", on -> on.eq(PointDO::getId, FormulaPointDO::getPointId))
                .leftJoin(InstrumentDO.class, "c", on -> on.eq(InstrumentDO::getId, InstrumentModelDO::getInstrumentId))
                .eq(FormulaPointDO::getPointFormulaId, pointFormulaId)
                .orderByDesc(FormulaPointDO::getCreateTime));
        return formulaPointListVOS;
    }

    @Override
    public List<FormulaPointDO> getFormulaPointByPointFormulaId(Long pointFormulaId) {
        return formulaPointMapper.selectList(new LambdaQueryWrapperX<FormulaPointDO>()
                .eq(FormulaPointDO::getPointFormulaId, pointFormulaId));
    }

}