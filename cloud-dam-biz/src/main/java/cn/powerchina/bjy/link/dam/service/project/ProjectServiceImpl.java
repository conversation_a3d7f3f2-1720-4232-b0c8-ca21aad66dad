package cn.powerchina.bjy.link.dam.service.project;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.cloud.framework.tenant.core.context.TenantContextHolder;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.project.bo.ProjectBO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategorySaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectuser.ProjectUserDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import cn.powerchina.bjy.link.dam.dal.mysql.project.ProjectMapper;
import cn.powerchina.bjy.link.dam.enums.CategoryTypeEnum;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.dam.enums.UserTypeEnum;
import cn.powerchina.bjy.link.dam.service.authdevice.AuthDeviceService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import cn.powerchina.bjy.link.dam.service.projectuser.ProjectUserService;
import cn.powerchina.bjy.link.dam.service.user.UserService;
import cn.powerchina.bjy.link.dam.util.CodeGenerator;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.PROJECT_NOT_EXISTS;

/**
 * 项目管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectServiceImpl implements ProjectService {

    @Resource
    private ProjectMapper projectMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Autowired
    @Lazy
    private AuthDeviceService authDeviceService;

    @Autowired
    private ProjectCategoryService projectCategoryService;

    @Autowired
    private PointService pointService;

    @Autowired
    private ProjectUserService projectUserService;

    @Autowired
    private RoleApi roleApi;

    @Override
    @Transactional
    public Long createProject(ProjectSaveReqVO createReqVO) {
        //校验项目名称是否存在
        validateProjectNameExists(createReqVO.getId(), createReqVO.getProjectName());
        //校验项目管理员是否存在
        validateProjectAdmin(createReqVO.getManagerUserId());
        // 插入项目表
        ProjectDO project = BeanUtils.toBean(createReqVO, ProjectDO.class);
        project.setId(snowFlakeUtil.snowflakeId());
        project.setProjectCode(getProjectCode());
        projectMapper.insert(project);
        //插入项目用户表
        projectUserService.saveProjectUser(project.getId(), project.getManagerUserId(), null);
        //插入监测站点
        projectCategoryService.createProjectCategory(ProjectCategorySaveReqVO.builder().projectId(project.getId())
                .parentId(null).categoryName(project.getProjectName()).categoryType(CategoryTypeEnum.STATION.getType()).build());
        //插入工程结构
        projectCategoryService.createProjectCategory(ProjectCategorySaveReqVO.builder().projectId(project.getId())
                .parentId(null).categoryName(project.getProjectName()).categoryType(CategoryTypeEnum.STRUCT.getType()).build());
        // 返回
        return project.getId();
    }

    /**
     * 获取项目编码
     *
     * @return
     */
    private String getProjectCode() {
        String projectCode = SceneTypeEnum.DAM_CODE.getPrefix() + CodeGenerator.createThreeCode();
        Long codeCount = projectMapper.selectCount(new LambdaQueryWrapperX<ProjectDO>().eq(ProjectDO::getProjectCode, projectCode));
        if (Objects.nonNull(codeCount) && codeCount.compareTo(0L) > 0) {
            getProjectCode();
        }
        return projectCode;
    }

    /**
     * 校验项目名称是否存在
     *
     * @param id
     * @param projectName
     */
    private void validateProjectNameExists(Long id, String projectName) {
        ProjectDO projectDO = projectMapper.selectOne(new LambdaQueryWrapperX<ProjectDO>().eq(ProjectDO::getProjectName, projectName));
        if (Objects.nonNull(projectDO) && (Objects.isNull(id) || !Objects.equals(id, projectDO.getId()))) {
            throw exception(ErrorCodeConstants.PROJECT_NAME_EXISTS);
        }
    }

    /**
     * 校验项目管理员是否存在
     *
     * @param managerUserId
     */
    private void validateProjectAdmin(Long managerUserId) {
        UserDO userDO = userService.getUser(managerUserId);
        if (Objects.isNull(userDO) || !Objects.equals(userDO.getUserType(), UserTypeEnum.PROJECT_ADMIN.getType())) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NOT_EXISTS);
        }
    }

    @Override
    @Transactional
    public void updateProject(ProjectSaveReqVO updateReqVO) {
        // 校验存在
        ProjectDO projectDOOrigin = validateProjectExists(updateReqVO.getId());
        //校验项目名称是否存在
        validateProjectNameExists(updateReqVO.getId(), updateReqVO.getProjectName());
        //校验项目管理员是否存在
        validateProjectAdmin(updateReqVO.getManagerUserId());
        // 更新
        ProjectDO updateObj = BeanUtils.toBean(updateReqVO, ProjectDO.class);
        projectMapper.updateById(updateObj);
        //更新项目用户关联
        if (!Objects.equals(projectDOOrigin.getManagerUserId(), updateObj.getManagerUserId())) {
            //插入项目用户表
            projectUserService.saveProjectUser(updateObj.getId(), updateObj.getManagerUserId(), projectDOOrigin.getManagerUserId());
        }
        if (!projectDOOrigin.getProjectName().equals(updateObj.getProjectName())) {
            //更新监测站点
            projectCategoryService.updateProjectCategoryName(projectDOOrigin.getProjectName(), updateReqVO.getProjectName(), CategoryTypeEnum.STATION.getType());
            //更新工程结构
            projectCategoryService.updateProjectCategoryName(projectDOOrigin.getProjectName(), updateReqVO.getProjectName(), CategoryTypeEnum.STRUCT.getType());
        }
    }

    @Override
    @Transactional
    public void deleteProject(Long id) {
        // 校验存在
        validateProjectExists(id);
        //当前工程下面有测点时，不允许删除
        if (pointService.countPointByProjectId(id, null, null, null, null) > 0) {
            throw exception(ErrorCodeConstants.PROJECT_INFO_POINT_EXISTS);
        }
        //删除设备授权的数据
        authDeviceService.deleteAuthDeviceByProjectId(id);
        //删除项目用户关系表，否则用户无法删除
        projectUserService.deleteProjectUserByProjectId(id);
        //其它的不删除，是因为别的菜单是根据项目走的，最上面项目不显示，就没有问题了
        projectMapper.deleteById(id);
    }

    @Override
    public ProjectDO validateProjectExists(Long id) {
        ProjectDO projectDO = projectMapper.selectById(id);
        if (Objects.isNull(projectDO)) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        return projectDO;
    }

    @Override
    public List<ProjectDO> getProjectList() {
        List<ProjectDO> projectDOList = projectMapper.selectList();
        //查询当前用户所属项目
        UserDO userDO = userService.getUser(WebFrameworkUtils.getLoginUserId());
        //如果是普通用户或者项目管理员，需要过滤
        if (Objects.nonNull(userDO) && (Objects.equals(userDO.getUserType(), UserTypeEnum.PROJECT_ADMIN.getType())
                || Objects.equals(userDO.getUserType(), UserTypeEnum.PROJECT_USER.getType()))) {
            List<ProjectUserDO> projectUserDOList = projectUserService.getProjectUserByUserId(userDO.getId());
            if (!CollectionUtils.isEmpty(projectUserDOList)) {
                List<Long> projectIds = projectUserDOList.stream().map(ProjectUserDO::getProjectId).toList();
                projectDOList = projectDOList.stream().filter(item -> projectIds.contains(item.getId())).toList();
            } else {
                projectDOList.clear();
            }
        }
        projectDOList = projectDOList.stream().sorted(Comparator.comparing(ProjectDO::getId).reversed()).toList();
        return projectDOList;
    }

    @Override
    public List<ProjectDO> getAllProjectList() {
        return projectMapper.selectList();
    }

    @Override
    public List<ProjectDO> getProjectListByUserId(Long userId) {
        return projectMapper.selectList(new LambdaQueryWrapperX<ProjectDO>()
                .eq(ProjectDO::getManagerUserId, userId));
    }

    @Override
    public ProjectDO getProject(Long id) {
        return projectMapper.selectById(id);
    }

    @Override
    public ProjectBO getProjectBO(Long id) {
        ProjectDO projectDO = getProject(id);
        if (Objects.nonNull(projectDO)) {
            ProjectBO projectBO = new ProjectBO();
            org.springframework.beans.BeanUtils.copyProperties(projectDO, projectBO);
            UserDO userDO = userService.getUser(projectBO.getManagerUserId());
            if (Objects.nonNull(userDO)) {
                projectBO.setManagerUserName(userDO.getName());
                projectBO.setManagerMobile(userDO.getMobile());
            }
            return projectBO;
        }
        return null;
    }

    @Override
    public PageResult<ProjectDO> getProjectPage(ProjectPageReqVO pageReqVO) {
        return projectMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<ProjectBO> getProjectBOPage(ProjectPageReqVO pageReqVO) {
        MPJLambdaWrapperX<ProjectDO> wrapper = (MPJLambdaWrapperX<ProjectDO>) new MPJLambdaWrapperX<ProjectDO>()
                .selectAll(ProjectDO.class)
                .selectAs(ProjectDO::getProjectName, ProjectBO::getProjectName)
                .selectAs(ProjectDO::getProvinceCode, ProjectBO::getProvinceCode)
                .selectAs(ProjectDO::getProvinceName, ProjectBO::getProvinceName)
                .selectAs(ProjectDO::getCityCode, ProjectBO::getCityCode)
                .selectAs(ProjectDO::getCityName, ProjectBO::getCityName)
                .selectAs(ProjectDO::getAreaCode, ProjectBO::getAreaCode)
                .selectAs(ProjectDO::getAreaName, ProjectBO::getAreaName)
                .selectAs(ProjectDO::getAddress, ProjectBO::getAddress)
                .selectAs(ProjectDO::getLongitude, ProjectBO::getLongitude)
                .selectAs(ProjectDO::getLatitude, ProjectBO::getLatitude)
                .selectAs(ProjectDO::getRemark, ProjectBO::getRemark)
                .selectAs(ProjectDO::getCreateTime, ProjectBO::getCreateTime)
                .selectAs(ProjectDO::getManagerUserId, ProjectBO::getManagerUserId)
                .selectAs(UserDO::getName, ProjectBO::getManagerUserName)
                .selectAs(UserDO::getMobile, ProjectBO::getManagerMobile)
                .likeIfPresent(ProjectDO::getProjectName, pageReqVO.getProjectName())
                .leftJoin(UserDO.class, "d", on -> on.eq(UserDO::getId, ProjectDO::getManagerUserId))
                .orderByDesc(ProjectDO::getId);
        return projectMapper.selectJoinPage(pageReqVO, ProjectBO.class, wrapper);
    }

}