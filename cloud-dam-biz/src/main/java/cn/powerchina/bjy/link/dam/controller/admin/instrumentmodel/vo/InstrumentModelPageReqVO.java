package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

@Schema(description = "管理后台 - 仪器类型-测量分量分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InstrumentModelPageReqVO extends PageParam {

    @Schema(description = "项目id")
    @NotNull(message = "请选择一个项目")
    private Long projectId;

    @Schema(description = "仪器类型id")
    @NotNull(message = "仪器类型id不能为空")
    private Long instrumentId;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "是否是计算公式分页")
    private Boolean calcFormula;

    @Schema(description = "分量类型集合：计算公式分页传参[2,3]", hidden = true)
    private List<Integer> thingTypeList = Arrays.asList(new Integer[]{2, 3});
}