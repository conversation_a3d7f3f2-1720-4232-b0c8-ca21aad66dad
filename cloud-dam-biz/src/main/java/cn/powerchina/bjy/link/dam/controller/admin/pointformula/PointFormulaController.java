package cn.powerchina.bjy.link.dam.controller.admin.pointformula;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategoryRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointformula.PointFormulaDO;
import cn.powerchina.bjy.link.dam.service.pointformula.PointFormulaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点计算公式")
@RestController
@RequestMapping("/dam/point/formula")
@Validated
public class PointFormulaController {

    @Resource
    private PointFormulaService pointFormulaService;

    @PutMapping("/update")
    @Operation(summary = "更新测点计算公式")
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:update')")
    public CommonResult<Boolean> updatePointFormula(@Valid @RequestBody PointFormulaSaveReqVO updateReqVO) {
        pointFormulaService.updatePointFormula(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测点计算公式")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:delete')")
    public CommonResult<Boolean> deletePointFormula(@RequestParam("id") Long id) {
        pointFormulaService.deletePointFormula(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测点计算公式")
    @Parameter(name = "id", description = "编号", example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:query')")
    public CommonResult<PointFormulaRespVO> getPointFormula(@RequestParam(value = "id", required = false) Long id) {
        PointFormulaDO pointFormula = pointFormulaService.getPointFormula(id);
        return success(BeanUtils.toBean(pointFormula, PointFormulaRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点计算公式分页")
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:query')")
    public CommonResult<PageResult<PointFormulaPageVO>> getPointFormulaPage(@Valid PointFormulaPageReqVO pageReqVO) {
        PageResult<PointFormulaPageVO> pageResult = pointFormulaService.getPointFormulaPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/thing")
    @Operation(summary = "类型为中间值和成果值的测量分量列表")
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:query')")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
    public CommonResult<List<InstrumentModelRespVO>> getThingList(Long categoryId) {
        List<InstrumentModelDO> instrumentModelDOList = pointFormulaService.getInstrumentModelByPointId(categoryId);
        return success(BeanUtils.toBean(instrumentModelDOList, InstrumentModelRespVO.class));
    }

    @GetMapping("/params")
    @Operation(summary = "参数列表")
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:query')")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
    public CommonResult<List<InstrumentParamRespVO>> getParamsList(Long categoryId) {
        List<InstrumentParamDO> instrumentParamDOList = pointFormulaService.getInstrumentParamsByPointId(categoryId);
        return success(BeanUtils.toBean(instrumentParamDOList, InstrumentParamRespVO.class));
    }

    @GetMapping("/model")
    @Operation(summary = "测点计算公式变量列表")
//    @PreAuthorize("@ss.hasPermission('dam:point-formula:query')")
    public CommonResult<List<PointFormulaModelVO>> getModelList(@Valid PointFormulaReqVO pointFormulaReqVO) {
        List<PointFormulaModelVO> formulaModelList = pointFormulaService.getFormulaModel(pointFormulaReqVO);
        return success(formulaModelList);
    }

    @PostMapping("/copy")
    @Operation(summary = "复制测点计算公式")
//    @PreAuthorize("@ss.hasPermission('dam:point:create')")
    public CommonResult<Boolean> copyPointFormula(@Valid @RequestBody PointFormulaCopyReqVO reqVO) {
        pointFormulaService.copyPointFormula(reqVO);
        return success(true);
    }

    @GetMapping("/getInstrument")
    @Operation(summary = "根据测点id获取仪器name")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:query')")
    public CommonResult<String> getInstrumentNameByPointId(@RequestParam("id") Long pointId) {
        String InstrumentName = pointFormulaService.getInstrumentNameByPointId(pointId);
        return success(InstrumentName);
    }

}