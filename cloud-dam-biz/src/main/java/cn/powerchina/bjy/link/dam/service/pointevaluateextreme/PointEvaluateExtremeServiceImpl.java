package cn.powerchina.bjy.link.dam.service.pointevaluateextreme;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluateextreme.PointEvaluateExtremeDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointevaluateextreme.PointEvaluateExtremeMapper;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.dam.service.pointevaluate.PointEvaluateService;
import cn.powerchina.bjy.link.dam.util.AviatorUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.POINT_EVALUATE_EXTREME_ERROR;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.POINT_EVALUATE_EXTREME_NOT_EXISTS;


/**
 * 测点评价指标极值 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PointEvaluateExtremeServiceImpl implements PointEvaluateExtremeService {

    @Resource
    private PointEvaluateExtremeMapper pointEvaluateExtremeMapper;

    @Resource
    private PointDataService pointDataService;

    @Resource
    private PointEvaluateService pointEvaluateService;

    @Override
    public String createPointEvaluateExtreme(PointEvaluateExtremeSaveReqVO createReqVO) {
        // 插入
        PointEvaluateExtremeReqVO pointEvaluateExtremeReqVO = createReqVO.getPointEvaluateExtremeReqVO();
        PointEvaluateSaveReqVO pointEvaluateSaveReqVO = createReqVO.getPointEvaluateSaveReqVO();
        PointEvaluateSaveReqVO saveReqVO = createReqVO.getPointEvaluateSaveReqVO();
        List<PointEvaluateExtremeBatchAppReqVO> batchAppReqVOList = createReqVO.getBatchAppReqVOList();
        PointEvaluateExtremeDO pointEvaluateExtreme = BeanUtils.toBean(pointEvaluateExtremeReqVO, PointEvaluateExtremeDO.class);
        if (null == pointEvaluateExtreme.getId()) {
            pointEvaluateExtremeMapper.insert(pointEvaluateExtreme);
        } else {
            // 校验存在
            validatePointEvaluateExtremeExists(pointEvaluateExtreme.getId());
//            pointEvaluateExtreme = BeanUtils.toBean(createReqVO, PointEvaluateExtremeDO.class);
            pointEvaluateExtremeMapper.updateById(pointEvaluateExtreme);
        }
        //保存测点评价指标
        Long id = pointEvaluateSaveReqVO.getId();
        pointEvaluateSaveReqVO.setEvaluateExtremeId(pointEvaluateExtreme.getId());
        if (null == pointEvaluateSaveReqVO.getId()) {
            id = pointEvaluateService.createPointEvaluate(pointEvaluateSaveReqVO);
        } else {
            pointEvaluateService.updatePointEvaluate(pointEvaluateSaveReqVO);
        }
        String message = null;
        if (null != batchAppReqVOList) {
            saveReqVO.setId(null);
            List<PointEvaluateSaveReqVO> saveReqVOList = new ArrayList<>();
            batchAppReqVOList.stream()
                    .filter(batch -> Objects.nonNull(batch.getPointId()) 
                            && !batch.getPointId().equals(pointEvaluateExtremeReqVO.getPointId())
                           ) // 过滤掉相同pointId的数据
                    .forEach(batch -> {
                saveReqVO.setPointId(batch.getPointId());
                saveReqVO.setProjectId(batch.getProjectId());
                saveReqVO.setPointCode(batch.getPointCode());
                pointEvaluateExtremeReqVO.setPointId(batch.getPointId());
                pointEvaluateExtremeReqVO.setProjectId(batch.getProjectId());
                pointEvaluateExtremeReqVO.setDataType(createReqVO.getPointEvaluateExtremeReqVO().getDataType());
                PointEvaluateExtremeResultVO calculating = calculating(pointEvaluateExtremeReqVO);

                PointEvaluateSaveReqVO evaluateSaveReqVO = new PointEvaluateSaveReqVO();
                BeanUtil.copyProperties(saveReqVO, evaluateSaveReqVO);
                BeanUtil.copyProperties(calculating, evaluateSaveReqVO);
                evaluateSaveReqVO.setEvaluateExtremeId(pointEvaluateExtreme.getId());
                saveReqVOList.add(evaluateSaveReqVO);
            });
            message = pointEvaluateService.resetEvaluateExtremeId(saveReqVOList, pointEvaluateExtreme.getId(), id);
        }
        // 返回
        return message;
    }

    /**
     * 测点时间段内数据为空，极值返回为null
     *
     * @param reqVO
     * @return
     */
    @Override
    public PointEvaluateExtremeResultVO calculating(PointEvaluateExtremeReqVO reqVO) {
        if (Objects.isNull(reqVO.getDataType()) || reqVO.getDataType() == 0) {
            reqVO.setDataType(null);
        }

        Integer dataType;
        Integer symbol;
        String rateValue;
        String value;
        PointEvaluateExtremeResultVO resultVO = new PointEvaluateExtremeResultVO();

        //异常最小值
        dataType = reqVO.getAbnormalDownType();
        symbol = reqVO.getAbnormalDownSymbol();
        rateValue = reqVO.getAbnormalDown();
        value = getExtremeValue(reqVO, dataType, symbol, rateValue);
        resultVO.setAbnormalDown(null != value ? value : null);

        dataType = reqVO.getAbnormalUpType();
        symbol = reqVO.getAbnormalUpSymbol();
        rateValue = reqVO.getAbnormalUp();
        value = getExtremeValue(reqVO, dataType, symbol, rateValue);
        resultVO.setAbnormalUp(null != value ? value : null);

        dataType = reqVO.getWaringUpType();
        symbol = reqVO.getWaringUpSymbol();
        rateValue = reqVO.getWaringUp();
        value = getExtremeValue(reqVO, dataType, symbol, rateValue);
        resultVO.setWaringUp(null != value ? value : null);

        dataType = reqVO.getWaringDownType();
        symbol = reqVO.getWaringDownSymbol();
        rateValue = reqVO.getWaringDown();
        value = getExtremeValue(reqVO, dataType, symbol, rateValue);
        resultVO.setWaringDown(null != value ? value : null);

        return resultVO;
    }

    /**
     * 获取极值结果
     *
     * @param reqVO
     * @param dataType
     * @param symbol
     * @param rateValue
     * @return
     */
    private String getExtremeValue(PointEvaluateExtremeReqVO reqVO, Integer dataType, Integer symbol, String rateValue) {
        if (reqVO.getExtremeType() == 3) {
            return new BigDecimal(rateValue) + "";
        }

        StringBuilder expression = new StringBuilder();
        boolean isMax = dataType != 1;
        PointDataDO extremaPointData = pointDataService.getExtremaPointData(reqVO.getPointId(), reqVO.getInstrumentModelId(), reqVO.getStartTime(), reqVO.getEndTime(), reqVO.getDataType(), isMax,reqVO.getProjectId());
        if (extremaPointData == null) {
            log.warn("测点 {} 时间段内的极值为空", reqVO.getPointId());
            return null;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("extremeValue", extremaPointData.getThingValue());
        params.put("rate", new BigDecimal(rateValue));

        expression.append("extremeValue");
        expression.append(symbol == 1 ? "+" : "-");
        switch (reqVO.getExtremeType()) {
            case 1:
                expression.append("extremeValue").append("*").append("rate/100");
                break;
            case 2:
                expression.append("rate");
                break;
            case 3:
                break;
            default:
                log.error("不支持该计算方式");
                throw exception(POINT_EVALUATE_EXTREME_ERROR);
        }
        Object result = AviatorUtils.execute(expression.toString(), params);
        String values = null;
        BigDecimal value = Objects.isNull(result) ? null : AviatorUtils.getBigDecimal(result).stripTrailingZeros();
        if (value != null) {
            if (value.toString().contains("E")) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                try {
                    Number number = numberFormat.parse(value.toString());
                    values = number.toString();
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            } else {
                values = value.toString();
            }
        }
        return values;
    }

    @Override
    public void updatePointEvaluateExtreme(PointEvaluateExtremeSaveReqVO updateReqVO) {
        // 校验存在
        // validatePointEvaluateExtremeExists(updateReqVO.getId());
        // 更新
        PointEvaluateExtremeDO updateObj = BeanUtils.toBean(updateReqVO, PointEvaluateExtremeDO.class);
        pointEvaluateExtremeMapper.updateById(updateObj);

        //保存测点评价指标
        pointEvaluateService.updatePointEvaluate(updateReqVO.getPointEvaluateSaveReqVO());
    }

    @Override
    public void deletePointEvaluateExtreme(Long id) {
        // 校验存在
        validatePointEvaluateExtremeExists(id);
        // 删除
        pointEvaluateExtremeMapper.deleteById(id);
    }

    private void validatePointEvaluateExtremeExists(Long id) {
        if (pointEvaluateExtremeMapper.selectById(id) == null) {
            throw exception(POINT_EVALUATE_EXTREME_NOT_EXISTS);
        }
    }

    @Override
    public PointEvaluateExtremeDO getPointEvaluateExtreme(Long id) {
        return pointEvaluateExtremeMapper.selectById(id);
    }

    @Override
    public PageResult<PointEvaluateExtremeDO> getPointEvaluateExtremePage(PointEvaluateExtremePageReqVO pageReqVO) {
        return pointEvaluateExtremeMapper.selectPage(pageReqVO);
    }

}