package cn.powerchina.bjy.link.dam.controller.admin.user.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserPageReqVO extends PageParam {

    @Schema(description = "用户账号", example = "赵六")
    private String username;

    @Schema(description = "姓名", example = "王五")
    private String name;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "用户类型:(1系统管理员，2项目管理员，3普通用户)", example = "2")
    private Integer userType;

    @Schema(description = "项目id", example = "26108")
    private Long projectId;

    @Schema(description = "用户id集合", hidden = true)
    private List<Long> userIdList;

}