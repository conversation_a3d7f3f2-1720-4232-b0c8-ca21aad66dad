package cn.powerchina.bjy.link.dam.service.pointdataimport;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointDataJsonSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointInstrumentModelJsonVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.enums.DamConstant;
import cn.powerchina.bjy.link.dam.enums.DataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.InstrumentThingTypeEnum;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * @Description: 描述
 * @Author: zhaoqiang
 * @CreateDate: 2024/9/30
 */
@Slf4j
public class ResultDataListener extends AnalysisEventListener<Map<Integer, String>> {

    //每100条数据入一次库
    private Integer batchCount;
    private List<PointDataJsonSaveReqVO> cachedDataList;
    private PointDataJsonService pointDataJsonService;

    PointDataImportSaveReqVO createReqVO;

    private ThreadPoolTaskExecutor damThreadPoolTaskExecutor;
    List<CompletableFuture<Void>> result = new ArrayList<>();

    public ResultDataListener(PointDataJsonService pointDataJsonService, PointDataImportSaveReqVO createReqVO, Integer batchCount, ThreadPoolTaskExecutor damThreadPoolTaskExecutor) {
        this.pointDataJsonService = pointDataJsonService;
        this.createReqVO = createReqVO;
        this.batchCount = null == batchCount ? 1000 : batchCount;
        this.cachedDataList = ListUtils.newArrayListWithExpectedSize(this.batchCount);
        this.damThreadPoolTaskExecutor = damThreadPoolTaskExecutor;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        log.debug("解析到一条数据:{}", JSON.toJSONString(data));
        LocalDateTime excelTime;
        PointDataJsonSaveReqVO dataJsonSaveReqVO = new PointDataJsonSaveReqVO();
        BeanUtil.copyProperties(createReqVO, dataJsonSaveReqVO, "id");
        try {
            excelTime = createReqVO.getTemplateType() != 2 ? LocalDateTime.parse(data.get(0), dateTimeFormatter) : LocalDateTime.parse(data.get(1), dateTimeFormatter);
        } catch (Exception e) {
            log.error("时间解析异常：{}", e.getMessage());
            e.printStackTrace();
            throw exception(POINT_DATA_IMPORT_TIME_FORMATTER);
        }
        //覆盖时间段的范围大于导入数据的时间
        if (DamConstant.IMPORT_TYPE_COVER.equals(createReqVO.getImportType())) {
            //开始日期和结束日期相同
            if (createReqVO.getStartTime().equals(createReqVO.getEndTime())) {
                createReqVO.setEndTime(createReqVO.getEndTime().plusDays(1));
            }
            if (!(excelTime.isAfter(createReqVO.getStartTime().atStartOfDay()) && excelTime.isBefore(createReqVO.getEndTime().atStartOfDay()))) {
                throw exception(POINT_DATA_IMPORT_TIME_CHECK);
            }
        } else {
            //追加：导入的监测数据时间，要大于数据库中已有的数据
            if (Objects.nonNull(createReqVO.getLastPointTime()) && !excelTime.isAfter(createReqVO.getLastPointTime())) {
                throw exception(POINT_DATA_IMPORT_TIME_ERROR, createReqVO.getLastPointTime().format(dateTimeFormatter));
            }
        }

        //测点id
        if (createReqVO.getTemplateType() == 2) {
            //根据测点编号，查询测点id
            if (!createReqVO.getPointCodeIdMap().containsKey(data.get(0))) {
                PointDO pointDO = pointDataJsonService.getPointByProjectIdAndPointCode(createReqVO.getProjectId(), data.get(0));
                //相同仪器类型下的测点数据才支持导入
                if (Objects.isNull(pointDO) || !pointDO.getInstrumentId().equals(dataJsonSaveReqVO.getInstrumentId())) {
                    throw exception(POINT_CODE_NOT_EXISTS, data.get(0));
                }
                createReqVO.getPointCodeIdMap().put(StringUtils.deleteWhitespace(data.get(0)), pointDO.getId());
                //覆盖导入：删除时间范围内的数据
                if (DamConstant.IMPORT_TYPE_COVER.equals(createReqVO.getImportType())) {
                    pointDataJsonService.updateByPointIdAndTime(pointDO.getId(), createReqVO.getStartTime().atStartOfDay(), createReqVO.getEndTime().atStartOfDay(), DataTypeEnum.MANUAL.getType(), true, createReqVO.getImportId());
                }
            }
            dataJsonSaveReqVO.setPointId(createReqVO.getPointCodeIdMap().get(StringUtils.deleteWhitespace(data.get(0))));
        }

        //采集时间
        dataJsonSaveReqVO.setDataType(DataTypeEnum.MANUAL.getType());
        //监测时间
        dataJsonSaveReqVO.setPointTime(excelTime);
        //excel和分量DO关系map
        Map<Integer, InstrumentModelDO> thingIdentityMap = createReqVO.getThingIdentityMap();
        //excel中取原始值
        List<PointInstrumentModelJsonVO> modelJsonVOS = new ArrayList<>();
        PointInstrumentModelJsonVO pointInstrumentModelJsonVO;
        for (Map.Entry<Integer, String> entry : data.entrySet()) {
            //跳过excel前一列
            if (createReqVO.getTemplateType() != 2) {
                if (entry.getKey() == 0) {
                    continue;
                }
            } else {
                if (entry.getKey() == 0 || entry.getKey() == 1) {
                    continue;
                }
            }
            InstrumentModelDO instrumentModelDO = thingIdentityMap.get(entry.getKey());
            if (Objects.isNull(instrumentModelDO)) {
                throw exception(POINT_DATA_IMPORT_DATA_TEMPLATE, entry.getKey() + 1);
            }
            pointInstrumentModelJsonVO = new PointInstrumentModelJsonVO();
            pointInstrumentModelJsonVO.setThingIdentity(instrumentModelDO.getThingIdentity());
            pointInstrumentModelJsonVO.setThingType(instrumentModelDO.getThingType());
            //excel中值可能为空
            if (Objects.nonNull(entry.getValue())) {
                pointInstrumentModelJsonVO.setThingValue(new BigDecimal(entry.getValue()));
            }
            pointInstrumentModelJsonVO.setInstrumentModelId(instrumentModelDO.getId());
            pointInstrumentModelJsonVO.setThingName(instrumentModelDO.getThingName());
            modelJsonVOS.add(pointInstrumentModelJsonVO);
        }
        dataJsonSaveReqVO.setPointData(JSONObject.toJSONString(modelJsonVOS));
        if (InstrumentThingTypeEnum.ORIGIN.getType().equals(createReqVO.getDataType())) {
            //计算成果值
            List<PointDataDO> resultPointDataList = pointDataJsonService.importCalculatePointFormula(dataJsonSaveReqVO);
            List<PointInstrumentModelJsonVO> resultModel = BeanUtils.toBean(resultPointDataList, PointInstrumentModelJsonVO.class);
            modelJsonVOS.addAll(resultModel);
            //组装原始值和结果值的pointData
            dataJsonSaveReqVO.setPointData(JSONObject.toJSONString(modelJsonVOS));
        }
        dataJsonSaveReqVO.setUserId(createReqVO.getUserId());
        cachedDataList.add(dataJsonSaveReqVO);
        if (cachedDataList.size() >= batchCount) {
            saveData();
            cachedDataList = ListUtils.newArrayListWithExpectedSize(batchCount);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
        CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
        log.info("所有数据解析完成！");
    }

    /**
     * 存储到数据库
     */
    private void saveData() {
        List<PointDataJsonSaveReqVO> newList = new ArrayList<>(cachedDataList);
        result.add(CompletableFuture.runAsync(() -> {
            log.info("{}条数据，开始存储数据库！", newList.size());
            pointDataJsonService.importPointDataJson(newList);
            log.info("存储数据库成功！");
        }, damThreadPoolTaskExecutor));
    }

    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("解析到表头数据:{}", JSON.toJSONString(headMap));
        Collection<String> values = headMap.values();
        List<String> thingIdentityNameList = createReqVO.getThingIdentityNameList();
        thingIdentityNameList.forEach(item->{
            if(!values.contains(item)){
                throw exception(POINT_DATA_IMPORT_MODEL_NOT_EXISTS, item);
            }
        });
    }

}
