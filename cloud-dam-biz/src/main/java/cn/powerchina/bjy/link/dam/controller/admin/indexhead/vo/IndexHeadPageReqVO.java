package cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 大坝首页头部信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndexHeadPageReqVO extends PageParam {

    @Schema(description = "项目id", example = "4887")
    private Long projectId;

    @Schema(description = "监测仪器数量")
    private Long pointDeviceTotal;

    @Schema(description = "监测仪器今日新增数量")
    private Long pointDeviceToday;

    @Schema(description = "监测数据数量")
    private Long pointDataTotal;

    @Schema(description = "监测数据今日新增数量")
    private Long pointDataToday;

    @Schema(description = "自动化设备数量")
    private Long deviceMcuTotal;

    @Schema(description = "自动化设备今日新增数量")
    private Long deviceMcuToday;

    @Schema(description = "在测自动化仪器数量")
    private Long deviceMcuRunTotal;

    @Schema(description = "在测自动化仪器今日新增数量")
    private Long deviceMcuRunToday;

    @Schema(description = "自动化监测数据数量")
    private Long pointDataMcuTotal;

    @Schema(description = "自动化监测数据今日新增数量")
    private Long pointDataMcuToday;

    @Schema(description = "生成时间")
    private String generateTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
