package cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 仪器类型-测量分量 DO
 *
 * <AUTHOR>
 */
@TableName("dam_instrument_model")
@KeySequence("dam_instrument_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstrumentModelDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 分量名称
     */
    private String thingName;
    /**
     * 标识符
     */
    private String thingIdentity;
    /**
     * 单位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String thingUnit;
    /**
     * 数据类型，1：整数型，2：浮点型，3：双精度
     */
    private Integer dataType;
    /**
     * 下限
     */
    private String downLimit;
    /**
     * 上限
     */
    private String upLimit;
    /**
     * 小数位
     */
    private Integer decimalLimit;
    /**
     * 分量类型，1：原始值，2：中间值，3：成果值
     */
    private Integer thingType;
    /**
     * 权重，数字越小越靠前
     */
    private Integer thingWeight;
    /**
     * iot属性标识符
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String thingIdentityIot;
    /**
     * 计算公式
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String calcFormula;
    /**
     * 新增时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime createTime;
    /**
     * 公式有效开始时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime effectiveStartTime;
    /**
     * 公式有效结束时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime effectiveEndTime;

}