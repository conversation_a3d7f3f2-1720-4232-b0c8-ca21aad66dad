package cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点评价指标新增/修改 Request VO")
@Data
public class PointEvaluateSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "测点id")
    @NotNull(message = "请选择测点")
    private Long pointId;

    @Schema(description = "分量id")
    @NotNull(message = "请输入分量id")
    private Long instrumentModelId;

    @Schema(description = "正常最大值")
    @NotNull(message = "请输入正常最大值")
    private String waringUp;

    @Schema(description = "正常最小值")
    @NotNull(message = "请输入正常最小值")
    private String waringDown;

    @Schema(description = "异常最大值")
    @NotNull(message = "请输入异常最大值")
    private String abnormalUp;

    @Schema(description = "异常最小值")
    @NotNull(message = "请输入异常最小值")
    private String abnormalDown;

    @Schema(description = "有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工")
    @NotNull(message = "请选择适用类型")
    private Integer applyType;

    @Schema(description = "极值公式id")
    private Long evaluateExtremeId;

    @Schema(description = "测点编号")
    private String pointCode;

}