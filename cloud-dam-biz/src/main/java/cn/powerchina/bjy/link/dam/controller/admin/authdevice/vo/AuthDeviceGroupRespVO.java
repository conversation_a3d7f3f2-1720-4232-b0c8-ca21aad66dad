package cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 设备授权 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AuthDeviceGroupRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "资源空间id")
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "父节点id")
    @ExcelProperty("父节点id")
    private Long parentId;

    @Schema(description = "设备分组名称")
    @ExcelProperty("设备分组名称")
    private String groupName;

}