package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 测点评价指标极值分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointEvaluateExtremePageReqVO extends PageParam {

    @Schema(description = "项目id", example = "27258")
    private Long projectId;

    @Schema(description = "测点id", example = "31317")
    private Long pointId;

    @Schema(description = "分量id", example = "28174")
    private Long instrumentModelId;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "结束时间（小于等于当前时间）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    @Schema(description = "采集类型(0：全部，1：自动化采集，2：人工录入）", example = "2")
    private Integer dataType;

    @Schema(description = "最值法(1-极值百分比浮动，2-极值固定值浮动，3-固定数值)", example = "2")
    private Integer extremeType;

    @Schema(description = "异常最小值(1-极小值，2-极大值)", example = "1")
    private Integer abnormalDownType;

    @Schema(description = "异常最小值(1-加，2-减)")
    private Integer abnormalDownSymbol;

    @Schema(description = "异常最小值")
    private String abnormalDown;

    @Schema(description = "正常最小值(1-极小值，2-极大值)", example = "1")
    private Integer waringDownType;

    @Schema(description = "正常最小值(1-加，2-减)")
    private Integer waringDownSymbol;

    @Schema(description = "正常最小值")
    private String waringDown;

    @Schema(description = "异常最大值(1-极小值，2-极大值)", example = "2")
    private Integer abnormalUpType;

    @Schema(description = "异常最大值(1-加，2-减)")
    private Integer abnormalUpSymbol;

    @Schema(description = "异常最大值")
    private String abnormalUp;

    @Schema(description = "正常最大值(1-极小值，2-极大值)", example = "1")
    private Integer waringUpType;

    @Schema(description = "正常最大值(1-加，2-减)")
    private Integer waringUpSymbol;

    @Schema(description = "异常最小值")
    private String waringUp;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}