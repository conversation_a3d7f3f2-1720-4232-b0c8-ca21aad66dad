package cn.powerchina.bjy.link.dam.service.projectuser;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.LoginReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectuser.ProjectUserDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 项目用户 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectUserService {

    /**
     * 创建项目用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectUser(@Valid ProjectUserSaveReqVO createReqVO);

    /**
     * 保存项目角色关系数据
     *
     * @param projectId
     * @param userId
     * @param userIdOrigin
     */
    void saveProjectUser(Long projectId, Long userId, Long userIdOrigin);

    /**
     * 更新项目用户
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectUser(@Valid ProjectUserSaveReqVO updateReqVO);

    /**
     * 删除项目用户
     *
     * @param id 编号
     */
    void deleteProjectUser(Long id);

    /**
     * 获得项目用户
     *
     * @param id 编号
     * @return 项目用户
     */
    UserBO getProjectUser(Long id, Long projectId);

    /**
     * 获得项目用户分页
     *
     * @param pageReqVO 分页查询
     * @return 项目用户分页
     */
    PageResult<UserBO> getProjectUserPage(ProjectUserPageReqVO pageReqVO);

    /**
     * 查询用户所属项目
     *
     * @param userId
     * @return
     */
    List<ProjectUserDO> getProjectUserByUserId(Long userId);

    /**
     * 删除项目的用户关系
     *
     * @param projectId
     */
    void deleteProjectUserByProjectId(Long projectId);

    boolean login(LoginReqVO loginReqVO);
}