package cn.powerchina.bjy.link.dam.service.projectmenu;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.tenant.core.context.TenantContextHolder;
import cn.powerchina.bjy.cloud.system.api.menu.MenuApi;
import cn.powerchina.bjy.cloud.system.api.menu.dto.MenuRespDTO;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleSaveReqDTO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.bo.ProjectMenuBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.RoleMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectmenu.ProjectMenuDO;
import cn.powerchina.bjy.link.dam.dal.mysql.projectmenu.ProjectMenuMapper;
import cn.powerchina.bjy.link.dam.enums.DamConstant;
import cn.powerchina.bjy.link.dam.enums.RoleTypeEnum;
import cn.powerchina.bjy.link.dam.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.dam.enums.UserRoleTypeEnum;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.dam.service.projectrole.ProjectRoleService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目菜单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectMenuServiceImpl implements ProjectMenuService {

    @Resource
    private ProjectMenuMapper projectMenuMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private MenuApi menuApi;

    @Autowired
    private RoleApi roleApi;

    @Autowired
    private PermissionApi permissionApi;

    @Autowired
    private ProjectRoleService projectRoleService;

    @Override
    @Transactional
    public Long saveProjectMenu(ProjectMenuSaveReqVO createReqVO) {
        //校验项目id是否存在
        ProjectDO projectDO = projectService.validateProjectExists(createReqVO.getProjectId());
        //删除旧的项目菜单
        projectMenuMapper.delete(new LambdaQueryWrapperX<ProjectMenuDO>().eq(ProjectMenuDO::getProjectId, createReqVO.getProjectId()));
        // 插入
        List<ProjectMenuDO> menuDOList = new ArrayList<>();
        for (Long menuId : createReqVO.getMenuIds()) {
            ProjectMenuDO menuDO = new ProjectMenuDO();
            menuDO.setMenuId(menuId);
            menuDO.setProjectId(createReqVO.getProjectId());
            menuDOList.add(menuDO);
        }
        projectMenuMapper.insertBatch(menuDOList);

        Long roleId = null;
        //项目特有的管理员角色，项目code作为角色code
        Map<String, RoleRespDTO> stringRoleRespDTOMap = roleApi.loadByCodes(Collections.singletonList(projectDO.getProjectCode()));
        if (Objects.nonNull(stringRoleRespDTOMap) && Objects.nonNull(stringRoleRespDTOMap.get(projectDO.getProjectCode()))) {
            roleId = stringRoleRespDTOMap.get(projectDO.getProjectCode()).getId();
        } else {
            RoleSaveReqDTO roleSaveReqDTO = new RoleSaveReqDTO();
            roleSaveReqDTO.setTenantId(TenantContextHolder.getTenantId());
            roleSaveReqDTO.setCode(projectDO.getProjectCode());
            //项目重建的管理员，角色名称相同，code和id不同
            roleSaveReqDTO.setName(RoleTypeEnum.PROJECT_ADMIN.getDesc() + "-" + projectDO.getProjectCode().replace(SceneTypeEnum.DAM_CODE.getPrefix(), ""));
            roleSaveReqDTO.setSort(DamConstant.PROJECT_ROLE_SORT);
            roleId = roleApi.createRole(roleSaveReqDTO).getCheckedData();
        }
        Set<Long> userIds = new HashSet<>();
        userIds.add(projectDO.getManagerUserId());
        //插入用户角色关系表
        permissionApi.assignUserRole(userIds, Collections.singleton(roleId), false);
        //分配的菜单权限给新建的项目管理员角色
        RoleMenuSaveReqVO roleMenuSaveReqVO = new RoleMenuSaveReqVO();
        roleMenuSaveReqVO.setRoleId(roleId);
        roleMenuSaveReqVO.setMenuIds(new HashSet<>(createReqVO.getMenuIds()));
        projectRoleService.saveRoleMenu(roleMenuSaveReqVO);
        // 返回
        return createReqVO.getProjectId();
    }

    @Override
    public ProjectMenuDO getProjectMenu(Long id) {
        return projectMenuMapper.selectById(id);
    }

    @Override
    public PageResult<ProjectMenuDO> getProjectMenuPage(ProjectMenuPageReqVO pageReqVO) {
        return projectMenuMapper.selectPage(pageReqVO);
    }

    @Override
    public ProjectMenuBO getProjectMenuBOByProjectId(Long projectId, Long roleId) {
        //项目管理员默认分配的菜单
        CommonResult<List<MenuRespDTO>> projectMenuResult = menuApi.roleCodeMenus(RoleTypeEnum.PROJECT_ADMIN.getCode());
        //给项目分配的菜单
        List<Long> projectMenuIdList = new ArrayList<>();
        List<ProjectMenuDO> menuDOList = projectMenuMapper.selectList(new LambdaQueryWrapperX<ProjectMenuDO>().eq(ProjectMenuDO::getProjectId, projectId));
        if (!CollectionUtils.isEmpty(menuDOList)) {
            projectMenuIdList = menuDOList.stream().map(ProjectMenuDO::getMenuId).collect(Collectors.toList());
        }
        ProjectMenuBO projectMenuBO = new ProjectMenuBO();
        projectMenuBO.setProjectId(projectId);
        //没用roleId，给项目分配菜单
        if (Objects.isNull(roleId) || roleId.compareTo(0L) <= 0) {
            projectMenuBO.setMenuIds(projectMenuIdList);
            if (Objects.nonNull(projectMenuResult)) {
                projectMenuBO.setMenuList(BeanUtils.toBean(projectMenuResult.getData(), ProjectMenuBO.MenuBO.class));
            }
        } else {
            //查找当前角色分配了哪些菜单
            CommonResult<List<MenuRespDTO>> result = menuApi.roleIdMenus(roleId);
            if (Objects.nonNull(result)) {
                projectMenuBO.setMenuIds(result.getData().stream().map(MenuRespDTO::getId).collect(Collectors.toList()));
            }
            //从项目管理员默认分配的菜单中过滤给项目分配的菜单
            List<Long> finalProjectMenuIdList = projectMenuIdList;
            List<MenuRespDTO> projectMenuList = projectMenuResult.getData().stream().filter(item -> finalProjectMenuIdList.contains(item.getId())).toList();
            projectMenuBO.setMenuList(BeanUtils.toBean(projectMenuList, ProjectMenuBO.MenuBO.class));
        }
        return projectMenuBO;
    }

}