package cn.powerchina.bjy.link.dam.service.mqtt.factory;

import cn.powerchina.bjy.link.dam.service.mqtt.processor.MsgProcessor;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 消息处理器工厂
 */
@Component
public class MsgProcessorFactory {

    @Autowired
    private List<MsgProcessor> msgProcessorList;

    private final Map<String, Map<String, MsgProcessor>> processorMap = new HashMap<>();

    @PostConstruct
    public void init(){
        processorMap.putAll(
            msgProcessorList.stream().collect(Collectors.groupingBy(
                    MsgProcessor::getTopic,
                    Collectors.toMap(
                            MsgProcessor::getDataType,
                            Function.identity(),
                            (existing, replacement) -> replacement)
                    )
            )
        );
    }

    public MsgProcessor getProcessor(String topic, String dataType) {
        return processorMap.getOrDefault(topic, new HashMap<>()).get(dataType);
    }
}
