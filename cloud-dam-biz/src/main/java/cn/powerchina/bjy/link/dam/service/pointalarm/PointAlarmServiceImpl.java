package cn.powerchina.bjy.link.dam.service.pointalarm;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmUpdateReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointalarm.PointAlarmDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointalarm.PointAlarmMapper;
import cn.powerchina.bjy.link.dam.enums.PointAlarmEnum;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.POINT_ALARM_NOT_EXISTS;

/**
 * 测点报警信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointAlarmServiceImpl implements PointAlarmService {

    @Resource
    private PointAlarmMapper pointAlarmMapper;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createPointAlarm(PointAlarmSaveReqVO createReqVO) {
        // 插入
        PointAlarmDO pointAlarm = BeanUtils.toBean(createReqVO, PointAlarmDO.class);
        //设置分量
        pointAlarm.setThingIdentity(instrumentModelService.getInstrumentModel(pointAlarm.getInstrumentModelId()).getThingIdentity());
        pointAlarm.setThingName(instrumentModelService.getInstrumentModel(pointAlarm.getInstrumentModelId()).getThingName());
        pointAlarm.setSolutionStatus(PointAlarmEnum.PENDING.getType());
        pointAlarmMapper.insert(pointAlarm);
        // 返回
        return pointAlarm.getId();
    }

    @Override
    public void updatePointAlarm(PointAlarmUpdateReqVO updateReqVO) {
        // 校验存在
        validatePointAlarmExists(updateReqVO.getId());
        // 更新
        PointAlarmDO updateObj = BeanUtils.toBean(updateReqVO, PointAlarmDO.class);
        //更新处理人信息
        updateObj.setSolutionUserName(adminUserApi.getUser(WebFrameworkUtils.getLoginUserId()).getData().getName());
        updateObj.setSolutionUserId(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        updateObj.setSolutionStatus(PointAlarmEnum.SOLVED.getType());
        pointAlarmMapper.updateById(updateObj);
    }

    @Override
    public void deletePointAlarm(Long id) {
        // 校验存在
        validatePointAlarmExists(id);
        // 删除
        pointAlarmMapper.deleteById(id);
    }

    private void validatePointAlarmExists(Long id) {
        if (pointAlarmMapper.selectById(id) == null) {
            throw exception(POINT_ALARM_NOT_EXISTS);
        }
    }

    @Override
    public PointAlarmDO getPointAlarm(Long id) {
        return pointAlarmMapper.selectById(id);
    }

    @Override
    public PageResult<PointAlarmPageRespVO> getPointAlarmPage(PointAlarmPageReqVO pageReqVO) {
        if (Optional.ofNullable(pageReqVO.getAlarmTime()).isPresent()) {
            LocalDate[] newDates = new LocalDate[2];
            newDates[0] = pageReqVO.getAlarmTime()[0];
            newDates[1] = pageReqVO.getAlarmTime()[1].plusDays(1);
            pageReqVO.setAlarmTime(newDates);
            return pointAlarmMapper.selectPage(pageReqVO);
        }
        return pointAlarmMapper.selectPage(pageReqVO);
    }

}