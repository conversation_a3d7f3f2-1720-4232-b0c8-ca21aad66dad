package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 仪器类型模板-测量分量分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InstrumentModelTemplatePageReqVO extends PageParam {

    @Schema(description = "仪器类型模板id", example = "8059")
    private Long instrumentId;

    @Schema(description = "分量名称", example = "赵六")
    private String thingName;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    private String thingUnit;

    @Schema(description = "数据类型，1：整数型，2：浮点型，3：双精度", example = "1")
    private Integer dataType;

    @Schema(description = "下限")
    private String downLimit;

    @Schema(description = "上限")
    private String upLimit;

    @Schema(description = "小数位")
    private Integer decimalLimit;

    @Schema(description = "分量类型，1：原始值，2：中间值，3：成果值", example = "2")
    private Integer thingType;

    @Schema(description = "权重，数字越小越靠前")
    private Integer thingWeight;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}