package cn.powerchina.bjy.link.dam.service.projectrole;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.tenant.core.context.TenantContextHolder;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.PermissionAssignRoleMenuDTO;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleAndUserRespDTO;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleSaveReqDTO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRolePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRoleRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRoleSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.RoleMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectrole.ProjectRoleDO;
import cn.powerchina.bjy.link.dam.dal.mysql.projectrole.ProjectRoleMapper;
import cn.powerchina.bjy.link.dam.enums.DamConstant;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.dam.util.CodeGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.PROJECT_ROLE_NOT_EXISTS;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.PROJECT_ROLE_USER_EXISTS;

/**
 * 项目角色信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProjectRoleServiceImpl implements ProjectRoleService {

    @Resource
    private ProjectRoleMapper projectRoleMapper;

    @Resource
    private RoleApi roleApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private ProjectService projectService;

    @Override
    @Transactional
    public Long createProjectRole(ProjectRoleSaveReqVO createReqVO) {
        //校验项目id合法
        ProjectDO projectDO = projectService.validateProjectExists(createReqVO.getProjectId());
        //校验角色名称是否存在
        validateProjectRoleNameExists(createReqVO.getProjectId(), createReqVO.getId(), createReqVO.getRoleName());
        RoleSaveReqDTO roleSaveReqDTO = new RoleSaveReqDTO();
        roleSaveReqDTO.setTenantId(TenantContextHolder.getTenantId());
        roleSaveReqDTO.setCode(getProjectRoleCode());
        roleSaveReqDTO.setName(createReqVO.getRoleName() + "-" + projectDO.getProjectCode().replace(SceneTypeEnum.DAM_CODE.getPrefix(), ""));
        roleSaveReqDTO.setSort(DamConstant.PROJECT_ROLE_SORT);
        roleSaveReqDTO.setRemark(createReqVO.getRemark());
        Long roleId = roleApi.createRole(roleSaveReqDTO).getCheckedData();

        // 插入
        ProjectRoleDO projectRole = BeanUtils.toBean(createReqVO, ProjectRoleDO.class);
        projectRole.setId(roleId);
        projectRole.setRoleCode(roleSaveReqDTO.getCode());
        projectRoleMapper.insert(projectRole);
        // 返回
        return projectRole.getId();
    }

    /**
     * 获取项目角色code
     *
     * @return
     */
    private String getProjectRoleCode() {
        String roleCode = SceneTypeEnum.DAM_CODE.getPrefix() + CodeGenerator.createThreeCode();
        Long codeCount = projectRoleMapper.selectCount(new LambdaQueryWrapperX<ProjectRoleDO>().eq(ProjectRoleDO::getRoleCode, roleCode));
        if (Objects.nonNull(codeCount) && codeCount.compareTo(0L) > 0) {
            getProjectRoleCode();
        }
        return roleCode;
    }

    /**
     * 校验角色名称是否存在
     *
     * @param projectId
     * @param id
     * @param roleName
     */
    private void validateProjectRoleNameExists(Long projectId, Long id, String roleName) {
        ProjectRoleDO roleDO = getProjectRoleByRoleName(projectId, roleName);
        if (Objects.nonNull(roleDO) && (Objects.isNull(id) || !Objects.equals(id, roleDO.getId()))) {
            throw exception(ErrorCodeConstants.PROJECT_ROLE_NAME_EXISTS);
        }
    }

    @Override
    @Transactional
    public void updateProjectRole(ProjectRoleSaveReqVO updateReqVO) {
        ProjectDO projectDO = projectService.validateProjectExists(updateReqVO.getProjectId());
        // 校验存在
        ProjectRoleDO projectRoleDO = validateProjectRoleExists(updateReqVO.getId());
        //校验角色名称是否存在
        validateProjectRoleNameExists(updateReqVO.getProjectId(), updateReqVO.getId(), updateReqVO.getRoleName());
        RoleSaveReqDTO roleSaveReqDTO = new RoleSaveReqDTO();
        roleSaveReqDTO.setId(updateReqVO.getId());
        roleSaveReqDTO.setName(updateReqVO.getRoleName() + "-" + projectDO.getProjectCode().replace(SceneTypeEnum.DAM_CODE.getPrefix(), ""));
        roleSaveReqDTO.setRemark(updateReqVO.getRemark());
        roleSaveReqDTO.setTenantId(TenantContextHolder.getTenantId());
        roleSaveReqDTO.setSort(DamConstant.PROJECT_ROLE_SORT);
        roleSaveReqDTO.setCode(projectRoleDO.getRoleCode());
        roleApi.updateRole(roleSaveReqDTO);
        // 更新
        projectRoleMapper.updateById(BeanUtils.toBean(updateReqVO, ProjectRoleDO.class));
    }

    @Override
    @Transactional
    public void deleteProjectRole(Long id) {
        // 校验存在
        validateProjectRoleExists(id);
        validateRoleUser(id);
        roleApi.deleteRole(id, TenantContextHolder.getTenantId());
        // 删除
        projectRoleMapper.deleteById(id);
    }


    private void validateRoleUser(Long roleId) {
        CommonResult<Map<Long, List<RoleAndUserRespDTO>>> mapCommonResult = roleApi.roleUsers(List.of(roleId), TenantContextHolder.getTenantId());
        List<RoleAndUserRespDTO> roleRespDTOS = mapCommonResult.getData().get(roleId);
        if (!CollectionUtils.isEmpty(roleRespDTOS)) {
            throw exception(PROJECT_ROLE_USER_EXISTS);
        }
    }

    private ProjectRoleDO validateProjectRoleExists(Long id) {
        ProjectRoleDO projectRoleDO = projectRoleMapper.selectById(id);
        if (Objects.isNull(projectRoleDO)) {
            throw exception(PROJECT_ROLE_NOT_EXISTS);
        }
        return projectRoleDO;
    }

    @Override
    public ProjectRoleDO getProjectRole(Long id) {
        return projectRoleMapper.selectById(id);
    }

    @Override
    public PageResult<ProjectRoleRespVO> getProjectRolePage(ProjectRolePageReqVO pageReqVO) {
        PageResult<ProjectRoleDO> projectRoleDOPageResult = projectRoleMapper.selectPage(pageReqVO);
        //查找角色id对应的用户数量
        List<Long> roleIds = projectRoleDOPageResult.getList().stream().map(ProjectRoleDO::getId).toList();
        List<ProjectRoleRespVO> roleRespVOList = new ArrayList<>();
        CommonResult<Map<Long, List<RoleAndUserRespDTO>>> mapCommonResult = roleApi.roleUsers(roleIds, TenantContextHolder.getTenantId());
        if (!CollectionUtils.isEmpty(projectRoleDOPageResult.getList())) {
            projectRoleDOPageResult.getList().forEach(item -> {
                ProjectRoleRespVO projectRoleRespVO = new ProjectRoleRespVO();
                BeanUtil.copyProperties(item, projectRoleRespVO);
                List<RoleAndUserRespDTO> roleRespDTOS = mapCommonResult.getCheckedData().get(item.getId());
                if (!CollectionUtils.isEmpty(roleRespDTOS)) {
                    projectRoleRespVO.setUserCount(roleRespDTOS.size());
                }
                roleRespVOList.add(projectRoleRespVO);
            });
        }
        return new PageResult<>(roleRespVOList, projectRoleDOPageResult.getTotal());
    }

    @Override
    @Transactional
    public Long saveRoleMenu(RoleMenuSaveReqVO roleMenuSaveReqVO) {
        PermissionAssignRoleMenuDTO permissionAssignRoleMenuDTO = new PermissionAssignRoleMenuDTO();
        BeanUtil.copyProperties(roleMenuSaveReqVO, permissionAssignRoleMenuDTO);
        permissionApi.assignRoleMenu(permissionAssignRoleMenuDTO);
        return roleMenuSaveReqVO.getRoleId();
    }

    @Override
    public ProjectRoleDO getProjectRoleByRoleName(Long projectId, String roleName) {
        return projectRoleMapper.selectOne(new LambdaQueryWrapperX<ProjectRoleDO>().eq(ProjectRoleDO::getProjectId, projectId)
                .eq(ProjectRoleDO::getRoleName, roleName).last("limit 1"));
    }

}