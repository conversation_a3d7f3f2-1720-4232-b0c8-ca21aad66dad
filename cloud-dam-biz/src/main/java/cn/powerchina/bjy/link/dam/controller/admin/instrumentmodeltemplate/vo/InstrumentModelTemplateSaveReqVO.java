package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 仪器类型模板-测量分量新增/修改 Request VO")
@Data
public class InstrumentModelTemplateSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25238")
    private Long id;

    @Schema(description = "仪器类型模板id", example = "8059")
    private Long instrumentId;

    @Schema(description = "分量名称", example = "赵六")
    @Length(max = 32)
    private String thingName;

    @Schema(description = "分量标识符")
    @Length(max = 32)
    @NotNull(message = "标识符不能为空")
    private String thingIdentity;

    @Schema(description = "单位")
    @Length(max = 32)
    @NotNull(message = "单位不能为空")
    private String thingUnit;

    @Schema(description = "数据类型，1：整数型，2：浮点型，3：双精度", example = "1")
    private Integer dataType;

    @Schema(description = "下限")
    private String downLimit;

    @Schema(description = "上限")
    private String upLimit;

    @Schema(description = "小数位")
    @Min(0)
    @Max(15)
    private Integer decimalLimit;

    @Schema(description = "分量类型，1：原始值，2：中间值，3：成果值", example = "2")
    private Integer thingType;

    @Schema(description = "权重，数字越小越靠前")
    @Min(0)
    private Integer thingWeight;

}