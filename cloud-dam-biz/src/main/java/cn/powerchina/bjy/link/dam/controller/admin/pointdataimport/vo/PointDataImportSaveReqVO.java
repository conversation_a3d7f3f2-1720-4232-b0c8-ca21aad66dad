package cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo;

import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 测点数据导入新增/修改 Request VO")
@Data
public class PointDataImportSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "数据类型，1：原始值，2：中间值，3：成果值")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "导入类型，1：追加导入，2：覆盖导入")
    @NotNull(message = "导入类型不能为空")
    private Integer importType;

    @Schema(description = "覆盖开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startTime;

    @Schema(description = "覆盖结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endTime;

    @Schema(description = "excel文件地址")
    private String filePath;

    //key=excel列顺序，value=InstrumentModelDO
    @Schema(hidden = true)
    private Map<Integer, InstrumentModelDO> thingIdentityMap = new HashMap<>();

    @Schema(hidden = true)
    private Long instrumentId;

    //多测点code和id集合
    @Schema(hidden = true)
    private Map<String, Long> pointCodeIdMap = new HashMap<>();

    @Schema(description = "数据库中最新一条数据监测时间", hidden = true)
    private LocalDateTime lastPointTime;

    @Schema(description = "导入方式，1：导入，2：多测点导入")
    private Integer templateType = 1;

    @Schema(hidden = true, description = "登录人id")
    private String userId;

    @Schema(hidden = true, description = "导入id")
    private Long importId;

    @Schema(hidden = true, description = "分量名称+单位 集合")
    private List<String> thingIdentityNameList = new ArrayList<>();

}