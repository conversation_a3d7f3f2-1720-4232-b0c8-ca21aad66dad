package cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 仪器类型-计算参数 DO
 *
 * <AUTHOR>
 */
@TableName("dam_instrument_param")
@KeySequence("dam_instrument_param_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstrumentParamDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 参数名称
     */
    private String thingName;
    /**
     * 参数标识符
     */
    private String thingIdentity;
    /**
     * 单位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String thingUnit;
    /**
     * 小数位
     */
    private Integer decimalLimit;
    /**
     * 权重
     */
    private Integer thingWeight;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;;

}