package cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 设备授权 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AuthDeviceRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9460")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "资源空间id", example = "25287")
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "项目id", example = "15413")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "设备分组id", example = "27690")
    @ExcelProperty("设备分组id")
    private Long deviceGroupId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}