package cn.powerchina.bjy.link.dam.dal.dataobject.iotproductmodel;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 物联网平台同步的产品物模型 DO
 *
 * <AUTHOR>
 */
@TableName("dam_iot_product_model")
@KeySequence("dam_iot_product_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotProductModelDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 物联网平台库的产品物模型id
     */
    private Long iotId;

    /**
     * 物联网平台库的产品id
     */
    private Long iotProductIotId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 物模型标识符
     */
    private String thingIdentity;

    /**
     * 物模型名称
     */
    private String thingName;

    /**
     * 物模型类型
     * 1:属性; 2:服务; 3:事件
     */
    private Integer thingType;

    /**
     * 数据类型
     * 取值范围:integer、decimal、string、bool、array、enum
     */
    private String datatype;

    /**
     * 读写类型
     * 1:读写; 2:只读; 3:只写
     * thingType为1时必填
     */
    private Integer readWriteType;

    /**
     * 事件类型
     * 1:信息; 2:告警; 3:故障
     * thingType为3时必填
     */
    private Integer eventType;

    /**
     * 输入参数
     */
    private String inputParams;

    /**
     * 输出参数
     */
    private String outputParams;

    /**
     * 属性扩展信息
     */
    private String extra;

    /**
     * 描述
     */
    private String remark;

    /**
     * 物联网平台库的产品物模型创建时间
     */
    private LocalDateTime iotCreateTime;
}
