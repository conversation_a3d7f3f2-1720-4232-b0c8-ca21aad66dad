package cn.powerchina.bjy.link.dam.dal.mysql.projectcategory;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategoryPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory.ProjectCategoryDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 工程分类管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectCategoryMapper extends BaseMapperX<ProjectCategoryDO> {

    default PageResult<ProjectCategoryDO> selectPage(ProjectCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eqIfPresent(ProjectCategoryDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectCategoryDO::getParentId, reqVO.getParentId())
                .likeIfPresent(ProjectCategoryDO::getCategoryName, reqVO.getCategoryName())
                .eqIfPresent(ProjectCategoryDO::getCategoryType, reqVO.getCategoryType())
                .eqIfPresent(ProjectCategoryDO::getCategoryLevel, reqVO.getCategoryLevel())
                .betweenIfPresent(ProjectCategoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProjectCategoryDO::getId));
    }

}