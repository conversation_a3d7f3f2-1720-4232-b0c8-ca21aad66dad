package cn.powerchina.bjy.link.dam.service.indexhead;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexhead.IndexHeadDO;
import jakarta.validation.*;

/**
 * 大坝首页头部信息 Service 接口
 *
 * <AUTHOR>
 */
public interface IndexHeadService {

    /**
     * 创建大坝首页头部信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIndexHead(@Valid IndexHeadSaveReqVO createReqVO);

    /**
     * 更新大坝首页头部信息
     *
     * @param updateReqVO 更新信息
     */
    void updateIndexHead(@Valid IndexHeadSaveReqVO updateReqVO);

    /**
     * 删除大坝首页头部信息
     *
     * @param id 编号
     */
    void deleteIndexHead(Long id);

    /**
     * 获得大坝首页头部信息
     *
     * @param id 编号
     * @return 大坝首页头部信息
     */
    IndexHeadDO getIndexHead(Long id);

    /**
     * 获得大坝首页头部信息分页
     *
     * @param pageReqVO 分页查询
     * @return 大坝首页头部信息分页
     */
    PageResult<IndexHeadDO> getIndexHeadPage(IndexHeadPageReqVO pageReqVO);

}
