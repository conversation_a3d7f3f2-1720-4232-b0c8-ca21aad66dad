package cn.powerchina.bjy.link.dam.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/4
 */
@Schema(description = "管理后台 - 用户状态 Request VO")
@Data
public class UserStatusReqVO {
    @Schema(description = "主键id", example = "28308")
    private Long id;

    @Schema(description = "启用状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "启用状态（0正常 1停用）不能为空")
    private Integer status;
}
