package cn.powerchina.bjy.link.dam.dal.dataobject.instrumenttemplate;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 仪器类型模板 DO
 *
 * <AUTHOR>
 */
@TableName("dam_instrument_template")
@KeySequence("dam_instrument_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstrumentTemplateDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 仪器类型模板名称
     */
    private String instrumentName;
    /**
     * 测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它
     */
    private Integer measurePrinciple;
    /**
     * 监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量
     */
    private Integer measureItem;
    /**
     * 图标地址
     */
    private String iconUrl;
    /**
     * 备注
     */
    private String remark;

}