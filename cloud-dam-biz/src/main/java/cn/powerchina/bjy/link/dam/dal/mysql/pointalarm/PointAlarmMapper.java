package cn.powerchina.bjy.link.dam.dal.mysql.pointalarm;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointalarm.PointAlarmDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 测点报警信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointAlarmMapper extends BaseMapperX<PointAlarmDO> {

    default PageResult<PointAlarmPageRespVO> selectPage(PointAlarmPageReqVO reqVO) {
        MPJLambdaWrapperX<PointAlarmDO> wrapper = (MPJLambdaWrapperX<PointAlarmDO>) new MPJLambdaWrapperX<PointAlarmDO>()
                .selectAll(PointAlarmDO.class)
                .selectAs(PointDO::getPointCode, PointAlarmPageRespVO::getPointCode)
                .leftJoin(PointDO.class, "p", on -> on.eq(PointDO::getId, PointAlarmDO::getPointId))
                .eqIfExists(PointAlarmDO::getProjectId, reqVO.getProjectId())
                .eqIfExists(PointAlarmDO::getSolutionStatus, reqVO.getSolutionStatus())
                .likeIfExists(PointDO::getPointCode, reqVO.getPointCode())
                .orderByDesc(PointEvaluateDO::getId);

        // 添加日期范围查询条件
        if (Objects.nonNull(reqVO.getAlarmTime())) {
            wrapper.geIfExists(PointAlarmDO::getAlarmTime, reqVO.getAlarmTime()[0])
                    .leIfExists(PointAlarmDO::getAlarmTime, reqVO.getAlarmTime()[1]);
        } else {
            LocalDate endTime = LocalDate.now();
            LocalDate startTime = endTime.minusMonths(1);
            wrapper.geIfExists(PointAlarmDO::getAlarmTime, startTime)
                    .leIfExists(PointAlarmDO::getAlarmTime, endTime.plusDays(1));
        }
        return selectJoinPage(reqVO, PointAlarmPageRespVO.class, wrapper);
    }

}