package cn.powerchina.bjy.link.dam.controller.admin.instrumentparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.service.instrumentparam.InstrumentParamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 仪器类型-计算参数")
@RestController
@RequestMapping("/dam/instrument/param")
@Validated
public class InstrumentParamController {

    @Resource
    private InstrumentParamService instrumentParamService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器类型-计算参数")
   // @PreAuthorize("@ss.hasPermission('dam:instrument-param:create')")
    public CommonResult<Long> createInstrumentParam(@Valid @RequestBody InstrumentParamSaveReqVO createReqVO) {
        return success(instrumentParamService.createInstrumentParam(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器类型-计算参数")
   // @PreAuthorize("@ss.hasPermission('dam:instrument-param:update')")
    public CommonResult<Boolean> updateInstrumentParam(@Valid @RequestBody InstrumentParamSaveReqVO updateReqVO) {
        instrumentParamService.updateInstrumentParam(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器类型-计算参数")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('dam:instrument-param:delete')")
    public CommonResult<Boolean> deleteInstrumentParam(@RequestParam("id") Long id) {
        instrumentParamService.deleteInstrumentParam(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器类型-计算参数")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-param:query')")
    public CommonResult<InstrumentParamRespVO> getInstrumentParam(@RequestParam("id") Long id) {
        InstrumentParamDO instrumentParam = instrumentParamService.getInstrumentParam(id);
        return success(BeanUtils.toBean(instrumentParam, InstrumentParamRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器类型-计算参数分页")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-param:query')")
    public CommonResult<PageResult<InstrumentParamRespVO>> getInstrumentParamPage(@Valid InstrumentParamPageReqVO pageReqVO) {
        PageResult<InstrumentParamDO> pageResult = instrumentParamService.getInstrumentParamPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentParamRespVO.class));
    }

}