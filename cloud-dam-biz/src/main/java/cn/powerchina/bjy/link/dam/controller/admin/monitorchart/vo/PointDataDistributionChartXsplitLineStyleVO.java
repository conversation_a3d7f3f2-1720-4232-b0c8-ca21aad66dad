package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Author: yangjingtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "lineStyle")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartXsplitLineStyleVO {

    @Schema(description = "color")
    private String color;

    @Schema(description = "type")
    private String type;

}
