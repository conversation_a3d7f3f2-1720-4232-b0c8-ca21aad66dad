package cn.powerchina.bjy.link.dam.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

/**
 * @Description: 描述
 * @Author: yhx
 */
@Component
public class SnowFlakeUtil {
    private long workerId = 0L;
    private long datacenterId = 1L;
    private Snowflake snowflake = IdUtil.getSnowflake(workerId, datacenterId);

    @PostConstruct
    public void init() {
        try {
            workerId = NetUtil.ipv4ToLong(NetUtil.getLocalhostStr());
        } catch (Exception e) {
            workerId = NetUtil.getLocalhostStr().hashCode();
        }
    }

    public synchronized long snowflakeId(long workerId, long datacenterId) {
        Snowflake snowflake = IdUtil.getSnowflake(workerId, datacenterId);
        return snowflake.nextId();
    }

    /**
     * 获取分布式id
     *
     * @return
     */
    public synchronized long snowflakeId() {
        return snowflake.nextId();
    }
}

