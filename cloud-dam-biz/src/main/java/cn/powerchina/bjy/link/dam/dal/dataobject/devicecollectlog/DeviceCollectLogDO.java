package cn.powerchina.bjy.link.dam.dal.dataobject.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 网关设备采集日志 DO
 *
 * <AUTHOR>
 */
@TableName("dam_device_collect_log")
@KeySequence("dam_device_collect_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceCollectLogDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 策略id
     */
    private Long strategyId;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 消息id
     */
    private String msgId;
    /**
     * 采集时间
     */
    private LocalDateTime collectTime;
    /**
     * 采集指令下发时间
     */
    private LocalDateTime sendTime;
    /**
     * 采集指令返回时间
     */
    private LocalDateTime backTime;
    /**
     * 采集状态，0：采集日志已创建，1：采集指令已下发，2：采集指令已返回
     */
    private Integer collectStatus;

    /**
     * 是否最后一个任务，0：不是，1：是
     */
    private Integer lastFlag;

}