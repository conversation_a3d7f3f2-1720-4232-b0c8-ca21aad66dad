package cn.powerchina.bjy.link.dam.controller.admin.authproduct;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.bo.AuthProductBO;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo.AuthProductPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo.AuthProductRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo.AuthProductSelectRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 产品授权")
@RestController
@RequestMapping("/dam/auth/product")
@Validated
public class AuthProductController {

    @Resource
    private AuthProductService authProductService;

    @GetMapping("/get")
    @Operation(summary = "获得产品授权")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:auth-product:query')")
    public CommonResult<AuthProductRespVO> getAuthProduct(@RequestParam("id") Long id) {
        AuthProductDO authProduct = authProductService.getAuthProduct(id);
        return success(BeanUtils.toBean(authProduct, AuthProductRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品授权分页")
//    @PreAuthorize("@ss.hasPermission('dam:auth-product:query')")
    public CommonResult<PageResult<AuthProductRespVO>> getAuthProductPage(@Valid AuthProductPageReqVO pageReqVO) {
        PageResult<AuthProductDO> pageResult = authProductService.getAuthProductPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AuthProductRespVO.class));
    }

    @GetMapping("/list/select")
    @Operation(summary = "获得采集设备产品下拉")
    @Parameter(name = "projectId", description = "项目id", required = false)
//    @PreAuthorize("@ss.hasPermission('dam:auth-product:query')")
    public CommonResult<List<AuthProductSelectRespVO>> getAuthProductSelectList(@RequestParam(value = "projectId", required = false) Long projectId) {
        List<AuthProductBO> productBOList = authProductService.getAuthProductSelectList(projectId);
        return success(BeanUtils.toBean(productBOList, AuthProductSelectRespVO.class));
    }

}