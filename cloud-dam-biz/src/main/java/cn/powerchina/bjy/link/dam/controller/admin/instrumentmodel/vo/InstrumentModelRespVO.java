package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型-测量分量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentModelRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "仪器类型id")
    @ExcelProperty("仪器类型id")
    private Long instrumentId;

    @Schema(description = "分量名称")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "标识符")
    @ExcelProperty("标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String thingUnit;

    @Schema(description = "数据类型，1：整数型，2：浮点型，3：双精度")
    @ExcelProperty("数据类型，1：整数型，2：浮点型，3：双精度")
    private Integer dataType;

    @Schema(description = "下限")
    @ExcelProperty("下限")
    private String downLimit;

    @Schema(description = "上限")
    @ExcelProperty("上限")
    private String upLimit;

    @Schema(description = "小数位")
    @ExcelProperty("小数位")
    private Integer decimalLimit;

    @Schema(description = "分量类型，1：原始值，2：中间值，3：成果值")
    @ExcelProperty("分量类型，1：原始值，2：中间值，3：成果值")
    private Integer thingType;

    @Schema(description = "权重，数字越小越靠前")
    @ExcelProperty("权重，数字越小越靠前")
    private Integer thingWeight;

    @Schema(description = "iot属性标识符")
    @ExcelProperty("iot属性标识符")
    private String thingIdentityIot;

    @Schema(description = "iot属性名称")
    @ExcelProperty("iot属性名称")
    private String thingNameIot;

    @Schema(description = "计算公式")
    @ExcelProperty("计算公式")
    private String calcFormula;

    @Schema(description = "公式有效开始时间")
    @ExcelProperty("公式有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "公式有效结束时间")
    @ExcelProperty("公式有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "产品名称")
    private String productName;

}