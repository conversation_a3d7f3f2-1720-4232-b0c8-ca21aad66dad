package cn.powerchina.bjy.link.dam.service.devicestrategy;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategyPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategySaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy.DeviceStrategyDO;
import cn.powerchina.bjy.link.dam.dal.mysql.devicestrategy.DeviceStrategyMapper;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.devicecollectlog.DeviceCollectLogService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.DEVICE_STRATEGY_NOT_EXISTS;

/**
 * 设备采集策略 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceStrategyServiceImpl implements DeviceStrategyService {

    @Resource
    private DeviceStrategyMapper deviceStrategyMapper;

    @Autowired
    @Lazy
    private DeviceCollectLogService deviceCollectLogService;

    @Autowired
    @Lazy
    private DeviceService deviceService;

    @Override
    public Long createDeviceStrategy(DeviceStrategySaveReqVO createReqVO) {
        //唯一性校验
        if (Objects.nonNull(deviceStrategyMapper.selectOne(new LambdaQueryWrapperX<DeviceStrategyDO>().eq(DeviceStrategyDO::getStrategyName, createReqVO.getStrategyName()).eq(DeviceStrategyDO::getProjectId, createReqVO.getProjectId())))) {
            throw exception(ErrorCodeConstants.DEVICE_STRATEGY_NAME_EXISTS);
        }
        // 插入
        DeviceStrategyDO deviceStrategy = BeanUtils.toBean(createReqVO, DeviceStrategyDO.class);
        deviceStrategyMapper.insert(deviceStrategy);
        // 返回
        return deviceStrategy.getId();
    }

    @Override
    @Transactional
    public void updateDeviceStrategy(DeviceStrategySaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceStrategyExists(updateReqVO.getId());
        DeviceStrategyDO deviceStrategyDO = deviceStrategyMapper.selectOne(new LambdaQueryWrapperX<DeviceStrategyDO>().eq(DeviceStrategyDO::getStrategyName, updateReqVO.getStrategyName()).eq(DeviceStrategyDO::getProjectId, updateReqVO.getProjectId()));
        if (Objects.nonNull(deviceStrategyDO) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), deviceStrategyDO.getId()))) {
            throw exception(ErrorCodeConstants.DEVICE_STRATEGY_NAME_EXISTS);
        }
        DeviceStrategyDO strategyDO = getDeviceStrategy(updateReqVO.getId());
        // 更新
        DeviceStrategyDO updateObj = BeanUtils.toBean(updateReqVO, DeviceStrategyDO.class);
        deviceStrategyMapper.updateById(updateObj);

        List<DeviceDO> deviceDO = deviceService.getDeviceByStrategyId(updateReqVO.getId());
        if (!Objects.equals(strategyDO.getStrategyType(), updateReqVO.getStrategyType()) ||
                !Objects.equals(strategyDO.getTimeInterval(), updateReqVO.getTimeInterval()) ||
                !Objects.equals(strategyDO.getTimePoint(), updateReqVO.getTimePoint())) {
            if (!CollectionUtils.isEmpty(deviceDO)) {
                for (DeviceDO device : deviceDO) {
                    deviceCollectLogService.createDeviceCollectLog(DeviceCollectLogSaveReqVO.builder()
                            .projectId(device.getProjectId())
                            .deviceCode(device.getDeviceCode())
                            .strategyId(device.getStrategyId())
                            .build());
                }
            }
        }
    }

    @Override
    public void deleteDeviceStrategy(Long id) {
        // 校验存在
        validateDeviceStrategyExists(id);
        //有采集仪在使用该策略时，不允许删除
        if (deviceService.countDeviceByStrategyId(id).compareTo(0L) > 0) {
            throw exception(ErrorCodeConstants.DEVICE_STRATEGY_USE_EXISTS);
        }
        // 删除
        deviceStrategyMapper.deleteById(id);
    }

    @Override
    public DeviceStrategyDO validateDeviceStrategyExists(Long id) {
        DeviceStrategyDO deviceStrategyDO = deviceStrategyMapper.selectById(id);
        if (Objects.isNull(deviceStrategyDO)) {
            throw exception(DEVICE_STRATEGY_NOT_EXISTS);
        }
        return deviceStrategyDO;
    }

    @Override
    public DeviceStrategyDO getDeviceStrategy(Long id) {
        return deviceStrategyMapper.selectById(id);
    }


    @Override
    public PageResult<DeviceStrategyDO> getDeviceStrategyPage(DeviceStrategyPageReqVO pageReqVO) {
        return deviceStrategyMapper.selectPage(pageReqVO);
    }

}