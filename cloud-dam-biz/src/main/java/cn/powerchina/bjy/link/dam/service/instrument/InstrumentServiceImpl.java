package cn.powerchina.bjy.link.dam.service.instrument;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.dict.DictDataApi;
import cn.powerchina.bjy.cloud.system.api.dict.dto.DictDataRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.ImportConfigModeSaveVo;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.TreeVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigPointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentaccount.InstrumentAccountDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigPointMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrument.InstrumentMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentaccount.InstrumentAccountMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentparam.InstrumentParamMapper;
import cn.powerchina.bjy.link.dam.enums.CategoryTypeEnum;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.importconfig.ImportConfigService;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.instrumentparam.InstrumentParamService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 仪器类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentServiceImpl implements InstrumentService {

    @Resource
    private InstrumentMapper instrumentMapper;

    @Resource
    private InstrumentAccountMapper instrumentAccountMapper;

    @Resource
    private SnowFlakeUtil snowFlakeUtil;

    @Resource
    @Lazy
    private PointService pointService;

    @Resource
    @Lazy
    private InstrumentModelService instrumentModelService;

    @Resource
    @Lazy
    private InstrumentParamService instrumentParamService;

    @Autowired
    private ProjectCategoryService projectCategoryService;

    @Resource
    private InstrumentModelMapper instrumentModelMapper;

    @Resource
    private InstrumentParamMapper instrumentParamMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private ImportConfigPointMapper   importConfigPointMapper;

    @Resource
    private ImportConfigModelMapper importConfigModelMapper;

    @Override
    public Long createInstrument(InstrumentSaveReqVO createReqVO) {
        validateNameExists(createReqVO.getId(), createReqVO.getInstrumentName(), createReqVO.getProjectId());
        // 插入
        InstrumentDO instrument = BeanUtils.toBean(createReqVO, InstrumentDO.class);
        instrument.setId(snowFlakeUtil.snowflakeId());
        instrumentMapper.insert(instrument);
        // 返回
        return instrument.getId();
    }

    @Override
    public void updateInstrument(InstrumentSaveReqVO updateReqVO) {
        // 校验存在
        InstrumentDO instrumentDO = validateInstrumentExists(updateReqVO.getId());
        validateNameExists(updateReqVO.getId(), updateReqVO.getInstrumentName(), updateReqVO.getProjectId());
        //如果更换iot产品，清空仪器分量iot属性标识值
        if (Objects.nonNull(instrumentDO.getProductCode()) && !instrumentDO.getProductCode().equals(updateReqVO.getProductCode())) {
            List<InstrumentModelDO> modelList = instrumentModelService.getModelByInstrumentId(instrumentDO.getId());
            if (!CollectionUtils.isEmpty(modelList)) {
                modelList.forEach(item -> {
                    item.setThingIdentityIot(null);
                    instrumentModelService.updateInstrumentModel(BeanUtils.toBean(item, InstrumentModelSaveReqVO.class));
                });
            }
        }
        //更新树结构的仪器名称
        projectCategoryService.updateProjectCategoryName(updateReqVO.getProjectId(), getInstrument(updateReqVO.getId()).getInstrumentName(), updateReqVO.getInstrumentName(), CategoryTypeEnum.INSTRUMENT.getType());
        // 更新
        InstrumentDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentDO.class);
        instrumentMapper.updateById(updateObj);
    }

    @Override
    @Transactional
    public void deleteInstrument(Long id) {
        // 校验存在
        InstrumentDO instrumentDO = instrumentMapper.selectById(id);
        if (instrumentDO == null) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
        List<PointDO> pointList = pointService.getPointListByInstrumentId(id);
        if (!CollectionUtils.isEmpty(pointList)) {
            throw exception(INSTRUMENT_POINT_EXISTS);
        }
        //判断仪器类型下是否有仪器台账
        List<InstrumentAccountDO> instrumentAccountDOList=instrumentAccountMapper.selectList(new LambdaQueryWrapperX<InstrumentAccountDO>().eq(InstrumentAccountDO::getInstrumentId, instrumentDO.getId()));
        if (!CollectionUtils.isEmpty(instrumentAccountDOList)) {
            throw exception(INSTRUMENT_ACCOUNT_EXISTS);
        }
        //删除左侧树的仪器类型和分组
        projectCategoryService.deleteInstrumentAndGroup(id);
        // 删除
        instrumentMapper.deleteById(id);
    }

    private InstrumentDO validateInstrumentExists(Long id) {
        InstrumentDO instrumentDO = instrumentMapper.selectById(id);
        if (instrumentDO == null) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
        return instrumentDO;
    }

    @Override
    public InstrumentDO getInstrument(Long id) {
        return instrumentMapper.selectById(id);
    }

    @Override
    public PageResult<InstrumentDO> getInstrumentPage(InstrumentPageReqVO pageReqVO) {
        return instrumentMapper.selectPage(pageReqVO);
    }

    @Override
    public Boolean checkPoint(Long id) {
        validateInstrumentExists(id);
        List<PointDO> pointList = pointService.getPointListByInstrumentId(id);
        return !CollectionUtils.isEmpty(pointList);
    }

    @Override
    public List<InstrumentDO> getInstrumentList(Long projectId) {
        return instrumentMapper.selectList(new LambdaQueryWrapperX<InstrumentDO>()
                .eq(InstrumentDO::getProjectId, projectId)
                .orderByDesc(InstrumentDO::getId));
    }

    @Override
    public List<InstrumentDO> listByIdList(List<Long> idList) {
        return instrumentMapper.selectList(new LambdaQueryWrapperX<InstrumentDO>()
                .in(InstrumentDO::getId, idList));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertListInstrument(InstrumentSaveListReqVO createReqVO) {
        InstrumentSaveReqVO respVO = createReqVO.getInstrumentSaveReqVO();
        validateNameExistsList(createReqVO);
        long Id;
        if (Objects.nonNull(respVO.getId())) {
            updateInstrument(respVO);
            Id = respVO.getId();
        } else {
            Id = createInstrument(respVO);
        }
        if(createReqVO.getInstrumentModelSaveReqVOS().size()>0){
            for (InstrumentModelSaveReqVO model : createReqVO.getInstrumentModelSaveReqVOS()){
                if (model.getThingIdentity() != null && model.getThingIdentity().matches("^\\d+$")) {
                    throw exception(new ErrorCode(500, "分量标识符不能是纯数字"));
                }
            }
        }
        insertListParam(createReqVO.getInstrumentParamSaveReqVOS(), Id);
        insertListModel(createReqVO.getInstrumentModelSaveReqVOS(), Id);
        return Id;
    }

    @Transactional(rollbackFor = Exception.class)
    void insertListModel(List<InstrumentModelSaveReqVO> instrumentModelSaveReqVOS, long id) {

        List<Long> oldIdList = new ArrayList<>();
        List<InstrumentModelDO> oldParamList = instrumentModelService.getModelByInstrumentId(id);
        if (!CollectionUtils.isEmpty(oldParamList)) {
            oldIdList = oldParamList.stream().map(InstrumentModelDO::getId).collect(Collectors.toList());
        }
        instrumentModelSaveReqVOS.forEach(req ->
                req.setInstrumentId(req.getInstrumentId() == null ? id : req.getInstrumentId())
        );
        ArrayList<InstrumentModelDO> instList = new ArrayList<>();
        ArrayList<InstrumentModelDO> updateList = new ArrayList<>();

        for (InstrumentModelSaveReqVO reqVO : instrumentModelSaveReqVOS) {
            InstrumentModelDO model = BeanUtils.toBean(reqVO, InstrumentModelDO.class);
            if (Objects.isNull(reqVO.getId())) {
                instrumentModelMapper.insert(model);
                instList.add(model);
            } else {
                instrumentModelMapper.updateById(model);
                oldIdList.remove(model.getId());
                updateList.add(model);
            }
        }
        if (!CollectionUtils.isEmpty(oldIdList)) {
            instrumentModelMapper.deleteBatchIds(oldIdList);
        }
        List<ImportConfigPointDO> pointDOS = importConfigPointMapper.selectList(new LambdaQueryWrapperX<ImportConfigPointDO>()
                .eq(ImportConfigPointDO::getInstrumentId, id));
        if (!CollectionUtils.isEmpty(pointDOS)) {
            for (ImportConfigPointDO pointDO : pointDOS) {
                List<ImportConfigModelDO> importConfigModelDOS = importConfigModelMapper.selectList(new LambdaQueryWrapperX<ImportConfigModelDO>()
                        .eq(ImportConfigModelDO::getPointConfigId, pointDO.getId()));
                if (!CollectionUtils.isEmpty(importConfigModelDOS) && !CollectionUtils.isEmpty(oldIdList)) {
                    List<Long> finalOldIdList = oldIdList;
                    List<Long> toDeleteIds = importConfigModelDOS.stream()
                            .filter(modelDO -> finalOldIdList.contains(modelDO.getInstrumentModelId()))
                            .map(ImportConfigModelDO::getId)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(toDeleteIds)) {
                        importConfigModelMapper.deleteBatchIds(toDeleteIds);
                    }
                }
//                新增
                if (instList.size() > 0) {
                    // 新增分量到importConfigModel表
                    for (InstrumentModelDO model : instList) {
                        ImportConfigModelDO configModel = new ImportConfigModelDO();
                        configModel.setPointConfigId(pointDO.getId());
                        configModel.setIsAutomation(0);
                        configModel.setStartLine(1);
                        configModel.setThingIdentity(model.getThingIdentity());
                        configModel.setInstrumentModelId(model.getId());
                        configModel.setCreator(model.getCreator());
                        configModel.setUpdater(model.getUpdater());
                        importConfigModelMapper.insert(configModel);
                    }
                }
//               修改
                if (updateList.size() > 0) {
                    for (InstrumentModelDO model : updateList) {
                        ImportConfigModelDO configModel = importConfigModelMapper.selectOne(new LambdaQueryWrapperX<ImportConfigModelDO>()
                                .eq(ImportConfigModelDO::getPointConfigId, pointDO.getId())
                                .eq(ImportConfigModelDO::getInstrumentModelId, model.getId()));
                        if (configModel != null) {
                            configModel.setThingIdentity(model.getThingIdentity());
                            configModel.setUpdater(model.getUpdater());
                            importConfigModelMapper.updateById(configModel);
                        }
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    void insertListParam(List<InstrumentParamSaveReqVO> instrumentParamSaveReqVOS, long id) {
        List<Long> oldIdList = new ArrayList<>();
        List<InstrumentParamDO> oldParamList = instrumentParamService.getListByInstrumentId(id);
        if (!CollectionUtils.isEmpty(oldParamList)) {
            oldIdList = oldParamList.stream()
                    .map(InstrumentParamDO::getId)
                    .collect(Collectors.toList());
        }
        instrumentParamSaveReqVOS.forEach(req ->
                req.setInstrumentId(req.getInstrumentId() == null ? id : req.getInstrumentId())
        );
        for (InstrumentParamSaveReqVO reqVO : instrumentParamSaveReqVOS) {
            InstrumentParamDO param = BeanUtils.toBean(reqVO, InstrumentParamDO.class);
            if (Objects.isNull(reqVO.getId())) {
                instrumentParamMapper.insert(param);
            } else {
                instrumentParamMapper.updateById(param);
                oldIdList.remove(param.getId());
            }
        }
        //删除
        if (!CollectionUtils.isEmpty(oldIdList)) {
            instrumentParamMapper.deleteBatchIds(oldIdList);
        }
    }

    private void validateNameExistsList(InstrumentSaveListReqVO reqVO) {

        String instrumentName = reqVO.getInstrumentSaveReqVO().getInstrumentName();
        chackParamAndModelRepeat(reqVO.getInstrumentParamSaveReqVOS(), reqVO.getInstrumentModelSaveReqVOS());
        InstrumentDO instrumentDO = instrumentMapper.selectOne(new LambdaQueryWrapperX<InstrumentDO>().eq(InstrumentDO::getInstrumentName, instrumentName).eq(InstrumentDO::getProjectId,reqVO.getInstrumentSaveReqVO().getProjectId()));
        if (Objects.nonNull(instrumentDO)
                && !Objects.equals(reqVO.getInstrumentSaveReqVO().getId(),instrumentDO.getId())) {
            throw exception(ErrorCodeConstants.INSTRUMENT_NAME_EXISTS);
        }
        List<InstrumentParamSaveReqVO> paramReq = reqVO.getInstrumentParamSaveReqVOS();
        List<InstrumentModelSaveReqVO> modeReq = reqVO.getInstrumentModelSaveReqVOS();
        for (InstrumentParamSaveReqVO param : paramReq) {
            if (!Objects.isNull(param.getInstrumentId())
                    && Objects.isNull(param.getId())) {//子表新增
                validateNameExistsParam(param.getId(), param.getThingName(), param.getInstrumentId());
                validateThingIdentityExistsParamAndMode(param.getId(), param.getInstrumentId(), param.getThingIdentity());
            } else if (!Objects.isNull(param.getInstrumentId())
                    && !Objects.isNull(param.getId())) {//子表修改
                validateInstrumentParamExistsParamAndMode(param.getId());
            }
        }
        for (InstrumentModelSaveReqVO mode : modeReq) {
             if (!Objects.isNull(mode.getInstrumentId())
                    && Objects.isNull(mode.getId())) {//子表新增
                validateInstrumentModelExists(mode.getId(), mode.getThingName(), mode.getInstrumentId());
                validateThingIdentityExistsParamAndMode(mode.getId(), mode.getInstrumentId(), mode.getThingIdentity());
            } else if (!Objects.isNull(mode.getInstrumentId())
                    && !Objects.isNull(mode.getId())) {//子表修改
                validateInstrumentModelExistsParamAndMode(mode.getId());
            }

        }

    }

    private void chackParamAndModelRepeat(List<InstrumentParamSaveReqVO> paramList, List<InstrumentModelSaveReqVO> modelList) {

        // 提取并检查第一个集合的 thingName 唯一性
        Set<String> paramThingNames = new HashSet<>();
        Set<String> paramThingIdentity = new HashSet<>();
        if (null != paramList) {
            for (InstrumentParamSaveReqVO vo : paramList) {
                String thingName = vo.getThingName();
                String thingIdentity = vo.getThingIdentity();
                if (thingName == null || !paramThingNames.add(thingName)) {
                    throw exception(INSTRUMENT_PARAMS_NAME_EXISTS);
                }
                if (thingName == null || !paramThingIdentity.add(thingIdentity)) {
                    throw exception(INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS);
                }
            }
        }

        // 提取并检查第二个集合的 thingName 唯一性
        Set<String> modelThingNames = new HashSet<>();
        Set<String> modelThingIdentity = new HashSet<>();
        if (null != modelList) {
            for (InstrumentModelSaveReqVO vo : modelList) {
                String thingName = vo.getThingName();
                String thingIdentity = vo.getThingIdentity();
                if (thingName == null || !modelThingNames.add(thingName)) {
                    throw exception(INSTRUMENT_MODEL_NAME_EXISTS);
                }
                if (thingName == null || !modelThingIdentity.add(thingIdentity)) {
                    throw exception(INSTRUMENT_MODEL_THING_IDENTITY_EXISTS);
                }
            }
        }

        // 检查两个集合的交集
        paramThingNames.retainAll(modelThingNames);
        paramThingIdentity.retainAll(modelThingIdentity);
        if (!paramThingNames.isEmpty() || !paramThingIdentity.isEmpty()) {
            throw exception(PARAM_MODEL_REPEAT);
        }
    }

    private void validateInstrumentModelExistsParamAndMode(Long id) {
        InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectById(id);
        if (instrumentModelDO == null) {
            throw exception(INSTRUMENT_MODEL_NOT_EXISTS);
        }
    }

    private void validateThingIdentityExistsParamAndMode(Long id, Long instrumentId, String thingIdentity) {
        InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelDO>().
                eq(InstrumentModelDO::getThingIdentity, thingIdentity).eq(InstrumentModelDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO)) {
            throw exception(INSTRUMENT_MODEL_THING_IDENTITY_EXISTS);
        }

        InstrumentParamDO instrumentParamDO = instrumentParamMapper.selectOne(new LambdaQueryWrapperX<InstrumentParamDO>().
                eq(InstrumentParamDO::getThingIdentity, thingIdentity).eq(InstrumentParamDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentParamDO)) {
            throw exception(INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS);
        }
    }

    private void validateInstrumentParamExistsParamAndMode(Long id) {
        InstrumentParamDO instrumentModelDO = instrumentParamMapper.selectById(id);
        if (instrumentModelDO == null) {
            throw exception(INSTRUMENT_MODEL_NOT_EXISTS);
        }
    }

    private void validateNameExistsParam(Long id, String name, Long instrumentId) {
        InstrumentParamDO instrumentParamDO = instrumentParamMapper.selectOne(new LambdaQueryWrapperX<InstrumentParamDO>().
                eq(InstrumentParamDO::getThingName, name).eq(InstrumentParamDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentParamDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentParamDO.getId()))) {
            throw exception(INSTRUMENT_PARAMS_NAME_EXISTS);
        }
    }

    private void validateInstrumentModelExists(Long id, String name, Long instrumentId) {
        InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelDO>().
                eq(InstrumentModelDO::getThingName, name).eq(InstrumentModelDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentModelDO.getId()))) {
            throw exception(INSTRUMENT_MODEL_NAME_EXISTS);
        }
    }

    @Override
    public InstrumenListReqVO getInstrumentAndModelAndParam(Long id, Long projectId) {
        InstrumenListReqVO res = new InstrumenListReqVO();
        InstrumentDO instrumentDO = instrumentMapper.selectById(id);
        InstrumentRespVO instrumentRespVO = new InstrumentRespVO();
        BeanUtil.copyProperties(instrumentDO, instrumentRespVO);
        InstrumentModelRespVO modelRespVO = new InstrumentModelRespVO();
        InstrumentParamRespVO pageReqVO = new InstrumentParamRespVO();
        List<InstrumentParamSaveReqVO> instrumentParamSaveReqVOS = new ArrayList<>();
        pageReqVO.setInstrumentId(id);
        pageReqVO.setProjectId(projectId);
        modelRespVO.setInstrumentId(id);
        modelRespVO.setProjectId(projectId);
        List<InstrumentModelRespVO> instrumentModelLists = instrumentModelService.getInstrumentModelLists(modelRespVO);
        List<InstrumentParamDO> instrumentParamDOS = instrumentParamMapper.selectList(new LambdaQueryWrapperX<InstrumentParamDO>().
                eq(InstrumentParamDO::getInstrumentId, id).eq(InstrumentParamDO::getProjectId, projectId)
                .orderByAsc(InstrumentParamDO::getThingWeight));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(instrumentParamDOS)) {
            instrumentParamSaveReqVOS = instrumentParamDOS.stream()
                    .map(doItem -> {
                        InstrumentParamSaveReqVO voItem = new InstrumentParamSaveReqVO();
                        BeanUtil.copyProperties(doItem, voItem);
                        return voItem;
                    })
                    .collect(Collectors.toList());
        }
        res.setInstrumentSaveReqVO(instrumentRespVO);
        res.setInstrumentParamSaveReqVOS(instrumentParamSaveReqVOS);
        res.setInstrumentModelSaveReqVOS(instrumentModelLists);
        return res;
    }

    @Override
    public List<TreeVO> getListTree(Long projectId) {
        List<InstrumentDO> instrumentDOS = instrumentMapper.selectList(new LambdaQueryWrapperX<InstrumentDO>()
                .eq(InstrumentDO::getProjectId, projectId));
        CommonResult<List<DictDataRespDTO>> dictDataList = dictDataApi.getDictDataList("measure_principle");
        List<TreeVO> treeList = new ArrayList<>();
        dictDataList.getData().stream().forEach(item -> {
            TreeVO parentVO = new TreeVO();
            parentVO.setId(item.getValue());
            parentVO.setName(item.getLabel());
            treeList.add(parentVO);
            Stream<InstrumentDO> childList = instrumentDOS.stream().filter(template -> String.valueOf(template.getMeasurePrinciple()).equals(item.getValue()));
            childList.forEach(template -> {
                TreeVO childVO = new TreeVO();
                childVO.setId(String.valueOf(template.getId()));
                childVO.setName(template.getInstrumentName());
                childVO.setParentId(item.getValue());
                treeList.add(childVO);
            });
        });
        return treeList;
    }

    /**
     * 校验仪器类型名称是否存在
     *
     * @param id
     * @param name
     */
    private void validateNameExists(Long id, String name, Long projectId) {
        InstrumentDO instrumentDO = instrumentMapper.selectOne(new LambdaQueryWrapperX<InstrumentDO>().eq(InstrumentDO::getInstrumentName, name).eq(InstrumentDO::getProjectId, projectId));
        if (Objects.nonNull(instrumentDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentDO.getId()))) {
            throw exception(ErrorCodeConstants.INSTRUMENT_NAME_EXISTS);
        }
    }

}