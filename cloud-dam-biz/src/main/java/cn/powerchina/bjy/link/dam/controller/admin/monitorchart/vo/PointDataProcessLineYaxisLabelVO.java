package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: yangjingt<PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "轴线")
@Data
public class PointDataProcessLineYaxisLabelVO {

    @Schema(description = "align")
    private String align;

    @Schema(description = "padding")
    private List<Integer> padding;
}
