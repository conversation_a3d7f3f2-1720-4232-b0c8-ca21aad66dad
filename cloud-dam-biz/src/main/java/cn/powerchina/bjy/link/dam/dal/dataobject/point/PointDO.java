package cn.powerchina.bjy.link.dam.dal.dataobject.point;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测点信息 DO
 *
 * <AUTHOR>
 */
@TableName("dam_point")
@KeySequence("dam_point_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 工程分类id
     */
    private Long categoryId;
    /**
     * 节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点）
     */
    private Integer categoryType;
    /**
     * 测点编号
     */
    private String pointCode;
    /**
     * 测点名称
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String pointName;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 仪器台账id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long accountId;
    /**
     * 量程规格
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String rangeSpecifications;
    /**
     * 监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量
     */
    private Integer measureItem;
    /**
     * 重要程度，1：一般
     */
    private Integer importantLevel;
    /**
     * 安装时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime installTime;
    /**
     * 基准时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime referenceTime;
    /**
     * 虚拟测点，0：否，1：是
     */
    private Integer virtualPoint;
    /**
     * 工程结构id
     */
    private Long structCategoryId;
    /**
     * 安装位置
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String installPosition;
    /**
     * 安装高程（m）
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String installHeight;
    /**
     * 埋设位置
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String hidePosition;
    /**
     * 埋设高程
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String hideHeight;
    /**
     * 埋设深度
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String hideDeepth;
    /**
     * 上下桩号
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String updownPile;
    /**
     * 左右桩号
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String leftrightPile;
    /**
     * X坐标（m）
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String xCoordinate;
    /**
     * Y坐标（m）
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String yCoordinate;
    /**
     * Z坐标（m）
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String zCoordinate;
    /**
     * 测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他
     */
    private Integer pointState;
    /**
     * 测量类型，1：人/自一体，2：自动化，3：人工
     */
    private Integer pointType;
    /**
     * 测量性质，1：运行期，2：施工期
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer pointStage;

    /**
     * 是否绑定设备，0：未绑定，1：已绑定
     */
    private Integer bindType;

    /**
     * 人工观测频次
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer manualFrequency;

    /**
     * 自动化观测频次
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer automatedFrequency;

}