package cn.powerchina.bjy.link.dam.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/26
 */
public class BigDecimalUtils {

    /**
     * 计算差值
     *
     * @param value1
     * @param value2
     * @param scale
     * @return
     */
    public static BigDecimal calculateValueSub(BigDecimal value1, BigDecimal value2, int scale) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return null;
        }
        return value1.subtract(value2).setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 设置值
     *
     * @param value1
     * @param scale
     * @return
     */
    public static BigDecimal calculateValue(BigDecimal value1, int scale) {
        if (Objects.isNull(value1)) {
            return null;
        }
        return value1.setScale(scale, RoundingMode.HALF_UP);
    }
}
