package cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 测点报警信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointAlarmPageReqVO extends PageParam {

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "报警时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] alarmTime;

    @Schema(description = "处理状态，0：未处理，1：已处理")
    private Integer solutionStatus;

}