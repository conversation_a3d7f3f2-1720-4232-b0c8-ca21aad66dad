package cn.powerchina.bjy.link.dam.service.instrumentmodeltemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodeltemplate.InstrumentModelTemplateDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 仪器类型模板-测量分量 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentModelTemplateService {

    /**
     * 创建仪器类型模板-测量分量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrumentModelTemplate(@Valid InstrumentModelTemplateSaveReqVO createReqVO);

    /**
     * 更新仪器类型模板-测量分量
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrumentModelTemplate(@Valid InstrumentModelTemplateSaveReqVO updateReqVO);

    /**
     * 删除仪器类型模板-测量分量
     *
     * @param id 编号
     */
    void deleteInstrumentModelTemplate(Long id);

    /**
     * 获得仪器类型模板-测量分量
     *
     * @param id 编号
     * @return 仪器类型模板-测量分量
     */
    InstrumentModelTemplateDO getInstrumentModelTemplate(Long id);

    /**
     * 获得仪器类型模板-测量分量分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器类型模板-测量分量分页
     */
    PageResult<InstrumentModelTemplateDO> getInstrumentModelTemplatePage(InstrumentModelTemplatePageReqVO pageReqVO);

    List<InstrumentModelTemplateDO> getModelByInstrumentId(long templateId);
}