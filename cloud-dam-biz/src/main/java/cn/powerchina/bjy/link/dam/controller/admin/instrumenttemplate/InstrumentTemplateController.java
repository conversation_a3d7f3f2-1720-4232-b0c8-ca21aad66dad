package cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumenttemplate.InstrumentTemplateDO;
import cn.powerchina.bjy.link.dam.service.instrumenttemplate.InstrumentTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 仪器类型模板")
@RestController
@RequestMapping("/dam/instrument-template")
@Validated
public class InstrumentTemplateController {

    @Resource
    private InstrumentTemplateService instrumentTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器类型模板")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:create')")
    public CommonResult<Long> createInstrumentTemplate(@Valid @RequestBody InstrumentTemplateSaveReqVO createReqVO) {
        return success(instrumentTemplateService.createInstrumentTemplate(createReqVO));
    }

    @PostMapping("/insert-list")
    @Operation(summary = "创建仪器类型模板")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:create')")
    public CommonResult<Long> insertListInstrumentTemplate(@Valid @RequestBody InstrumentTemplateSaveListReqVO createReqVO) {
        return success(instrumentTemplateService.insertListInstrumentTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器类型模板")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:update')")
    public CommonResult<Boolean> updateInstrumentTemplate(@Valid @RequestBody InstrumentTemplateSaveReqVO updateReqVO) {
        instrumentTemplateService.updateInstrumentTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器类型模板")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:delete')")
    public CommonResult<Boolean> deleteInstrumentTemplate(@RequestParam("id") Long id) {
        instrumentTemplateService.deleteInstrumentTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器类型模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:query')")
    public CommonResult<InstrumentTemplateSaveListReqVO> getInstrumentTemplate(@RequestParam("id") Long id) {
        InstrumentTemplateSaveListReqVO instrumentTemplate = instrumentTemplateService.getInstrumentTemplate(id);
        return success(instrumentTemplate);
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器类型模板分页")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:query')")
    public CommonResult<PageResult<InstrumentTemplateRespVO>> getInstrumentTemplatePage(@Valid InstrumentTemplatePageReqVO pageReqVO) {
        PageResult<InstrumentTemplateDO> pageResult = instrumentTemplateService.getInstrumentTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出仪器类型模板 Excel")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:export')")
    public void exportInstrumentTemplateExcel(@Valid InstrumentTemplatePageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InstrumentTemplateDO> list = instrumentTemplateService.getInstrumentTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "仪器类型模板.xls", "数据", InstrumentTemplateRespVO.class,
                BeanUtils.toBean(list, InstrumentTemplateRespVO.class));
    }

    @GetMapping("/get-list-tree")
    @Operation(summary = "获得仪器类型模板列表树")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:query')")
    public CommonResult<List<TreeVO>> getListTree() {
        List<TreeVO> res = instrumentTemplateService.getListTree();
        return success(res);
    }

}