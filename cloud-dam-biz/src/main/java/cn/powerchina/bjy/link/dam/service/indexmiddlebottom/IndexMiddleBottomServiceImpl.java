package cn.powerchina.bjy.link.dam.service.indexmiddlebottom;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexmiddlebottom.IndexMiddleBottomDO;
import cn.powerchina.bjy.link.dam.dal.mysql.indexmiddlebottom.IndexMiddleBottomMapper;
import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INDEX_MIDDLE_BOTTOM_NOT_EXISTS;

/**
 * 首页中间和底部数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndexMiddleBottomServiceImpl implements IndexMiddleBottomService{
    @Resource
    private IndexMiddleBottomMapper indexMiddleBottomMapper;

    @Override
    public Long createIndexMiddleBottom(IndexMiddleBottomSaveReqVO createReqVO) {
        // 插入
        IndexMiddleBottomDO indexMiddleBottom = BeanUtils.toBean(createReqVO, IndexMiddleBottomDO.class);
        indexMiddleBottomMapper.insert(indexMiddleBottom);
        // 返回
        return indexMiddleBottom.getId();
    }

    @Override
    public void updateIndexMiddleBottom(IndexMiddleBottomSaveReqVO updateReqVO) {
        // 校验存在
        validateIndexMiddleBottomExists(updateReqVO.getId());
        // 更新
        IndexMiddleBottomDO updateObj = BeanUtils.toBean(updateReqVO, IndexMiddleBottomDO.class);
        indexMiddleBottomMapper.updateById(updateObj);
    }

    @Override
    public void deleteIndexMiddleBottom(Long id) {
        // 校验存在
        validateIndexMiddleBottomExists(id);
        // 删除
        indexMiddleBottomMapper.deleteById(id);
    }

    private void validateIndexMiddleBottomExists(Long id) {
        if (indexMiddleBottomMapper.selectById(id) == null) {
            throw exception(INDEX_MIDDLE_BOTTOM_NOT_EXISTS);
        }
    }

    @Override
    public IndexMiddleBottomDO getIndexMiddleBottom(Long id) {
        return indexMiddleBottomMapper.selectById(id);
    }

    @Override
    public PageResult<IndexMiddleBottomDO> getIndexMiddleBottomPage(IndexMiddleBottomPageReqVO pageReqVO) {
        return indexMiddleBottomMapper.selectPage(pageReqVO);
    }
}
