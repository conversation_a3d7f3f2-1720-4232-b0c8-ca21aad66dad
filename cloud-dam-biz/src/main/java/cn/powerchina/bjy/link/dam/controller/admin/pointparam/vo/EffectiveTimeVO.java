package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/13
 */
@Data
public class EffectiveTimeVO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 有效开始时间
     */
    private LocalDateTime effectiveStartTime;
    /**
     * 有效结束时间
     */
    private LocalDateTime effectiveEndTime;
}
