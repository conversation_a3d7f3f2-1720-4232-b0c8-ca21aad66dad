package cn.powerchina.bjy.link.dam.service.instrumenttemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplateSaveListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.TreeVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumenttemplate.InstrumentTemplateDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;


/**
 * 仪器类型模板 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentTemplateService {

    /**
     * 创建仪器类型模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrumentTemplate(@Valid InstrumentTemplateSaveReqVO createReqVO);

    /**
     * 更新仪器类型模板
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrumentTemplate(@Valid InstrumentTemplateSaveReqVO updateReqVO);

    /**
     * 删除仪器类型模板
     *
     * @param id 编号
     */
    void deleteInstrumentTemplate(Long id);

    /**
     * 获得仪器类型模板
     *
     * @param id 编号
     * @return 仪器类型模板
     */
    InstrumentTemplateSaveListReqVO getInstrumentTemplate(Long id);

    /**
     * 获得仪器类型模板分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器类型模板分页
     */
    PageResult<InstrumentTemplateDO> getInstrumentTemplatePage(InstrumentTemplatePageReqVO pageReqVO);

    Long insertListInstrumentTemplate(InstrumentTemplateSaveListReqVO createReqVO);

    List<TreeVO> getListTree();
}