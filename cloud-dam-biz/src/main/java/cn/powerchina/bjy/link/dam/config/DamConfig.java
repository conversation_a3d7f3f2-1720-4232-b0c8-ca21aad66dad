package cn.powerchina.bjy.link.dam.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/30
 */

@Component
@ConfigurationProperties(prefix = "dam.config")
@RefreshScope
@Data
public class DamConfig {

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;

    /**
     * 批量导入数量
     */
    private Integer batchCount;

}
