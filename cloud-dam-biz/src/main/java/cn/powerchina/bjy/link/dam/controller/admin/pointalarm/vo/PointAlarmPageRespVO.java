package cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点报警信息 Response VO")
@Data
@ExcelIgnoreUnannotated

public class PointAlarmPageRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25970")
    private Long id;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "监测时间")
    private LocalDateTime pointTime;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "测值")
    private String pointData;

    @Schema(description = "报警内容")
    private String alarmContent;

    @Schema(description = "报警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "处理情况")
    private String solutionContent;

    @Schema(description = "处理状态，0：未处理，1：已处理")
    private Integer solutionStatus;

    @Schema(description = "报警类型：1-异常报警；2-错误报警")
    private Integer alarmType;

    @Schema(description = "处理人姓名")
    private String solutionUserName;
}
