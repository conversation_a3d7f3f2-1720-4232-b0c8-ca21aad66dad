package cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplateRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparamtemplate.InstrumentParamTemplateDO;
import cn.powerchina.bjy.link.dam.service.instrumentparamtemplate.InstrumentParamTemplateService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 仪器类型模板-计算参数")
@RestController
@RequestMapping("/dam/instrument-param-template")
@Validated
public class InstrumentParamTemplateController {

    @Resource
    private InstrumentParamTemplateService instrumentParamTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器类型模板-计算参数")
   // @PreAuthorize("@ss.hasPermission('dam:instrument-param-template:create')")
    public CommonResult<Long> createInstrumentParamTemplate(@Valid @RequestBody InstrumentParamTemplateSaveReqVO createReqVO) {
        return success(instrumentParamTemplateService.createInstrumentParamTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器类型模板-计算参数")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-param-template:update')")
    public CommonResult<Boolean> updateInstrumentParamTemplate(@Valid @RequestBody InstrumentParamTemplateSaveReqVO updateReqVO) {
        instrumentParamTemplateService.updateInstrumentParamTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器类型模板-计算参数")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:instrument-param-template:delete')")
    public CommonResult<Boolean> deleteInstrumentParamTemplate(@RequestParam("id") Long id) {
        instrumentParamTemplateService.deleteInstrumentParamTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器类型模板-计算参数")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-param-template:query')")
    public CommonResult<InstrumentParamTemplateRespVO> getInstrumentParamTemplate(@RequestParam("id") Long id) {
        InstrumentParamTemplateDO instrumentParamTemplate = instrumentParamTemplateService.getInstrumentParamTemplate(id);
        return success(BeanUtils.toBean(instrumentParamTemplate, InstrumentParamTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器类型模板-计算参数分页")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-param-template:query')")
    public CommonResult<PageResult<InstrumentParamTemplateRespVO>> getInstrumentParamTemplatePage(@Valid InstrumentParamTemplatePageReqVO pageReqVO) {
        PageResult<InstrumentParamTemplateDO> pageResult = instrumentParamTemplateService.getInstrumentParamTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentParamTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出仪器类型模板-计算参数 Excel")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-param-template:export')")
    public void exportInstrumentParamTemplateExcel(@Valid InstrumentParamTemplatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InstrumentParamTemplateDO> list = instrumentParamTemplateService.getInstrumentParamTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "仪器类型模板-计算参数.xls", "数据", InstrumentParamTemplateRespVO.class,
                        BeanUtils.toBean(list, InstrumentParamTemplateRespVO.class));
    }

}