package cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 公式关联测点列表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FormulaPointListVO {

    @Schema(description = "主键id", example = "15140")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "测点名称")
    @ExcelProperty("测点名称")
    private String pointName;

    @Schema(description = "仪器类型名称")
    @Length(max = 32)
    private String instrumentName;

    @Schema(description = "测量分量", example = "芋艿")
    private String thingName;

    @Schema(description = "数据范围，1：全部，2：自动化，3：人工", example = "2")
    @ExcelProperty("数据范围，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值")
    @ExcelProperty("取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值")
    private Integer dataCondition;

    @Schema(description = "数值或1：日统计值，2：月统计值")
    @ExcelProperty("数值或1：日统计值，2：月统计值")
    private Integer dataValue;

}