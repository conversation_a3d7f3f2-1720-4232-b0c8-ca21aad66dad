package cn.powerchina.bjy.link.dam.dal.mysql.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicecollectlog.DeviceCollectLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 网关设备采集日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceCollectLogMapper extends BaseMapperX<DeviceCollectLogDO> {

    default PageResult<DeviceCollectLogDO> selectPage(DeviceCollectLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceCollectLogDO>()
                .eqIfPresent(DeviceCollectLogDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(DeviceCollectLogDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceCollectLogDO::getMsgId, reqVO.getMsgId())
                .betweenIfPresent(DeviceCollectLogDO::getCollectTime, reqVO.getCollectTime())
                .betweenIfPresent(DeviceCollectLogDO::getSendTime, reqVO.getSendTime())
                .betweenIfPresent(DeviceCollectLogDO::getBackTime, reqVO.getBackTime())
                .eqIfPresent(DeviceCollectLogDO::getCollectStatus, reqVO.getCollectStatus())
                .betweenIfPresent(DeviceCollectLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceCollectLogDO::getId));
    }

    int updateCollectLogToSend(@Param("id") Long id, @Param("collectStatus") Integer collectStatus,
                               @Param("collectStatusTo") Integer collectStatusTo, @Param("sendTime") Date sendTime);
}