package cn.powerchina.bjy.link.dam.service.instrumentparamtemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparamtemplate.InstrumentParamTemplateDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentparamtemplate.InstrumentParamTemplateMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumenttemplate.InstrumentTemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INSTRUMENT_NOT_EXISTS;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INSTRUMENT_PARAM_TEMPLATE_NOT_EXISTS;


/**
 * 仪器类型模板-计算参数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentParamTemplateServiceImpl implements InstrumentParamTemplateService {

    @Resource
    private InstrumentParamTemplateMapper instrumentParamTemplateMapper;

    @Resource
    private InstrumentTemplateMapper instrumentTemplateMapper;

    @Override
    public Long createInstrumentParamTemplate(InstrumentParamTemplateSaveReqVO createReqVO) {
        // 插入
        InstrumentParamTemplateDO instrumentParamTemplate = BeanUtils.toBean(createReqVO, InstrumentParamTemplateDO.class);
        instrumentParamTemplateMapper.insert(instrumentParamTemplate);
        // 返回
        return instrumentParamTemplate.getId();
    }

    @Override
    public void updateInstrumentParamTemplate(InstrumentParamTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateInstrumentParamTemplateExists(updateReqVO.getId());
        // 更新
        InstrumentParamTemplateDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentParamTemplateDO.class);
        instrumentParamTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteInstrumentParamTemplate(Long id) {
        // 校验存在
        validateInstrumentParamTemplateExists(id);
        // 删除
        instrumentParamTemplateMapper.deleteById(id);
    }

    private void validateInstrumentParamTemplateExists(Long id) {
        if (instrumentParamTemplateMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_PARAM_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public InstrumentParamTemplateDO getInstrumentParamTemplate(Long id) {
        return instrumentParamTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<InstrumentParamTemplateDO> getInstrumentParamTemplatePage(InstrumentParamTemplatePageReqVO pageReqVO) {
        return instrumentParamTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InstrumentParamTemplateDO> getListByInstrumentId(long templateId) {
        validateInstrumentExists(templateId);
        return instrumentParamTemplateMapper.selectList(new LambdaQueryWrapperX<InstrumentParamTemplateDO>().
                eq(InstrumentParamTemplateDO::getInstrumentId, templateId).orderByAsc(InstrumentParamTemplateDO::getThingWeight));
    }

    private void validateInstrumentExists(Long id) {
        if (instrumentTemplateMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
    }
}