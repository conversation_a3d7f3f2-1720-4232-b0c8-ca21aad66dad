package cn.powerchina.bjy.link.dam.framework.rpc.config;

import cn.powerchina.bjy.cloud.infra.api.config.ConfigApi;
import cn.powerchina.bjy.cloud.infra.api.logger.ApiAccessLogApi;
import cn.powerchina.bjy.cloud.system.api.dict.DictDataApi;
import cn.powerchina.bjy.cloud.system.api.menu.MenuApi;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.link.iot.api.device.DeviceApi;
import cn.powerchina.bjy.link.iot.api.devicegroup.DeviceGroupApi;
import cn.powerchina.bjy.link.iot.api.devicegroupdetail.DeviceGroupDetailApi;
import cn.powerchina.bjy.link.iot.api.product.ProductApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/*@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {ConfigApi.class, AdminUserApi.class, RoleApi.class, PermissionApi.class, ApiAccessLogApi.class,
        MenuApi.class, DeviceGroupApi.class, ProductApi.class, DictDataApi.class, DeviceGroupDetailApi.class, DeviceApi.class,})*/
public class RpcConfiguration {
}
