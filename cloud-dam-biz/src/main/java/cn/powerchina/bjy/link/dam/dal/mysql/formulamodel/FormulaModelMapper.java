package cn.powerchina.bjy.link.dam.dal.mysql.formulamodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 公式关联分量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FormulaModelMapper extends BaseMapperX<FormulaModelDO> {

    default PageResult<FormulaModelDO> selectPage(FormulaModelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FormulaModelDO>()
                .eqIfPresent(FormulaModelDO::getPointFormulaId, reqVO.getPointFormulaId())
                .eqIfPresent(FormulaModelDO::getInstrumentModelId, reqVO.getInstrumentModelId())
                .eqIfPresent(FormulaModelDO::getDataCondition, reqVO.getDataCondition())
                .eqIfPresent(FormulaModelDO::getDataValue, reqVO.getDataValue())
                .eqIfPresent(FormulaModelDO::getDataUnit, reqVO.getDataUnit())
                .betweenIfPresent(FormulaModelDO::getSpecifyTime, reqVO.getSpecifyTime())
                .betweenIfPresent(FormulaModelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FormulaModelDO::getId));
    }

}