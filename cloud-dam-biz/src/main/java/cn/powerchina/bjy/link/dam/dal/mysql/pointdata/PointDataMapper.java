package cn.powerchina.bjy.link.dam.dal.mysql.pointdata;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 测点数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointDataMapper extends BaseMapperX<PointDataDO> {

    default PageResult<PointDataDO> selectPage(PointDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PointDataDO>()
                .eqIfPresent(PointDataDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(PointDataDO::getDataType, reqVO.getDataType())
                .orderByDesc(PointDataDO::getId));
    }

    /**
     * 批量逻辑删除
     *
     * @param pointId
     * @param startTime
     * @param endTime
     * @param dataType
     */
    void batchUpdateDelete(@Param("pointId") Long pointId,
                           @Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime,
                           @Param("dataType") Integer dataType,
                           @Param("rollbackImportId") Long rollbackImportId);

    /**
     * 批量恢复
     *
     * @param pointId
     * @param startTime
     * @param endTime
     * @param dataType
     */
    void batchUpdateRecover(@Param("pointId") Long pointId,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime,
                            @Param("dataType") Integer dataType,
                            @Param("rollbackImportId") Long rollbackImportId);

    /**
     * 分组统计月份最大值、最小值、平均值、测量次数
     *
     * @param pointId
     * @param instrumentModelId
     * @param startTime
     * @param endTime
     * @param
     * @return
     */
    List<PointDataBO> yearAndMonthStatistic(@Param("pointId") Long pointId,
                                            @Param("instrumentModelId") Long instrumentModelId,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime,
                                            @Param("dataTypeList") List<Integer> dataTypeList,
                                            @Param("useAbsoluteValue") boolean useAbsoluteValue,@Param("dataStatusList") List<Integer> dataStatusList);

    List<PointDataBO> yearStatistic(@Param("pointId") Long pointId,
                                    @Param("instrumentModelId") Long instrumentModelId,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime,
                                    @Param("dataTypeList") List<Integer> dataTypeList,
                                    @Param("useAbsoluteValue") boolean useAbsoluteValue,@Param("dataStatusList") List<Integer> dataStatusList);

    /**
     * 查询组内最大值、最小值监测日期
     *
     * @param pointId
     * @param instrumentModelId
     * @param startTime
     * @param endTime
     * @param
     * @param isMax
     * @return
     */
    List<Map<String, Object>> selectYearValuePointTime(@Param("pointId") Long pointId,
                                                       @Param("instrumentModelId") Long instrumentModelId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime,
                                                       @Param("dataTypeList") List<Integer> dataTypeList,
                                                       @Param("isMax") Integer isMax,
                                                       @Param("useAbsoluteValue") boolean useAbsoluteValue,@Param("dataStatusList") List<Integer> dataStatusList);

    List<Map<String, Object>> selectYearAndMonthValuePointTime(@Param("pointId") Long pointId,
                                                               @Param("instrumentModelId") Long instrumentModelId,
                                                               @Param("startTime") LocalDateTime startTime,
                                                               @Param("endTime") LocalDateTime endTime,
                                                               @Param("dataTypeList") List<Integer> dataTypeList,
                                                               @Param("isMax") Integer isMax,
                                                               @Param("useAbsoluteValue") boolean useAbsoluteValue,@Param("dataStatusList") List<Integer> dataStatusList);

}