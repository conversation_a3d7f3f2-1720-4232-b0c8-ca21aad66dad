package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice.IotDeviceDO;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.iotdevice.IotDeviceService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceSave;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 设备更新消息处理器
 */
@Component
public class DeviceUpdateMsgProcessor implements MsgProcessor {

    @Autowired
    private IotDeviceService iotDeviceService;

    @Autowired
    private DeviceService deviceService;

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    @Transactional
    public void process(String payload) {
        MqttReceiveData<DeviceSave> deviceData = JsonUtils.parseObject(payload, new TypeReference<MqttReceiveData<DeviceSave>>(){});
        DeviceSave deviceSave = deviceData.getMessage();

        // 更新物联网平台同步的设备
        IotDeviceDO iotDeviceDO = iotDeviceService.getByIotId(deviceSave.getId());
        if (Objects.nonNull(iotDeviceDO)) {
            if (Objects.nonNull(deviceSave.getId())) {
                iotDeviceDO.setIotId(deviceSave.getId());
            }
            if (StringUtils.isNotBlank(deviceSave.getProductCode())) {
                iotDeviceDO.setProductCode(deviceSave.getProductCode());
            }
            //iotDeviceDO.setProjectCode(deviceSave.getProjectCode());
            if (StringUtils.isNotBlank(deviceSave.getDeviceName())) {
                iotDeviceDO.setDeviceName(deviceSave.getDeviceName());
            }
            //iotDeviceDO.setDeviceCode(deviceSave.getDeviceCode());
            if (StringUtils.isNotBlank(deviceSave.getParentCode())) {
                iotDeviceDO.setParentCode(deviceSave.getParentCode());
            }
            //iotDeviceDO.setParentName(deviceSave.getParentName());
            if (StringUtils.isNotBlank(deviceSave.getDeviceSerial())) {
                iotDeviceDO.setDeviceSerial(deviceSave.getDeviceSerial());
            }
            if (Objects.nonNull(deviceSave.getShadow())) {
                iotDeviceDO.setShadow(deviceSave.getShadow() ? 1 : 0);
            }
            if (StringUtils.isNotBlank(deviceSave.getChannelCode())) {
                iotDeviceDO.setChannelCode(deviceSave.getChannelCode());
            }
            if (StringUtils.isNotBlank(deviceSave.getMcuChannel())) {
                iotDeviceDO.setMcuChannel(deviceSave.getMcuChannel());
            }
            if (StringUtils.isNotBlank(deviceSave.getSlaveId())) {
                iotDeviceDO.setSlaveId(deviceSave.getSlaveId());
            }
            //iotDeviceDO.setDistributeState(deviceSave.getDistributeState());
            //iotDeviceDO.setLinkState(deviceSave.getLinkState());
            //iotDeviceDO.setRegisterState(deviceSave.getRegisterState());
            if (Objects.nonNull(deviceSave.getNodeType())) {
                iotDeviceDO.setNodeType(deviceSave.getNodeType());
            }
            //iotDeviceDO.setExtra(deviceSave.getExtra());

            //LocalDateTime lastUpTime = Optional.ofNullable(deviceSave.getLastUpTime()).map(LocalDateTime::parse)
            //        .orElse(null);
            //iotDeviceDO.setLastUpTime(lastUpTime);

            if (StringUtils.isNotBlank(deviceSave.getEdgeCode())) {
                iotDeviceDO.setEdgeCode(deviceSave.getEdgeCode());
            }
            if (StringUtils.isNotBlank(deviceSave.getDriverCode())) {
                iotDeviceDO.setDriverCode(deviceSave.getDriverCode());
            }
            //iotDeviceDO.setProductName(deviceSave.getProductName());
            if (StringUtils.isNotBlank(deviceSave.getRemark())) {
                iotDeviceDO.setRemark(deviceSave.getRemark());
            }
            //iotDeviceDO.setFirmName(deviceSave.getFirmName());
            //iotDeviceDO.setProductModel(deviceSave.getProductModel());
            if (Objects.nonNull(deviceSave.getResourceSpaceId())) {
                iotDeviceDO.setResourceSpaceId(deviceSave.getResourceSpaceId());
            }
            //iotDeviceDO.setSpaceName(deviceSave.getSpaceName());
            if (StringUtils.isNotBlank(deviceSave.getLongitude())) {
                iotDeviceDO.setLongitude(deviceSave.getLongitude());
            }
            if (StringUtils.isNotBlank(deviceSave.getLatitude())) {
                iotDeviceDO.setLatitude(deviceSave.getLatitude());
            }
            if (StringUtils.isNotBlank(deviceSave.getCreateTime())) {
                iotDeviceDO.setIotCreateTime(LocalDateTime.parse(deviceSave.getCreateTime()));
            }
            if (StringUtils.isNotBlank(deviceSave.getRegisterTime())) {
                iotDeviceDO.setRegisterTime(LocalDateTime.parse(deviceSave.getRegisterTime()));
            }
            iotDeviceService.updateByIotId(iotDeviceDO);

            // 更新大坝设备
            DeviceDO deviceDO = deviceService.getDeviceDOByDeviceCode(iotDeviceDO.getDeviceCode());
            if (Objects.nonNull(deviceDO)) {
                if (StringUtils.isNotBlank(deviceSave.getParentCode())) {
                    deviceDO.setParentCode(deviceSave.getParentCode());

                    DeviceDO parentDeviceDO = Optional.ofNullable(deviceService.getDeviceDOByDeviceCode(
                            deviceSave.getParentCode())).orElse(new DeviceDO());
                    deviceDO.setParentName(parentDeviceDO.getDeviceName());
                    deviceDO.setParentProductCode(parentDeviceDO.getProductCode());
                    deviceDO.setParentSerial(parentDeviceDO.getDeviceSerial());
                }
                if (StringUtils.isNotBlank(deviceSave.getProductCode())) {
                    deviceDO.setProductCode(deviceSave.getProductCode());
                }
                if (StringUtils.isNotBlank(deviceSave.getDeviceName())) {
                    deviceDO.setDeviceName(deviceSave.getDeviceName());
                }
                if (StringUtils.isNotBlank(deviceSave.getMcuChannel())) {
                    deviceDO.setMcuChannel(deviceSave.getMcuChannel());
                }
                if (StringUtils.isNotBlank(deviceSave.getDeviceSerial())) {
                    deviceDO.setDeviceSerial(deviceSave.getDeviceSerial());
                }
                if (Objects.nonNull(deviceSave.getNodeType())) {
                    deviceDO.setNodeType(deviceSave.getNodeType());
                }

                // 当前设备的子设备信息也要改
                List<DeviceDO> deviceDOList = deviceService.listByParentCode(deviceDO.getDeviceCode());
                deviceDOList.forEach(item -> {
                    item.setParentName(deviceDO.getDeviceName());
                    item.setParentProductCode(deviceDO.getProductCode());
                    item.setParentSerial(deviceDO.getDeviceSerial());
                });

                // 将当前设备与子设备放到一个集合中，批量更新
                List<DeviceDO> updateList = new ArrayList<>();
                updateList.add(deviceDO);
                updateList.addAll(deviceDOList);
                deviceService.updateBatch(updateList);
            }
        }
    }

    /**
     * 获取当前消息处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.DEVICE_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.DEVICE_UPDATE.getDataType();
    }
}
