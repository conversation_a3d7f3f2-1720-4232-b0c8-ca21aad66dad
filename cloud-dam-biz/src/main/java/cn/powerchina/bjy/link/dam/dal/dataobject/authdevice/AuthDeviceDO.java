package cn.powerchina.bjy.link.dam.dal.dataobject.authdevice;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 设备授权 DO
 *
 * <AUTHOR>
 */
@TableName("dam_auth_device")
@KeySequence("dam_auth_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthDeviceDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 资源空间id
     */
    private Long resourceSpaceId;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 设备分组id
     */
    private Long deviceGroupId;

}