package cn.powerchina.bjy.link.dam.controller.admin.user.bo;

import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserRespVO;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 描述
 * @Author: zhaoqi<PERSON>
 * @CreateDate: 2024/8/29
 */
@Data
public class UserBO extends UserRespVO {

    @Schema(description = "所属项目:当前项目名")
    @ExcelProperty("所属项目")
    private String projectName;

    @Schema(description = "角色id集合")
    private List<Long> roleIds = new ArrayList<>();

    private Long projectId;
}
