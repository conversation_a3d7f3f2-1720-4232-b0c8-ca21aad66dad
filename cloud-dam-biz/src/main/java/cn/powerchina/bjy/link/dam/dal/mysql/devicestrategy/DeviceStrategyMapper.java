package cn.powerchina.bjy.link.dam.dal.mysql.devicestrategy;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategyPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy.DeviceStrategyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备采集策略 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceStrategyMapper extends BaseMapperX<DeviceStrategyDO> {

    default PageResult<DeviceStrategyDO> selectPage(DeviceStrategyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceStrategyDO>()
                .eqIfPresent(DeviceStrategyDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(DeviceStrategyDO::getStrategyName, reqVO.getStrategyName())
                .eqIfPresent(DeviceStrategyDO::getStrategyType, reqVO.getStrategyType())
                .eqIfPresent(DeviceStrategyDO::getTimeInterval, reqVO.getTimeInterval())
                .eqIfPresent(DeviceStrategyDO::getTimePoint, reqVO.getTimePoint())
                .betweenIfPresent(DeviceStrategyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceStrategyDO::getId));
    }

}