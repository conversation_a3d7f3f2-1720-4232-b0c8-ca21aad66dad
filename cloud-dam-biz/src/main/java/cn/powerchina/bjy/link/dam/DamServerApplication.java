package cn.powerchina.bjy.link.dam;


import org.apache.rocketmq.client.autoconfigure.RocketMQAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@Import(RocketMQAutoConfiguration.class)
@EnableFeignClients(basePackages = {"cn.powerchina.bjy.cloud", "cn.powerchina.bjy.link"})
public class DamServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(DamServerApplication.class, args);
    }

}
