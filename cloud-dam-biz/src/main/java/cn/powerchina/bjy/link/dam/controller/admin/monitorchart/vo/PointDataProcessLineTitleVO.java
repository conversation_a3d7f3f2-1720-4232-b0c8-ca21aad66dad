package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-过程线--标题
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "标题")
@Data
public class PointDataProcessLineTitleVO {

    private Long id;

    @Schema(description = "标题内容")
    private String text;

    @Schema(description = "left")
    private String left;

    @Schema(description = "top")
    private String top;

}
