package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 测点数据人工录入基础信息和分量列表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointInformationVO {

    @Schema(description = "测点id", example = "24279")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "测点编号", example = "24279")
    @ExcelProperty("测点编号")
    private String pointCode;

    @Schema(description = "仪器类型id", example = "3871")
    private Long instrumentId;

    @Schema(description = "仪器类型名称")
    private String instrumentName;

    @Schema(description = "录入人id")
    private String creator;

    @Schema(description = "录入人")
    private String creatorName;

    @Schema(description = "测点分量")
    List<InstrumentModelDO> instrumentModelDOList;

}