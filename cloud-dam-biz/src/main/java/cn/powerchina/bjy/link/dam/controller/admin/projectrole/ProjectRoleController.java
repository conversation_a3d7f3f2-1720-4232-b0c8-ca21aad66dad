package cn.powerchina.bjy.link.dam.controller.admin.projectrole;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRolePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRoleRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRoleSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.RoleMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectrole.ProjectRoleDO;
import cn.powerchina.bjy.link.dam.service.projectrole.ProjectRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 项目角色信息")
@RestController
@RequestMapping("/dam/project/role")
@Validated
public class ProjectRoleController {

    @Resource
    private ProjectRoleService projectRoleService;

    @PostMapping("/create")
    @Operation(summary = "创建项目角色信息")
    // @PreAuthorize("@ss.hasPermission('dam:project-role:create')")
    public CommonResult<Long> createProjectRole(@Valid @RequestBody ProjectRoleSaveReqVO createReqVO) {
        return success(projectRoleService.createProjectRole(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新项目角色信息")
    // @PreAuthorize("@ss.hasPermission('dam:project-role:update')")
    public CommonResult<Boolean> updateProjectRole(@Valid @RequestBody ProjectRoleSaveReqVO updateReqVO) {
        projectRoleService.updateProjectRole(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除项目角色信息")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('dam:project-role:delete')")
    public CommonResult<Boolean> deleteProjectRole(@RequestParam("id") Long id) {
        projectRoleService.deleteProjectRole(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目角色信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //  @PreAuthorize("@ss.hasPermission('dam:project-role:query')")
    public CommonResult<ProjectRoleRespVO> getProjectRole(@RequestParam("id") Long id) {
        ProjectRoleDO projectRole = projectRoleService.getProjectRole(id);
        return success(BeanUtils.toBean(projectRole, ProjectRoleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得项目角色信息分页")
    //@PreAuthorize("@ss.hasPermission('dam:project-role:query')")
    public CommonResult<PageResult<ProjectRoleRespVO>> getProjectRolePage(@Valid ProjectRolePageReqVO pageReqVO) {
        PageResult<ProjectRoleRespVO> pageResult = projectRoleService.getProjectRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProjectRoleRespVO.class));
    }

    @PostMapping("/save/menu")
    @Operation(summary = "保存角色菜单")
//    @PreAuthorize("@ss.hasPermission('dam:project-role:create')")
    public CommonResult<Long> createProjectMenu(@Valid @RequestBody RoleMenuSaveReqVO createReqVO) {
        return success(projectRoleService.saveRoleMenu(createReqVO));
    }

}