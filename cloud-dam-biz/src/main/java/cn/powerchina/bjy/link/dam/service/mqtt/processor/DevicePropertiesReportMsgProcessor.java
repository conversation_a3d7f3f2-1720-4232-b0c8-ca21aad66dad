package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointDataJsonSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice.IotDeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointevaluate.PointEvaluateMapper;
import cn.powerchina.bjy.link.dam.enums.DataStatusEnum;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.iotdevice.IotDeviceService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceProperty;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DevicePropertyService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceStatue;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.InstrumentModel;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备属性上报消息处理器
 */
@Slf4j
@Component
public class DevicePropertiesReportMsgProcessor implements MsgProcessor {

    @Autowired
    private PointEvaluateMapper pointEvaluateMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private PointService pointService;

    @Autowired
    private InstrumentService instrumentService;

    @Autowired
    private InstrumentModelService instrumentModelService;

    @Autowired
    private PointDataService pointDataService;

    @Autowired
    private PointDataJsonService pointDataJsonService;


    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    public void process(String payload) {
        MqttReceiveData<DeviceProperty> propertyData = JsonUtils.parseObject(payload, new TypeReference<MqttReceiveData<DeviceProperty>>(){});
        DeviceProperty deviceProperty = propertyData.getMessage();
        // 接到得设备编码
        String deviceCode = deviceProperty.getDeviceCode();
        // 产品编码
        String productCode = deviceProperty.getProductCode();
        // 接到得属性上报的值
        List<DevicePropertyService> devicePropertyServiceList = deviceProperty.getServices();

        // 根据设备编码获取设备详细信息
        DeviceDO deviceDO = deviceService.getDeviceDOByDeviceCode(deviceCode);
        if (Objects.isNull(deviceDO)) {
            log.error("设备属性上报消息处理器，根据设备编码获取设备详细信息失败，设备编码：{}", deviceCode);
            return;
        }

        // 根据设备绑定的测点获取测点详细信息
        PointDO pointDO = pointService.getPoint(deviceDO.getPointId());
        if (Objects.isNull(pointDO)) {
            log.error("设备属性上报消息处理器，根据测点id获取测点详细信息失败，测点id：{}", deviceDO.getPointId());
            return;
        }

        // 根据测点关联的仪器类型获取仪器类型详细信息
        InstrumentDO instrumentDO = instrumentService.getInstrument(pointDO.getInstrumentId());
        if (Objects.isNull(instrumentDO)) {
            log.error("设备属性上报消息处理器，根据仪器类型id获取仪器类型详细信息失败，仪器类型id：{}", pointDO.getInstrumentId());
            return;
        }

        // 根据仪器类型id获取分量
        List<InstrumentModelDO> instrumentModelDOList = instrumentModelService.getModelByInstrumentId(instrumentDO.getId());

        // 将分量转成map(key:iot属性标识符; value:分量信息)
        Map<String, InstrumentModelDO> instrumentModelDOMap = instrumentModelDOList.stream()
                .collect(Collectors.toMap(InstrumentModelDO::getThingIdentityIot, item -> item, (k1, k2) -> k1));

        // 获取测点评价指标
        PointEvaluateReqVO pointEvaluateReqVO = new PointEvaluateReqVO();
        pointEvaluateReqVO.setPointIdList(new ArrayList<>(Collections.singletonList(pointDO.getId())));
        pointEvaluateReqVO.setProjectId(pointDO.getProjectId());
        List<PointEvaluatePageRespVO> pointEvaluateDOS = pointEvaluateMapper.selectList1(pointEvaluateReqVO);

        // 将评价指标转成map(key:分量id; value:评价指标)
        Map<Long, PointEvaluatePageRespVO> pointEvaluateDOMap = pointEvaluateDOS.stream()
                .collect(Collectors.toMap(PointEvaluatePageRespVO::getInstrumentModelId, item -> item, (k1, k2) -> k1));

        // 创建TDengine表
        pointDataService.defineDevicePropertyData(deviceDO.getProjectId(), instrumentDO.getId());

        // 计算要保存的数据的监测时间
        LocalDateTime pointTime = null;
        Long reportTime = devicePropertyServiceList.stream().map(DevicePropertyService::getReportTime).filter(Objects::nonNull).findFirst().orElse(null);
        if (Objects.nonNull(reportTime)) {
            pointTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(reportTime), ZoneId.systemDefault());
        }

        // 计算要保存的数据的分量信息
        List<InstrumentModel> instrumentModelList = new ArrayList<>();
        devicePropertyServiceList.forEach(temp->{
            temp.getProperties().forEach((key, value) -> {
                InstrumentModelDO instrumentModelDO = instrumentModelDOMap.get(key);
                if (Objects.nonNull(instrumentModelDO)) {
                    InstrumentModel instrumentModel = new InstrumentModel();
                    instrumentModel.setInstrumentModelId(instrumentModelDO.getId());
                    instrumentModel.setThingName(instrumentModelDO.getThingName());
                    instrumentModel.setThingIdentity(instrumentModelDO.getThingIdentity());
                    instrumentModel.setThingValue(new BigDecimal(value));
                    instrumentModelList.add(instrumentModel);
                }
            });
        });

        // 计算要保存的数据的数据状态
        Integer status = DataStatusEnum.UNDETERMINED.getType();
        if (CollectionUtils.isNotEmpty(pointEvaluateDOS)) {
            status = DataStatusEnum.NORMAL.getType();
            Set<Integer> statusSet = new HashSet<>();
            instrumentModelList.forEach(item -> {
                PointEvaluatePageRespVO evaluate = pointEvaluateDOMap.get(item.getInstrumentModelId());
                if (Objects.nonNull(evaluate)) {
                    BigDecimal value = item.getThingValue();
                    if (value.compareTo(new BigDecimal(evaluate.getAbnormalUp())) > 0 ||
                            value.compareTo(new BigDecimal(evaluate.getAbnormalDown())) < 0) {
                        statusSet.add(DataStatusEnum.ERROR_DATA.getType());
                    } else if (value.compareTo(new BigDecimal(evaluate.getWaringUp())) > 0 ||
                            value.compareTo(new BigDecimal(evaluate.getWaringDown())) < 0) {
                        statusSet.add(DataStatusEnum.ANOMALOUS.getType());
                    }
                }
            });
            if (statusSet.contains(DataStatusEnum.ERROR_DATA.getType())) {
                status = DataStatusEnum.ERROR_DATA.getType();
            } else if (statusSet.contains(DataStatusEnum.ANOMALOUS.getType())) {
                status = DataStatusEnum.ANOMALOUS.getType();
            }
        }

        // 将上面计算的值组装成调用保存方法时需要的参数
        PointDataJsonSaveReqVO dataJsonSaveReqVO = new PointDataJsonSaveReqVO();
        dataJsonSaveReqVO.setProjectId(deviceDO.getProjectId());
        dataJsonSaveReqVO.setPointId(pointDO.getId());
        dataJsonSaveReqVO.setPointTime(pointTime);
        dataJsonSaveReqVO.setPointData(JSONObject.toJSONString(instrumentModelList));
        dataJsonSaveReqVO.setInstrumentId(instrumentDO.getId());
        dataJsonSaveReqVO.setDataType(1);
        //dataJsonSaveReqVO.setImportId(reqVO.getId());
        //dataJsonSaveReqVO.setUserId(String.valueOf(loginUserId));
        dataJsonSaveReqVO.setDataStatus(status);
        /*
        PointDataJsonDO pointDataJson = BeanUtils.toBean(dataJsonSaveReqVO, PointDataJsonDO.class);
        pointDataJson.setReviewer(loginUserId.toString());
        pointDataJson.setReviewer(String.valueOf(loginUserId));
        pointDataJson.setReviewName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
        */

        // 保存
        pointDataJsonService.insertPointDataUpload(dataJsonSaveReqVO);
    }

    /**
     * 获取当前消息处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.PROPERTIES_REPORT_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.PROPERTIES_REPORT.getDataType();
    }
}
