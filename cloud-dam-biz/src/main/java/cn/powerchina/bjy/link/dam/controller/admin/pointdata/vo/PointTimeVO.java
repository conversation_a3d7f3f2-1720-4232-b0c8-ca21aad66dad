package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/12/27
 */
@Data
public class PointTimeVO {


    private List<String> data;

    private Integer type;

    public PointTimeVO(List<String> data, Integer type) {
        this.data = data;
        this.type = type;
    }
}
