package cn.powerchina.bjy.link.dam.service.instrumenttemplate;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.dict.DictDataApi;
import cn.powerchina.bjy.cloud.system.api.dict.dto.DictDataRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplateSaveListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.TreeVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodeltemplate.InstrumentModelTemplateDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparamtemplate.InstrumentParamTemplateDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumenttemplate.InstrumentTemplateDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodeltemplate.InstrumentModelTemplateMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentparamtemplate.InstrumentParamTemplateMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumenttemplate.InstrumentTemplateMapper;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.instrumentmodeltemplate.InstrumentModelTemplateServiceImpl;
import cn.powerchina.bjy.link.dam.service.instrumentparamtemplate.InstrumentParamTemplateServiceImpl;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;


/**
 * 仪器类型模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentTemplateServiceImpl implements InstrumentTemplateService {

    @Resource
    private InstrumentTemplateMapper instrumentTemplateMapper;

    @Resource
    private SnowFlakeUtil snowFlakeUtil;

    @Resource
    private InstrumentModelTemplateMapper instrumentModelTemplateMapper;

    @Resource
    private InstrumentParamTemplateMapper instrumentParamTemplateMapper;

    @Resource
    private InstrumentParamTemplateServiceImpl instrumentParamTemplateService;

    @Resource
    private InstrumentModelTemplateServiceImpl instrumentModelTemplateService;

    @Resource
    private DictDataApi dictDataApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInstrumentTemplate(InstrumentTemplateSaveReqVO createReqVO) {
        // 插入
        InstrumentTemplateDO instrumentTemplate = BeanUtils.toBean(createReqVO, InstrumentTemplateDO.class);
        instrumentTemplateMapper.insert(instrumentTemplate);
        // 返回
        return instrumentTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInstrumentTemplate(InstrumentTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateInstrumentTemplateExists(updateReqVO.getId());
        // 更新
        InstrumentTemplateDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentTemplateDO.class);
        instrumentTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteInstrumentTemplate(Long id) {
        // 校验存在
        validateInstrumentTemplateExists(id);
        // 删除
        instrumentTemplateMapper.deleteById(id);
    }

    private void validateInstrumentTemplateExists(Long id) {
        if (instrumentTemplateMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public InstrumentTemplateSaveListReqVO getInstrumentTemplate(Long id) {
//        return instrumentTemplateMapper.selectById(id);
        InstrumentTemplateSaveListReqVO res = new InstrumentTemplateSaveListReqVO();
        InstrumentTemplateSaveReqVO instrumentTemplateSaveReqVO = new InstrumentTemplateSaveReqVO();
        List<InstrumentParamTemplateSaveReqVO> paramRes = null;
        List<InstrumentModelTemplateSaveReqVO> modelRes = null;
        InstrumentTemplateDO instrumentTemplateDO = instrumentTemplateMapper.selectById(id);
        List<InstrumentModelTemplateDO> instrumentModelTemplateDOS = instrumentModelTemplateMapper.selectList(new LambdaQueryWrapperX<InstrumentModelTemplateDO>().
                eq(InstrumentModelTemplateDO::getInstrumentId, id)
                .orderByAsc(InstrumentModelTemplateDO::getThingWeight));
        List<InstrumentParamTemplateDO> instrumentParamTemplateDOS = instrumentParamTemplateMapper.selectList(new LambdaQueryWrapperX<InstrumentParamTemplateDO>().
                eq(InstrumentParamTemplateDO::getInstrumentId, id)
                .orderByAsc(InstrumentParamTemplateDO::getThingWeight));
        BeanUtil.copyProperties(instrumentTemplateDO, instrumentTemplateSaveReqVO);
        if (CollectionUtils.isNotEmpty(instrumentModelTemplateDOS)) {
            modelRes = instrumentModelTemplateDOS.stream()
                    .map(doItem -> {
                        InstrumentModelTemplateSaveReqVO voItem = new InstrumentModelTemplateSaveReqVO();
                        BeanUtil.copyProperties(doItem, voItem);
                        return voItem;
                    })
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(instrumentParamTemplateDOS)) {
            paramRes = instrumentParamTemplateDOS.stream()
                    .map(doItem -> {
                        InstrumentParamTemplateSaveReqVO voItem = new InstrumentParamTemplateSaveReqVO();
                        BeanUtil.copyProperties(doItem, voItem);
                        return voItem;
                    })
                    .collect(Collectors.toList());
        }
        res.setInstrumentTemplateRespVO(instrumentTemplateSaveReqVO);
        res.setInstrumentParamTemplateSaveReqVOS(paramRes);
        res.setInstrumentModelTemplateSaveReqVOS(modelRes);
        return res;
    }

    @Override
    public PageResult<InstrumentTemplateDO> getInstrumentTemplatePage(InstrumentTemplatePageReqVO pageReqVO) {
        return instrumentTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertListInstrumentTemplate(InstrumentTemplateSaveListReqVO createReqVO) {
        InstrumentTemplateSaveReqVO respVO = createReqVO.getInstrumentTemplateRespVO();
        long templateId = snowFlakeUtil.snowflakeId();
        validateNameExists(createReqVO);
        if (Objects.nonNull(respVO.getId())) {
            updateInstrumentTemplate(respVO);
            templateId = respVO.getId();
        } else {
            respVO.setId(templateId);
            createInstrumentTemplate(respVO);
        }

        insertListParam(createReqVO.getInstrumentParamTemplateSaveReqVOS(), templateId);
        insertListModel(createReqVO.getInstrumentModelTemplateSaveReqVOS(), templateId);
        return templateId;
    }

    @Override
    public List<TreeVO> getListTree() {
        CommonResult<List<DictDataRespDTO>> dictDataList = dictDataApi.getDictDataList("measure_principle");
        List<InstrumentTemplateDO> instrumentTemplateDOS = instrumentTemplateMapper.selectList();
        List<TreeVO> treeList = new ArrayList<>();
        dictDataList.getData().stream().forEach(item -> {
            TreeVO parentVO = new TreeVO();
            parentVO.setId(item.getValue());
            parentVO.setName(item.getLabel());
            treeList.add(parentVO);
            Stream<InstrumentTemplateDO> childList = instrumentTemplateDOS.stream().filter(template -> String.valueOf(template.getMeasurePrinciple()).equals(item.getValue()));
            childList.forEach(template -> {
                TreeVO childVO = new TreeVO();
                childVO.setId(String.valueOf(template.getId()));
                childVO.setName(template.getInstrumentName());
                childVO.setParentId(item.getValue());
                treeList.add(childVO);
            });
        });

        return treeList;

    }

    @Transactional(rollbackFor = Exception.class)
    void insertListModel(List<InstrumentModelTemplateSaveReqVO> instrumentModelTemplateSaveReqVOS, long templateId) {
        List<Long> oldIdList = new ArrayList<>();
        List<InstrumentModelTemplateDO> oldParamList = instrumentModelTemplateService.getModelByInstrumentId(templateId);
        if (!org.springframework.util.CollectionUtils.isEmpty(oldParamList)) {
            oldIdList = oldParamList.stream().map(InstrumentModelTemplateDO::getId).collect(Collectors.toList());
        }
        instrumentModelTemplateSaveReqVOS.forEach(req ->
                req.setInstrumentId(req.getInstrumentId() == null ? templateId : req.getInstrumentId())
        );
        for (InstrumentModelTemplateSaveReqVO reqVO : instrumentModelTemplateSaveReqVOS) {
            InstrumentModelTemplateDO model = BeanUtils.toBean(reqVO, InstrumentModelTemplateDO.class);
            if (Objects.isNull(reqVO.getId())) {
                instrumentModelTemplateMapper.insert(model);
            } else {
                instrumentModelTemplateMapper.updateById(model);
                oldIdList.remove(model.getId());
            }
        }
        //删除
        if (!org.springframework.util.CollectionUtils.isEmpty(oldIdList)) {
            instrumentModelTemplateMapper.deleteBatchIds(oldIdList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    void insertListParam(List<InstrumentParamTemplateSaveReqVO> instrumentParamTemplateSaveReqVOS, long templateId) {

        List<Long> oldIdList = new ArrayList<>();
        List<InstrumentParamTemplateDO> oldParamList = instrumentParamTemplateService.getListByInstrumentId(templateId);
        if (!org.springframework.util.CollectionUtils.isEmpty(oldParamList)) {
            oldIdList = oldParamList.stream()
                    .map(InstrumentParamTemplateDO::getId)
                    .collect(Collectors.toList());
        }

        instrumentParamTemplateSaveReqVOS.forEach(req ->
                req.setInstrumentId(req.getInstrumentId() == null ? templateId : req.getInstrumentId())
        );
        for (InstrumentParamTemplateSaveReqVO reqVO : instrumentParamTemplateSaveReqVOS) {
            InstrumentParamTemplateDO param = BeanUtils.toBean(reqVO, InstrumentParamTemplateDO.class);
            if (Objects.isNull(reqVO.getId())) {
                instrumentParamTemplateMapper.insert(param);
            } else {
                instrumentParamTemplateMapper.updateById(param);
                oldIdList.remove(param.getId());
            }
        }
        //删除
        if (!org.springframework.util.CollectionUtils.isEmpty(oldIdList)) {
            instrumentParamTemplateMapper.deleteBatchIds(oldIdList);
        }
    }

    //校验参数是否存在
    private void validateNameExists(InstrumentTemplateSaveListReqVO reqVO) {
        String instrumentName = reqVO.getInstrumentTemplateRespVO().getInstrumentName();
        chackParamAndModelRepeat(reqVO.getInstrumentParamTemplateSaveReqVOS(), reqVO.getInstrumentModelTemplateSaveReqVOS());
        InstrumentTemplateDO instrumentTemplateDO = instrumentTemplateMapper.selectOne(new LambdaQueryWrapperX<InstrumentTemplateDO>().eq(InstrumentTemplateDO::getInstrumentName, instrumentName));
        if (Objects.nonNull(instrumentTemplateDO)
                && (!instrumentTemplateDO.getId().equals(reqVO.getInstrumentTemplateRespVO().getId()))) {
            throw exception(ErrorCodeConstants.INSTRUMENT_TEMPLATE_HAS_EXISTS);
        }
        List<InstrumentParamTemplateSaveReqVO> paramReq = reqVO.getInstrumentParamTemplateSaveReqVOS();
        List<InstrumentModelTemplateSaveReqVO> modeReq = reqVO.getInstrumentModelTemplateSaveReqVOS();
        for (InstrumentParamTemplateSaveReqVO param : paramReq) {
            if (Objects.nonNull(param.getInstrumentId())
                    && Objects.isNull(param.getId())) {//子表新增
                validateNameExists(param.getId(), param.getInstrumentId(), param.getThingName());
                validateThingIdentityExists(param.getId(), param.getInstrumentId(), param.getThingIdentity());
            } else if (Objects.nonNull(param.getInstrumentId())
                    && Objects.nonNull(param.getId())) {//子表修改
                validateInstrumentParamExists(param.getId());
            }
        }
        for (InstrumentModelTemplateSaveReqVO mode : modeReq) {
            if (Objects.nonNull(mode.getInstrumentId())
                    && Objects.isNull(mode.getId())) {//子表新增
                validateNameExists(mode.getId(), mode.getInstrumentId(), mode.getThingName());
                validateThingIdentityExists(mode.getId(), mode.getInstrumentId(), mode.getThingIdentity());
            } else if (Objects.nonNull(mode.getInstrumentId())
                    && Objects.nonNull(mode.getId())) {//子表修改
                validateInstrumentModelExists(mode.getId());
            }
        }

    }

    private void chackParamAndModelRepeat(List<InstrumentParamTemplateSaveReqVO> paramList, List<InstrumentModelTemplateSaveReqVO> modelList) {

        // 提取并检查第一个集合的 thingName 唯一性
        Set<String> paramThingNames = new HashSet<>();
        Set<String> paramThingIdentity = new HashSet<>();
        if (null != paramList) {
            for (InstrumentParamTemplateSaveReqVO vo : paramList) {
                String thingName = vo.getThingName();
                String thingIdentity = vo.getThingIdentity();
                if (thingName == null || !paramThingNames.add(thingName)) {
                    throw exception(INSTRUMENT_PARAMS_NAME_EXISTS);
                }
                if (thingName == null || !paramThingIdentity.add(thingIdentity)) {
                    throw exception(INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS);
                }
            }
        }

        // 提取并检查第二个集合的 thingName 唯一性
        Set<String> modelThingNames = new HashSet<>();
        Set<String> modelThingIdentity = new HashSet<>();
        if (null != modelList) {
            for (InstrumentModelTemplateSaveReqVO vo : modelList) {
                String thingName = vo.getThingName();
                String thingIdentity = vo.getThingIdentity();
                if (thingName == null || !modelThingNames.add(thingName)) {
                    throw exception(INSTRUMENT_MODEL_NAME_EXISTS);
                }
                if (thingName == null || !modelThingIdentity.add(thingIdentity)) {
                    throw exception(INSTRUMENT_MODEL_THING_IDENTITY_EXISTS);
                }
            }
        }

        // 检查两个集合的交集
        paramThingNames.retainAll(modelThingNames);
        paramThingIdentity.retainAll(modelThingIdentity);
        if (!paramThingNames.isEmpty() || !paramThingIdentity.isEmpty()) {
            throw exception(PARAM_MODEL_REPEAT);
        }
    }

    private void validateInstrumentParamExists(Long id) {
        InstrumentParamTemplateDO instrumentModelDO = instrumentParamTemplateMapper.selectById(id);
        if (instrumentModelDO == null) {
            throw exception(INSTRUMENT_MODEL_NOT_EXISTS);
        }
    }

    private void validateInstrumentModelExists(Long id) {
        InstrumentModelTemplateDO instrumentModelDO = instrumentModelTemplateMapper.selectById(id);
        if (instrumentModelDO == null) {
            throw exception(INSTRUMENT_MODEL_NOT_EXISTS);
        }
    }

    /**
     * 分量名称是否存在
     *
     * @param id
     * @param name
     */
    public void validateNameExists(Long id, Long instrumentId, String name) {
        InstrumentModelTemplateDO instrumentModelDO = instrumentModelTemplateMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelTemplateDO>().
                eq(InstrumentModelTemplateDO::getThingName, name).eq(InstrumentModelTemplateDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO)) {
            throw exception(INSTRUMENT_MODEL_NAME_EXISTS);
        }

        InstrumentParamTemplateDO instrumentModelDO1 = instrumentParamTemplateMapper.selectOne(new LambdaQueryWrapperX<InstrumentParamTemplateDO>().
                eq(InstrumentParamTemplateDO::getThingName, name).eq(InstrumentParamTemplateDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO1)) {
            throw exception(INSTRUMENT_PARAMS_NAME_EXISTS);
        }
    }

    /**
     * 标识符是否存在
     *
     * @param id
     * @param instrumentId
     * @param thingIdentity
     */
    public void validateThingIdentityExists(Long id, Long instrumentId, String thingIdentity) {
        InstrumentModelTemplateDO instrumentModelDO = instrumentModelTemplateMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelTemplateDO>().
                eq(InstrumentModelTemplateDO::getThingIdentity, thingIdentity).eq(InstrumentModelTemplateDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentModelDO.getId()))) {
            throw exception(INSTRUMENT_MODEL_THING_IDENTITY_EXISTS);
        }

        InstrumentParamTemplateDO instrumentModelDO1 = instrumentParamTemplateMapper.selectOne(new LambdaQueryWrapperX<InstrumentParamTemplateDO>().
                eq(InstrumentParamTemplateDO::getThingIdentity, thingIdentity).eq(InstrumentParamTemplateDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentModelDO1.getId()))) {
            throw exception(INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS);
        }
    }
}