package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/10
 */
@Schema(description = "管理后台 - 工程分类管理list Response VO")
@Data
public class ProjectCategoryListReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "请选择一个项目")
    private Long projectId;

    @Schema(description = "节点id")
    private Long categoryId;

    @Schema(description = "节点类型(1：监测站点，2：工程结构，3：查询测点信息管理树，4：查询单个仪器类型下的分组树，5：测点，6：网络拓扑")
    @NotNull(message = "请选择一个节点类型")
    private Integer categoryType;

    @Schema(description = "是否筛选未绑定测点，0：不过滤，1：过滤")
    private Integer filterPoint = 0;

    @Schema(description = "测量类型，1：人/自一体，2：自动化，3：人工")
    private List<Integer> pointTypeList;
}
