package cn.powerchina.bjy.link.dam.service.mqtt.bo.product;

import lombok.Data;

/**
 * @Description: 产品物模型
 * @Author: AI Assistant
 * @CreateDate: 2025/7/23
 */
@Data
public class ProductModel {

    /**
     * 物联网平台存储的产品物模型主键
     */
    private Long id;

    /**
     * 产品物模型操作类型
     * products_model_create: 新增
     * products_model_update: 修改
     * products_model_delete: 删除
     */
    private String modelDataType;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 物模型标识符
     */
    private String thingIdentity;

    /**
     * 物模型名称
     */
    private String thingName;

    /**
     * 物模型类型
     * 1:属性; 2:服务; 3:事件
     */
    private Integer thingType;

    /**
     * 数据类型
     * 可取的值包括(integer、decimal、string、bool、array、enum)
     */
    private String datatype;

    /**
     * 读写类型
     * 1:读写; 2:只读; 3:只写
     * thingType为1时必填
     */
    private Integer readWriteType;

    /**
     * 事件类型
     * 1:信息; 2:告警; 3:故障
     * thingType为3时必填
     */
    private Integer eventType;

    /**
     * 输入参数
     */
    private String inputParams;

    /**
     * 输出参数
     */
    private String outputParams;

    /**
     * 属性扩展信息
     */
    private String extra;

    /**
     * 描述
     */
    private String remark;

    /**
     * 创建时间
     * 时间戳
     */
    private String createTime;
}
