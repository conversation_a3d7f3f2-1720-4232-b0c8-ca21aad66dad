package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;
import org.apache.commons.collections4.ListUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点计算参数新增/修改 Request VO")
@Data
public class PointParamSaveReqVO {

    @Schema(description = "主键id", example = "25970")
    private Long id;

    @Schema(description = "项目id", example = "20428")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "测点id", example = "10551")
    @NotNull(message = "测点不能为空")
    private Long pointId;

    @Schema(description = "测点参数")
    @NotNull(message = "测点参数不能为空")
    private String calcParam;

    @Schema(description = "有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    @NotNull(message = "适用类型不能为空")
    private Integer applyType;

    public static void main(String[] args) {
        String json="[{\"type\\\":0,\"datatype\\\":\\\"DATE\\\",\\\"required\\\":1,\\\"templateType\\\":2,\\\"templateIdentity\\\":\\\"3333333\\\",\\\"templateDetailsName\\\":\\\"3333333\\\"},{\\\"type\\\":0,\\\"datatype\\\":\\\"DATE\\\",\\\"required\\\":1,\\\"templateType\\\":2,\\\"templateIdentity\\\":\\\"3333333\\\",\\\"templateDetailsName\\\":\\\"3333333\\\"},{\\\"datatype\\\":\\\"DATE\\\",\\\"required\\\":1,\\\"templateDetailsName\\\":\\\"33334\\\",\\\"templateIdentity\\\":\\\"4\\\",\\\"type\\\":0,\\\"templateType\\\":2},{\\\"datatype\\\":\\\"DATE\\\",\\\"required\\\":1,\\\"templateDetailsName\\\":\\\"33334\\\",\\\"templateIdentity\\\":\\\"5\\\",\\\"type\\\":0,\\\"templateType\\\":2},{\\\"datatype\\\":\\\"DATE\\\",\\\"required\\\":1,\\\"templateDetailsName\\\":\\\"5\\\",\\\"templateIdentity\\\":\\\"56\\\",\\\"type\\\":0,\\\"templateType\\\":2},{\\\"datatype\\\":\\\"DATE\\\",\\\"required\\\":1,\\\"templateDetailsName\\\":\\\"6\\\",\\\"templateIdentity\\\":\\\"56\\\",\\\"type\\\":0,\\\"templateType\\\":2}]";
        String json1=json.replaceAll("\\\\\\\\","");
        System.out.println(json1);
        System.out.println(json1.toString().replaceAll("\\\\",""));
        JSONArray elements=JSONArray.parseArray(json1.toString().replaceAll("\\\\",""));
        List<String> fieldNames = new ArrayList<>();
        JSONArray distinctArr = new JSONArray();
        ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject)e).forEach(e -> {
            String fileName = e.getString("templateDetailsName");
            if(!fieldNames.contains(fileName)){
                fieldNames.add(fileName);
                distinctArr.add(e);
            }
        });
        List<String> fieldNames1 = new ArrayList<>();
        JSONArray distinctArr1 = new JSONArray();
        ListUtils.emptyIfNull(distinctArr).stream().map((e) -> (JSONObject)e).forEach(e -> {
            String fileName = e.getString("templateIdentity");
            if(!fieldNames1.contains(fileName)){
                fieldNames1.add(fileName);
                distinctArr1.add(e);
            }
        });
        System.out.println(distinctArr1);

    }

}