package cn.powerchina.bjy.link.dam.service.projectcategory;

import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.NameRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategoryListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategorySaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory.ProjectCategoryDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 工程分类管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectCategoryService {

    /**
     * 创建工程分类管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectCategory(@Valid ProjectCategorySaveReqVO createReqVO);

    /**
     * 更新工程分类管理
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectCategory(@Valid ProjectCategorySaveReqVO updateReqVO);

    /**
     * 删除工程分类管理
     *
     * @param id 编号
     */
    void deleteProjectCategory(Long id);

    /**
     * 校验工程分类
     *
     * @param id
     * @return
     */
    ProjectCategoryDO validateProjectCategoryExists(Long id);

    /**
     * 获得工程分类管理list
     *
     * @param projectId
     * @param categoryType 类别查询
     * @return 工程分类管理数据
     */
    List<ProjectCategoryDO> getProjectCategoryListByCategoryType(Long projectId, Integer categoryType);

    /**
     * 获得工程分类管理list
     *
     * @param parentId         父节点
     * @param allChildren
     * @param sameCategoryType 同一个节点类型
     * @return 工程分类管理数据
     */
    List<ProjectCategoryDO> getProjectCategoryListByParentId(Long parentId, boolean allChildren, boolean sameCategoryType);

    /**
     * @param reqVO
     * @return工程分类管理数据
     */
    List<ProjectCategoryBO> getProjectCategoryListByCategoryTypeAndId(ProjectCategoryListReqVO reqVO);

    /**
     * 获得工程分类管理list
     *
     * @param projectId
     * @return 工程分类管理数据
     */
    List<ProjectCategoryDO> getProjectCategoryListByProjectId(Long projectId);

    /**
     * 获得工程分类管理
     *
     * @param id 编号
     * @return 工程分类管理
     */
    ProjectCategoryDO getProjectCategory(Long id);

    /**
     * 修改工程分类名称
     *
     * @param categoryNameOrigin
     * @param categoryName
     * @param categoryType
     * @return
     */
    Boolean updateProjectCategoryName(String categoryNameOrigin, String categoryName, Integer categoryType);

    Boolean updateProjectCategoryName(Long projectId, String categoryNameOrigin, String categoryName, Integer categoryType);

    /**
     * 获取父节点
     *
     * @param id 编号
     * @return
     */
    ProjectCategoryDO getProjectCategoryParent(Long id);

    /**
     * 获取当前节点
     *
     * @param id 编号
     * @return
     */
    ProjectCategoryDO getProjectCategoryId(Long id);

    /**
     * 根据分类名称进行查找
     *
     * @param projectName
     * @param categoryType
     * @return
     */
    List<ProjectCategoryDO> getProjectCategoryByName(String projectName, Integer categoryType);

    /**
     * 获取 仪器类型、工程结构
     *
     * @param id 编号
     * @return 仪器类型、工程结构
     */
    ProjectCategoryLevelBO getProjectCategoryLevelBO(Long id);

    /**
     * 获取父级到当前节点全路径名称
     *
     * @param id
     * @param format
     * @return
     */
    String getAllPathCategoryName(Long id, String format);

    /**
     * 根据工程id，获取当前工程id下的测点id集合
     *
     * @param id
     * @return
     */
    List<Long> getPointListByCategoryId(Long id);

    /**
     * 根据测点id获取仪器类型id
     *
     * @param pointId
     * @return
     */
    Long getInstrumentIdByPointId(Long pointId);

    /**
     * 根据项目id和父节点id---计算直系子节点数量
     *
     * @param projectId
     * @param parentId
     * @return
     */
    Long countByProjectIdAndParentId(Long projectId, Long parentId);

    /**
     * 删除仪器类型和下面分组
     *
     * @param instrumentId
     */
    void deleteInstrumentAndGroup(Long instrumentId);

    /**
     * 查找仪器类型和工程结构和监测项目
     *
     * @param categoryId
     */
    NameRespVO getProjectCategoryMeasure(Long categoryId);


    /**
     * 查找仪器类型相同名字的节点
     *
     * @param reqVO
     * @param instrumentName
     */
    List<ProjectCategoryBO> getProjectInstrumentListByCategoryTypeAndId(ProjectCategoryListReqVO reqVO, String instrumentName);
}