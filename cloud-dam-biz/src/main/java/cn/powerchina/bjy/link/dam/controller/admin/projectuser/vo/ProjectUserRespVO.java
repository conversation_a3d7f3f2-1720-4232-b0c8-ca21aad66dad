package cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo;

import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 项目用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectUserRespVO extends UserBO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25835")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12622")
    @ExcelProperty("项目id")
    private Long projectId;
}