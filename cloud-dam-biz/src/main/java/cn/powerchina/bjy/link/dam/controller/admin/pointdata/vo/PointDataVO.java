package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/12/27
 */
@Data
public class PointDataVO {
    private String title;

    @Schema(description = "属性值")
    private List<BigDecimal> data;

    @Schema(description = "x轴：0、1")
    private Integer horizontalType;
    @Schema(description = "y轴：0、1、2、3")
    private Integer longitudinalType;

    private BigDecimal minValue;
    private BigDecimal maxValue;
    private Long instrumentModelId;

    public PointDataVO(String title, Integer horizontalType, Integer longitudinalType) {
        this.title = title;
        this.horizontalType = horizontalType;
        this.longitudinalType = longitudinalType;
    }
}
