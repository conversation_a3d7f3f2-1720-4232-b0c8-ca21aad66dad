package cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 仪器类型模板新增/修改 Request VO")
@Data
public class InstrumentTemplateSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4056")
    private Long id;

    @Schema(description = "仪器类型模板名称", example = "王五")
    @Length(max = 32)
    private String instrumentName;

    @Schema(description = "测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它")
    @NotNull(message = "测量原理不能为空")
    private Integer measurePrinciple;

    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @Schema(description = "图标地址")
    private String iconUrl;

    @Schema(description = "备注", example = "你猜")
    @Length(max = 64)
    private String remark;

}