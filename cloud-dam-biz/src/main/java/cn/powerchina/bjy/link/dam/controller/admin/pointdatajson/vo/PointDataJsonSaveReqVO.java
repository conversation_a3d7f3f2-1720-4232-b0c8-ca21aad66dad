package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 测点数据json新增/修改 Request VO")
@Data
public class PointDataJsonSaveReqVO {

    @Schema(description = "主键id", example = "406")
    private Long id;

    @Schema(description = "项目id", example = "19338")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "测点id", example = "24279")
    @NotNull(message = "测点id不能为空")
    private Long pointId;

    @Schema(description = "监测时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime pointTime;

    @Schema(description = "仪器类型id", example = "3871")
    @NotNull(message = "仪器类型id不能为空")
    private Long instrumentId;

    @Schema(description = "分量值json，包括分量id、分量标识符、分量名称、分量原始值、分量值")
    private String pointData;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入；前端默认2）", example = "2")
    private Integer dataType;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private Integer dataStatus;

    @Schema(hidden = true, description = "登录人id")
    private String userId;

    @Schema(hidden = true, description = "导入id")
    private Long importId;

    /**
     * 以自动化监测数据存储,0否，1是
     */
    private Integer isAutomation;

    private String creator;
}