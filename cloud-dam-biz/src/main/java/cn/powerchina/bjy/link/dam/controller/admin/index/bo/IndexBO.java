package cn.powerchina.bjy.link.dam.controller.admin.index.bo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 首页信息
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Data
public class IndexBO {

    /**
     * 监测仪器数量
     */
    private Long pointDeviceTotal;

    /**
     * 监测仪器今日新增数量
     */
    private Long pointDeviceToday;

    /**
     * 监测数据数量
     */
    private Long pointDataTotal;

    /**
     * 监测数据今日新增数量
     */
    private Long pointDataToday;

    /**
     * 自动化设备数量
     */
    private Long deviceMcuTotal;

    /**
     * 自动化设备今日新增数量
     */
    private Long deviceMcuToday;

    /**
     * 在测自动化仪器数量
     */
    private Long deviceMcuRunTotal;

    /**
     * 在测自动化仪器今日新增数量
     */
    private Long deviceMcuRunToday;

    /**
     * 自动化监测数据数量
     */
    private Long pointDataMcuTotal;

    /**
     * 自动化监测数据今日新增数量
     */
    private Long pointDataMcuToday;

    /**
     * 仪器数量列表
     */
    private List<PointDeviceInfo> pointDeviceInfoList;

    @Data
    public static class PointDeviceInfo {

        /**
         * 仪器类型名称
         */
        private String instrumentName;

        /**
         * 仪器数量
         */
        private Long instrumentCount;

        /**
         * 仪器占比%
         */
        private String instrumentRate;

        /**
         * 观测记录数量
         */
        private Long pointDataCount;

        /**
         * 最近观测时间
         */
        private LocalDateTime pointTimeRecent;

        /**
         * 最早观测时间
         */
        private LocalDateTime pointTimeFirst;

    }
}
