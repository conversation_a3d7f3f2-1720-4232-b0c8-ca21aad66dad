package cn.powerchina.bjy.link.dam.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Random;

/**
 * CodeGenerator
 *
 * <AUTHOR>
 **/
public class CodeGenerator {

    private final static int PAD_LENGTH = 3;
    private final static char PAD_CHAR = '0';

    private final static char[] lower = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
    private final static char[] upper = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
    private final static char[] digital = {'1', '2', '3', '4', '5', '6', '7', '8', '9'};

    /**
     * 根据类型生成编码
     * 生成规则：${前缀}${时间戳}${随机数}
     *
     * @param type 类型
     * @return 编码
     */
    public static String createCode(String type) {
        int number = RandomUtil.randomInt(1000);
        return type + System.currentTimeMillis() + StringUtils.leftPad(String.valueOf(number), PAD_LENGTH, PAD_CHAR);
    }


    /**
     * 生成日志编码
     * 生成规则：msg+uuid
     *
     * @return 编码
     */
    public static String createMsgUUID() {
        return "msg" + IdUtil.randomUUID().replace("-", "");
    }

    /**
     * 生成三位数随机字符串
     *
     * @return
     */
    public static String createThreeCode() {
        return lower[getRandomInteger(0, lower.length - 1)] + "" + upper[getRandomInteger(0, upper.length - 1)] + "" + digital[getRandomInteger(0, digital.length - 1)];
    }

    /**
     * 生成指定范围随机整数
     *
     * @param min
     * @param max
     * @return
     */
    public static int getRandomInteger(int min, int max) {
        Random rand = new Random();
        return rand.nextInt((max - min) + 1) + min; // 生成[min,max]范围的随机整数
    }
}
