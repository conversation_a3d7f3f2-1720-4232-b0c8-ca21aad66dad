package cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点计算公式分页 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointFormulaPageVO {

    @Schema(description = "主键id", example = "7726")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "仪器类型")
    private String instrumentName;

    @Schema(description = "测点状态")
    private Integer pointState;

    @Schema(description = "分量名称", example = "赵六")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "计算公式")
    @ExcelProperty("计算公式")
    private String calcFormula;

    @Schema(description = "有效开始时间")
    @ExcelProperty("有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    @ExcelProperty("有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    @ExcelProperty("适用类型，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}