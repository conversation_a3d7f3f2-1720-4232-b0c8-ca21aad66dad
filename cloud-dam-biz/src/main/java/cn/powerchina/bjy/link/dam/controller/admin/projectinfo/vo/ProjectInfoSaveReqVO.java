package cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 项目工程信息新增/修改 Request VO")
@Data
public class ProjectInfoSaveReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "工程信息")
    private String projectInfo;

}