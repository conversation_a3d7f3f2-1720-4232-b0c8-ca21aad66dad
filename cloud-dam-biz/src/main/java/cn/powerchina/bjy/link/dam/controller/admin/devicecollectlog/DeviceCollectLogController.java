package cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicecollectlog.DeviceCollectLogDO;
import cn.powerchina.bjy.link.dam.service.devicecollectlog.DeviceCollectLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 网关设备采集日志")
@RestController
@RequestMapping("/dam/device/collect/log")
@Validated
public class DeviceCollectLogController {

    @Resource
    private DeviceCollectLogService deviceCollectLogService;

    @PostMapping("/create")
    @Operation(summary = "创建网关设备采集日志")
//    @PreAuthorize("@ss.hasPermission('dam:device-collect-log:create')")
    public CommonResult<Boolean> createDeviceCollectLog(@Valid @RequestBody DeviceCollectLogSaveReqVO createReqVO) {
        deviceCollectLogService.createDeviceCollectLog(createReqVO);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新网关设备采集日志")
//    @PreAuthorize("@ss.hasPermission('dam:device-collect-log:update')")
    public CommonResult<Boolean> updateDeviceCollectLog(@Valid @RequestBody DeviceCollectLogSaveReqVO updateReqVO) {
        deviceCollectLogService.updateDeviceCollectLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除网关设备采集日志")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:device-collect-log:delete')")
    public CommonResult<Boolean> deleteDeviceCollectLog(@RequestParam("id") Long id) {
        deviceCollectLogService.deleteDeviceCollectLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得网关设备采集日志")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:device-collect-log:query')")
    public CommonResult<DeviceCollectLogRespVO> getDeviceCollectLog(@RequestParam("id") Long id) {
        DeviceCollectLogDO deviceCollectLog = deviceCollectLogService.getDeviceCollectLog(id);
        return success(BeanUtils.toBean(deviceCollectLog, DeviceCollectLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得网关设备采集日志分页")
//    @PreAuthorize("@ss.hasPermission('dam:device-collect-log:query')")
    public CommonResult<PageResult<DeviceCollectLogRespVO>> getDeviceCollectLogPage(@Valid DeviceCollectLogPageReqVO pageReqVO) {
        PageResult<DeviceCollectLogDO> pageResult = deviceCollectLogService.getDeviceCollectLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceCollectLogRespVO.class));
    }

}