package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据新增/修改 Request VO")
@Data
public class PointDataSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4260")
    private Long id;

    @Schema(description = "项目id", example = "12214")
    private Long projectId;

    @Schema(description = "测点id", example = "20030")
    private Long pointId;

    @Schema(description = "监测时间")
    private LocalDateTime pointTime;

    @Schema(description = "仪器类型id", example = "19226")
    private Long instrumentId;

    @Schema(description = "分量id", example = "15433")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称", example = "李四")
    private String thingName;

    @Schema(description = "分量原始值")
    private BigDecimal thingValueOrigin;

    @Schema(description = "分量值")
    private BigDecimal thingValue;

    @Schema(description = "分量值绝对值")
    private BigDecimal absoluteValue;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private Integer dataType;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private Integer dataStatus;

}