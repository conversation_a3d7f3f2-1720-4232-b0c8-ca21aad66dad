package cn.powerchina.bjy.link.dam.controller.admin.monitorchart;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo.*;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 监测图形")
@RestController
@RequestMapping("/dam/monitor/chart")
@Validated
public class MonitorChartController {

    @Resource
    private PointDataService pointDataService;

    @PostMapping("/processline")
    @Operation(summary = "过程线")
    public CommonResult<List<PointDataProcessLineResVO>> listPointDataProcessLine(@Valid @RequestBody PointDataProcessLineReqVO pointDataProcessLineReqVO){
        List<PointDataProcessLineResVO> pointDataProcessLineResVOList = pointDataService.listPointDataProcessLine(pointDataProcessLineReqVO);
        return success(pointDataProcessLineResVOList);
    }

    @PostMapping("/pointTime")
    @Operation(summary = "分布图时间")
    public CommonResult<List<String>> listPointTime(@Valid @RequestBody PointTimeReqVO pointTimeReqVO){
        List<String> poinTimeList = pointDataService.listPointTime(pointTimeReqVO);
        return success(poinTimeList);
    }

    @PostMapping("/distributionChart")
    @Operation(summary = "分布图")
    public CommonResult<List<PointDataDistributionChartResVO>> listPointDataDistributionChart(@Valid @RequestBody PointDataDistributionChartReqVO pointDataDistributionChartReqVO){
        List<PointDataDistributionChartResVO> pointDataDistributionChartResVOList = pointDataService.listPointDataDistributionChart(pointDataDistributionChartReqVO);
        return success(pointDataDistributionChartResVOList);
    }
}