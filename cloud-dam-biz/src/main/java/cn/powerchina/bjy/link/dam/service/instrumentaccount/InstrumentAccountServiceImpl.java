package cn.powerchina.bjy.link.dam.service.instrumentaccount;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.device.bo.DeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountPageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentaccount.InstrumentAccountDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentaccount.InstrumentAccountMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INSTRUMENT_ACCOUNT_NOT_EXISTS;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.POINT_DELETE_EXISTS;

/**
 * 仪器台账 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentAccountServiceImpl implements InstrumentAccountService {

    @Resource
    private InstrumentAccountMapper instrumentAccountMapper;

    @Resource
    private PointMapper pointMapper;

    @Override
    public Long createInstrumentAccount(InstrumentAccountSaveReqVO createReqVO) {
        // 插入
        InstrumentAccountDO instrumentAccount = BeanUtils.toBean(createReqVO, InstrumentAccountDO.class);
        instrumentAccountMapper.insert(instrumentAccount);
        // 返回
        return instrumentAccount.getId();
    }

    @Override
    public void updateInstrumentAccount(InstrumentAccountSaveReqVO updateReqVO) {
        // 校验存在
        validateInstrumentAccountExists(updateReqVO.getId());
        //检验仪器台账下面是否有测点
        List<PointDO> list=pointMapper.selectList(new LambdaQueryWrapperX<PointDO>().eqIfPresent(PointDO::getAccountId, updateReqVO.getId()).apply("deleted = {0}", 0));
        if (!CollectionUtils.isEmpty(list)) {
            throw exception(POINT_DELETE_EXISTS);
        }
        // 更新
        InstrumentAccountDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentAccountDO.class);
        instrumentAccountMapper.updateById(updateObj);
    }

    @Override
    public void deleteInstrumentAccount(Long id) {
        // 校验存在
        validateInstrumentAccountExists(id);
        //检验仪器台账下面是否有测点
        List<PointDO> list=pointMapper.selectList(new LambdaQueryWrapperX<PointDO>().eqIfPresent(PointDO::getAccountId, id).apply("deleted = {0}", 0));
        if (!CollectionUtils.isEmpty(list)) {
            throw exception(POINT_DELETE_EXISTS);
        }
        // 删除
        instrumentAccountMapper.deleteById(id);
    }

    private void validateInstrumentAccountExists(Long id) {
        if (instrumentAccountMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_ACCOUNT_NOT_EXISTS);
        }
    }

    @Override
    public InstrumentAccountDO getInstrumentAccount(Long id) {
        return instrumentAccountMapper.selectById(id);
    }

    @Override
    public PageResult<InstrumentAccountPageRespVO> getInstrumentAccountPage(InstrumentAccountPageReqVO pageReqVO) {
        return instrumentAccountMapper.selectAccountPage(pageReqVO);
    }

}