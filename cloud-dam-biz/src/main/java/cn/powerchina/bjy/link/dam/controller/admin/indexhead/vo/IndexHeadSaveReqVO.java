package cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 大坝首页头部信息新增/修改 Request VO")
@Data
public class IndexHeadSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "340")
    private Long id;

    @Schema(description = "项目id", example = "4887")
    private Long projectId;

    @Schema(description = "监测仪器数量")
    private Long pointDeviceTotal;

    @Schema(description = "监测仪器今日新增数量")
    private Long pointDeviceToday;

    @Schema(description = "监测数据数量")
    private Long pointDataTotal;

    @Schema(description = "监测数据今日新增数量")
    private Long pointDataToday;

    @Schema(description = "自动化设备数量")
    private Long deviceMcuTotal;

    @Schema(description = "自动化设备今日新增数量")
    private Long deviceMcuToday;

    @Schema(description = "在测自动化仪器数量")
    private Long deviceMcuRunTotal;

    @Schema(description = "在测自动化仪器今日新增数量")
    private Long deviceMcuRunToday;

    @Schema(description = "自动化监测数据数量")
    private Long pointDataMcuTotal;

    @Schema(description = "自动化监测数据今日新增数量")
    private Long pointDataMcuToday;

    @Schema(description = "生成时间")
    private String generateTime;

}