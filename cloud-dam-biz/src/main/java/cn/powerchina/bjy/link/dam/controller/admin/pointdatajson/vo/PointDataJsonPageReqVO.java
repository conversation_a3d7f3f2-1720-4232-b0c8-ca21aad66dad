package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 测点数据json分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointDataJsonPageReqVO extends PageParam {

    @Schema(description = "项目id", example = "19338")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "测点id", example = "24279")
    @NotNull(message = "测点id不能为空")
    private Long pointId;

    @Schema(description = "监测时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] pointTime;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private Integer dataType;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private List<Integer> dataStatusList;

    @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）", example = "1")
    private List<Integer> reviewStatusList;

    @Schema(description = "分量类型：1：原始值，2：中间值，3：成果值（只有导出接口需要传该参数）", example = "1")
    private List<Integer> thingTypeList;

}