package cn.powerchina.bjy.link.dam.dal.mysql.projectmenu;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectmenu.ProjectMenuDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 项目菜单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectMenuMapper extends BaseMapperX<ProjectMenuDO> {

    default PageResult<ProjectMenuDO> selectPage(ProjectMenuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectMenuDO>()
                .eqIfPresent(ProjectMenuDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectMenuDO::getMenuId, reqVO.getMenuId())
                .betweenIfPresent(ProjectMenuDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProjectMenuDO::getId));
    }

}