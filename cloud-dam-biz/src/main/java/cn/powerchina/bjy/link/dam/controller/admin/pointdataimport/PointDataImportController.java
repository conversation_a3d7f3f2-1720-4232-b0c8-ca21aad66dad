package cn.powerchina.bjy.link.dam.controller.admin.pointdataimport;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.BatchImportVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointDataJsonSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointInstrumentModelJsonVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdataimport.PointDataImportDO;
import cn.powerchina.bjy.link.dam.enums.DamConstant;
import cn.powerchina.bjy.link.dam.enums.DataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.pointdataimport.PointDataImportService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点数据导入")
@RestController
@RequestMapping("/dam/point/data/import")
@Validated
@Slf4j
public class PointDataImportController {

    @Resource
    private PointDataImportService pointDataImportService;

    @Resource
    private PointDataJsonService pointDataJsonService;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private ThreadPoolTaskExecutor damThreadPoolTaskExecutor;

    @Resource
    private RedisTemplate<String, Integer> redisTemplate;

    @PostMapping(value = "/create")
    @Operation(summary = "创建测点数据导入")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-import:create')")
    public CommonResult<Long> createPointDataImport(PointDataImportSaveReqVO createReqVO,
                                                    @RequestPart(name = "file") MultipartFile file) {
        return success(pointDataImportService.createPointDataImport(createReqVO, file));
    }

    @PostMapping("/batchtest")
    @Operation(summary = "批量插入测试数据")
//    @PreAuthorize("@ss.hasPermission('dam:point-data-import:update')")
    public CommonResult<Boolean> updatePointDataImport(@Valid @RequestBody BatchImportVO batchImportVO, @RequestParam Integer totalSize, @RequestParam Integer batchCount) {
        LocalDate localDate = LocalDate.of(2013, 1, 1);
        LocalDateTime localDateTime = localDate.atStartOfDay();
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(batchImportVO.getPointId());
        if (CollectionUtils.isEmpty(instrumentModelList)) {
            throw new RuntimeException("测点下没有分量");
        }
        log.info("start insert time {}", System.currentTimeMillis());
        List<PointDataJsonSaveReqVO> pointDataJsonSaveReqVOList = ListUtils.newArrayListWithExpectedSize(batchCount);
        PointDataJsonSaveReqVO pointDataJsonSaveReqVO;
        for (int i = 0; i < totalSize; i++) {
            pointDataJsonSaveReqVO = new PointDataJsonSaveReqVO();
            pointDataJsonSaveReqVO.setProjectId(batchImportVO.getProjectId());
            pointDataJsonSaveReqVO.setPointId(batchImportVO.getPointId());
            pointDataJsonSaveReqVO.setDataType(DataTypeEnum.MANUAL.getType());
            localDateTime = localDateTime.plusMinutes(10);
            pointDataJsonSaveReqVO.setPointTime(localDateTime);
            pointDataJsonSaveReqVO.setDataStatus((int) (Math.random() * 3));
            pointDataJsonSaveReqVO.setInstrumentId(instrumentModelList.get(0).getInstrumentId());

            List<PointInstrumentModelJsonVO> modelBOList = new ArrayList<>();
            PointInstrumentModelJsonVO modelJsonVO;
            for (InstrumentModelDO modelDO : instrumentModelList) {
                modelJsonVO = new PointInstrumentModelJsonVO();
                BeanUtils.copyProperties(modelDO, modelJsonVO, "id");
                modelJsonVO.setInstrumentModelId(modelDO.getId());
                modelJsonVO.setThingValue(new BigDecimal((int) (Math.random() * 1000)));
                modelBOList.add(modelJsonVO);
            }
            pointDataJsonSaveReqVO.setPointData(JSONObject.toJSONString(modelBOList));
            pointDataJsonSaveReqVOList.add(pointDataJsonSaveReqVO);

            if (pointDataJsonSaveReqVOList.size() >= batchCount) {
                saveData(pointDataJsonSaveReqVOList);
                pointDataJsonSaveReqVOList = ListUtils.newArrayListWithExpectedSize(batchCount);
            }
        }
        CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
        log.info(" insert end time {}", System.currentTimeMillis());
        return success(true);
    }

    List<CompletableFuture<Void>> result = new ArrayList<>();

    private void saveData(List<PointDataJsonSaveReqVO> pointDataJsonSaveReqVOList) {
        List<PointDataJsonSaveReqVO> newList = new ArrayList<>(pointDataJsonSaveReqVOList);
        result.add(CompletableFuture.runAsync(() -> {
            log.info("{}条数据，开始存储数据库！", newList.size());
            pointDataJsonService.importPointDataJson(newList);
            log.info("存储数据库成功！");
        }, damThreadPoolTaskExecutor));
    }


    @GetMapping("/template")
    @Operation(summary = "获得导入模板")
    @Parameter(name = "pointId", description = "测点id", required = true)
    @Parameter(name = "templateType", description = "模板类型")
    public void importTemplate(HttpServletResponse response, Long pointId, @RequestParam(required = false) Integer templateType) throws IOException {
        templateType = null == templateType ? 1 : templateType;
        List<List<String>> templateHead = pointDataImportService.getTemplateHead(pointId, templateType);
        //示例数据
        List<List<String>> dataList = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        String timeString = dateTimeFormatter.format(LocalDateTime.now().withSecond(0));
        List<String> firstDataList = new ArrayList<>();
        if (templateType == 2) {
            firstDataList.add(null);
        }
        firstDataList.add(timeString);
        dataList.add(firstDataList);

        try {
            response.setContentType("application/binary;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("测点导入模板.xls", StandardCharsets.UTF_8.name()));
            EasyExcel.write(response.getOutputStream())
                    // 这里放入动态头
                    .head(templateHead).sheet("模板")
                    // 当然这里数据也可以用 List<List<String>> 去传入
                    .doWrite(dataList);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.POINT_DATA_IMPORT_TEMPLATE_ERROR, e.getMessage());
        }
    }


    @GetMapping("/result")
    @Operation(summary = "查询导入结果")
    @Parameter(name = "pointId", description = "测点id", required = true)
    public CommonResult<PointDataImportDO> importResult(Long pointId) {
        PointDataImportDO lastPointDataImportDO = pointDataImportService.getLastPointDataImportDO(pointId);
        return success(lastPointDataImportDO);
    }

    @GetMapping("/taskStatus")
    @Operation(summary = "任务执行状态")
    @Parameter(name = "pointId", description = "测点id", required = true)
    public CommonResult<Integer> taskStatus(Long pointId) {
        String key = String.format(DamConstant.POINT_TASK_STATUS_KEY, pointId);
        return success(redisTemplate.opsForValue().get(key));
    }

}