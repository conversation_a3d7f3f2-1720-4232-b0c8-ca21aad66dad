package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "lineStyle")
@Data
public class PointDataProcessLineYsplitLineStyleVO {

    @Schema(description = "color")
    private String color;

    @Schema(description = "type")
    private String type;
}