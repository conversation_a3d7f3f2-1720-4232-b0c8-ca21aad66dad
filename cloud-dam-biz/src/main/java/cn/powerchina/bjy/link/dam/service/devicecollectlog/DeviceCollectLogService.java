package cn.powerchina.bjy.link.dam.service.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicecollectlog.DeviceCollectLogDO;
import jakarta.validation.Valid;

/**
 * 网关设备采集日志 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceCollectLogService {

    /**
     * 创建网关设备采集日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createDeviceCollectLog(@Valid DeviceCollectLogSaveReqVO createReqVO);

    /**
     * 更新网关设备采集日志
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceCollectLog(@Valid DeviceCollectLogSaveReqVO updateReqVO);

    /**
     * 删除网关设备采集日志
     *
     * @param id 编号
     */
    void deleteDeviceCollectLog(Long id);

    /**
     * 获得网关设备采集日志
     *
     * @param id 编号
     * @return 网关设备采集日志
     */
    DeviceCollectLogDO getDeviceCollectLog(Long id);

    /**
     * 获得网关设备采集日志分页
     *
     * @param pageReqVO 分页查询
     * @return 网关设备采集日志分页
     */
    PageResult<DeviceCollectLogDO> getDeviceCollectLogPage(DeviceCollectLogPageReqVO pageReqVO);

    /**
     * 执行执行计划
     */
    void runDeviceCollectLog();
}