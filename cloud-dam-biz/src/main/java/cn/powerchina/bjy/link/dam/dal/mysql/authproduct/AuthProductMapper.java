package cn.powerchina.bjy.link.dam.dal.mysql.authproduct;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo.AuthProductPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 产品授权 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AuthProductMapper extends BaseMapperX<AuthProductDO> {

    default PageResult<AuthProductDO> selectPage(AuthProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AuthProductDO>()
                .eqIfPresent(AuthProductDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(AuthProductDO::getProductCode, reqVO.getProductCode())
                .likeIfPresent(AuthProductDO::getProductName, reqVO.getProductName())
                .likeIfPresent(AuthProductDO::getFirmName, reqVO.getFirmName())
                .eqIfPresent(AuthProductDO::getProductModel, reqVO.getProductModel())
                .eqIfPresent(AuthProductDO::getNodeType, reqVO.getNodeType())
                .betweenIfPresent(AuthProductDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AuthProductDO::getId));
    }

}