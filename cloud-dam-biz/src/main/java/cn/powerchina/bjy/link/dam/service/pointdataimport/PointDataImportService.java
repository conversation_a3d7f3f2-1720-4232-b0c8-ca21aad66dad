package cn.powerchina.bjy.link.dam.service.pointdataimport;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdataimport.PointDataImportDO;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 测点数据导入 Service 接口
 *
 * <AUTHOR>
 */
public interface PointDataImportService {

    /**
     * 创建测点数据导入
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointDataImport(@Valid PointDataImportSaveReqVO createReqVO, MultipartFile file);

    /**
     * 更新测点数据导入
     *
     * @param updateReqVO 更新信息
     */
    void updatePointDataImport(@Valid PointDataImportSaveReqVO updateReqVO);

    /**
     * 删除测点数据导入
     *
     * @param id 编号
     */
    void deletePointDataImport(Long id);

    /**
     * 获得测点数据导入
     *
     * @param id 编号
     * @return 测点数据导入
     */
    PointDataImportDO getPointDataImport(Long id);

    /**
     * 获得测点数据导入分页
     *
     * @param pageReqVO 分页查询
     * @return 测点数据导入分页
     */
    PageResult<PointDataImportDO> getPointDataImportPage(PointDataImportPageReqVO pageReqVO);

    /**
     * 人工数据导入的动态表头
     *
     * @param pointId
     * @return
     */
    List<List<String>> getTemplateHead(Long pointId, Integer templateType);

    PointDataImportDO getLastPointDataImportDO(Long pointId);

}