package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import cn.powerchina.bjy.link.dam.service.iotproduct.IotProductService;
import cn.powerchina.bjy.link.dam.service.iotproductmodel.IotProductModelService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.product.Product;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品删除消息处理器
 */
@Component
public class ProductDeleteMsgProcessor implements MsgProcessor {

    @Autowired
    private IotProductService iotProductService;

    @Autowired
    private IotProductModelService iotProductModelService;

    @Autowired
    private AuthProductService authProductService;

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    @Transactional
    public void process(String payload) {
        MqttReceiveData<Product> productData = JsonUtils.parseObject(payload, new TypeReference<>() {
        });
        Product product = productData.getMessage();

        // 删除物联网平台同步的产品
        iotProductService.deleteByIotId(product.getId());

        // 删除物联网平台同步的产品物模型
        iotProductModelService.deleteByIotProductIotId(product.getId());

        // 删除产品授权
        authProductService.deleteByProductCode(product.getProductCode());
    }

    /**
     * 获取当前消息对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.PRODUCT_TOPIC.getTopic();
    }

    /**
     * 获取当前消息对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.PRODUCT_DELETE.getDataType();
    }
}
