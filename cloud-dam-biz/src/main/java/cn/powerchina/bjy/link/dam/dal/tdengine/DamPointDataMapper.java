package cn.powerchina.bjy.link.dam.dal.tdengine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataTDDO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointDataJsonReviewVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointDataTRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.framework.tdengine.TDengineDS;
import cn.powerchina.bjy.link.dam.framework.tdengine.TDengineTableField;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测点数据 {@link PointDataDO} Mapper 接口
 */
@Mapper
@TDengineDS
@InterceptorIgnore(tenantLine = "true") // 避免 SQL 解析，因为 JSqlParser 对 TDengine 的 SQL 解析会报错
public interface DamPointDataMapper {

    List<TDengineTableField> getPointDataSTableFieldList(@Param("projectId") String projectId,@Param("instrumentId") String instrumentId);

    void createPointDataSTable(@Param("projectId") String projectId,
                               @Param("instrumentId") String instrumentId,
                                     @Param("fields") List<TDengineTableField> fields);

    default void alterPointDataSTable( String projectId,
                                           String instrumentId,
                                            List<TDengineTableField> oldFields,
                                            List<TDengineTableField> newFields) {
        oldFields.removeIf(field -> StrUtil.equalsAny(field.getField(),
                TDengineTableField.FIELD_TS, "data_type", "data_status", "creator", "create_time", "point_id"
                , "review_status", "reviewer", "review_name", "review_opinion"));
        List<TDengineTableField> addFields = newFields.stream().filter( // 新增的字段
                        newField -> oldFields.stream().noneMatch(oldField -> oldField.getField().equals(newField.getField())))
                .collect(Collectors.toList());
        List<TDengineTableField> dropFields = oldFields.stream().filter( // 删除的字段
                        oldField -> newFields.stream().noneMatch(n -> n.getField().equals(oldField.getField())))
                .collect(Collectors.toList());

        List<TDengineTableField> modifyTypeFields = new ArrayList<>(); // 变更类型的字段
        List<TDengineTableField> modifyLengthFields = new ArrayList<>(); // 变更长度的字段
        newFields.forEach(newField -> {
            TDengineTableField oldField = CollUtil.findOne(oldFields, field -> field.getField().equals(newField.getField()));
            if (oldField == null) {
                return;
            }
            if (ObjectUtil.notEqual(oldField.getType(), newField.getType())) {
                modifyTypeFields.add(newField);
                return;
            }
            if (newField.getLength() != null) {
                if (newField.getLength() > oldField.getLength()) {
                    modifyLengthFields.add(newField);
                } else if (newField.getLength() < oldField.getLength()) {
                    // 特殊：TDengine 长度修改时，只允许变长，所以此时认为是修改类型
                    modifyTypeFields.add(newField);
                }
            }
        });

        // 执行
        addFields.forEach(field -> alterPointDataSTableAddField(projectId,instrumentId, field));
        dropFields.forEach(field -> alterPointDataSTableDropField(projectId,instrumentId, field));
        modifyLengthFields.forEach(field -> alterPointDataSTableModifyField(projectId,instrumentId, field));
        modifyTypeFields.forEach(field -> {
            alterPointDataSTableDropField(projectId,instrumentId, field);
            alterPointDataSTableAddField(projectId,instrumentId, field);
        });
    }

    void alterPointDataSTableAddField(@Param("projectId") String projectId,
                                            @Param("instrumentId") String instrumentId,
                                            @Param("field") TDengineTableField field);

    void alterPointDataSTableModifyField(@Param("projectId") String projectId,
                                               @Param("instrumentId") String instrumentId,
                                               @Param("field") TDengineTableField field);

    void alterPointDataSTableDropField(@Param("projectId") String projectId,
                                             @Param("instrumentId") String instrumentId,
                                             @Param("field") TDengineTableField field);

    void insertType1(@Param("pointData") PointDataTDDO pointData,
                @Param("pointDataMap") Map<String, String> pointDataMap);

    void insertType2(@Param("pointData") PointDataTDDO pointData,
                @Param("pointDataMap") Map<String, String> pointDataMap);

    void delByPointTime1(@Param("instrumentId") String instrumentId,
                        @Param("pointId") String pointId,
                        @Param("startPointTime") String startPointTime,
                        @Param("endPointTime") String endPointTime);

    void delByPointTime2(@Param("instrumentId") String instrumentId,
                         @Param("pointId") String pointId,
                         @Param("startPointTime") String startPointTime,
                         @Param("endPointTime") String endPointTime);

    void insertReview1(@Param("pointTime") Long pointTime,@Param("pointData") PointDataJsonDO pointData);

    void insertReview2(@Param("pointTime") Long pointTime,@Param("pointData") PointDataJsonDO pointData);

    Map getLastPointDataJson(@Param("instrumentId") String instrumentId,
                         @Param("pointId") String pointId,
                         @Param("dataType") String dataType);

    List<Map> selectPointDateJson(@Param("reqVO") PointDataTRespVO reqVO);

    Long selectPointDateJsonCount(@Param("reqVO") PointDataTRespVO reqVO);

    List<Map> selectPointDateJsonV1(@Param("reqVO") PointDataTRespVO reqVO);

    List<Map> selectInstrument(@Param("reqVO") PointDataTRespVO reqVO);
}
