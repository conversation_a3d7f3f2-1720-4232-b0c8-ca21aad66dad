package cn.powerchina.bjy.link.dam.dal.tdengine;

import cn.powerchina.bjy.link.dam.controller.admin.monitorchart.bo.PointDataJsonPointTimeBO;
import cn.powerchina.bjy.link.dam.framework.tdengine.TDengineDS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 测点数据json Mapper
 *
 * <AUTHOR>
 */
@Mapper
@TDengineDS
@InterceptorIgnore(tenantLine = "true") // 避免 SQL 解析，因为 JSqlParser 对 TDengine 的 SQL 解析会报错
public interface DamPointDataJsonMapper {

    /**
     * 根据一批测点id查询最大监测时间
     * @param pointIdList
     * @param projectId
     * @param instrumentId
     * @return
     */
    List<Map> listMaxPointTimeByPointIdListTdengine(@Param("pointIdList") List<Long> pointIdList,
                                                    @Param("projectId") Long projectId,
                                                    @Param("instrumentId") Long instrumentId);


    /**
     * 查询监测图形-过程线的数据
     * @param projectId
     * @param pointTimeList
     * @param dataTypeList
     * @param dataStatusList
     * @param reviewStatusList
     * @return
     */
    List<Map> list4ProcessLineTdengine(@Param("projectId") Long projectId, @Param("instrumentId") Long instrumentId,
                                           @Param("pointTimeList") List<PointDataJsonPointTimeBO> pointTimeList,
                                           @Param("dataTypeList") List<Integer> dataTypeList,
                                           @Param("dataStatusList") List<Integer> dataStatusList,
                                           @Param("reviewStatusList") List<Integer> reviewStatusList);

    /**
     * 查询时间范围内的数据
     * @param projectId
     * @param instrumentId
     * @param pointIdList
     * @param minTime
     * @param maxTime
     * @return
     */
    List<Map> listPointTime(@Param("projectId") Long projectId, @Param("instrumentId") Long instrumentId,
                            @Param("pointIdList") List<Long> pointIdList, @Param("minTime") String minTime,
                            @Param("maxTime") String maxTime);

    /**
     * 查询监测图形
     * @param projectId
     * @param pointIdList
     * @param pointTimeList
     * @param dataTypeList
     * @param dataStatusList
     * @param reviewStatusList
     * @return
     */
    List<Map> list4DistributionChartTdengine(@Param("projectId") Long projectId, @Param("instrumentId") Long instrumentId,
                                                 @Param("pointIdList") List<Long> pointIdList,
                                                 @Param("pointTimeList") List<String> pointTimeList,
                                                 @Param("dataTypeList") List<Integer> dataTypeList,
                                                 @Param("dataStatusList") List<Integer> dataStatusList,
                                                 @Param("reviewStatusList") List<Integer> reviewStatusList);

    /**
     * 查询分布图替换数据
     * @param projectId
     * @param instrumentId
     * @param pointId
     * @param minTime
     * @param maxTime
     * @param dataTypeList
     * @param dataStatusList
     * @param reviewStatusList
     * @return
     */
    List<Map> listReplaceData(@Param("projectId") Long projectId, @Param("instrumentId") Long instrumentId,
                              @Param("pointId") Long pointId,
                              @Param("minTime") String minTime,
                              @Param("maxTime") String maxTime,
                              @Param("dataTypeList") List<Integer> dataTypeList,
                              @Param("dataStatusList") List<Integer> dataStatusList,
                              @Param("reviewStatusList") List<Integer> reviewStatusList);

    /**
     * 查询测值统计的数据
     * @param projectId
     * @param instrumentId
     * @param pointId
     * @param startTime
     * @param endTime
     * @param dataTypeList
     * @param dataStatusList
     * @return
     */
    List<Map> list4MeasurementStatisticsTdengine(@Param("projectId") Long projectId, @Param("instrumentId") Long instrumentId,
                                                 @Param("pointId") Long pointId,
                                                 @Param("startTime") String startTime,
                                                 @Param("endTime") String endTime,
                                                 @Param("dataTypeList") List<Integer> dataTypeList,
                                                 @Param("dataStatusList") List<Integer> dataStatusList);
}