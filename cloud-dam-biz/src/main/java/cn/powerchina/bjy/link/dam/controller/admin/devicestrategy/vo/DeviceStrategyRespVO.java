package cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备采集策略 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceStrategyRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "策略名称")
    @ExcelProperty("策略名称")
    private String strategyName;

    @Schema(description = "采集方式，1：定点采集，2：间隔采集，3：跨天采集")
    @ExcelProperty("采集方式，1：定点采集，2：间隔采集，3：跨天采集")
    private Integer strategyType;

    @Schema(description = "时间间隔")
    @ExcelProperty("时间间隔")
    private String timeInterval;

    @Schema(description = "时间点（英文逗号分隔）")
    @ExcelProperty("时间点（英文逗号分隔）")
    private String timePoint;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}