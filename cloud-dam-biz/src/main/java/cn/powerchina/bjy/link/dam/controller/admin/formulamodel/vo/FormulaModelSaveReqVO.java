package cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 公式关联分量新增/修改 Request VO")
@Data
public class FormulaModelSaveReqVO {

    @Schema(description = "主键id",  example = "9598")
    private Long id;

    @Schema(description = "测点公式id", example = "10774")
    @NotNull(message = "测点公式id不能为空")
    private Long pointFormulaId;

    @Schema(description = "分量id", example = "31788")
    @NotNull(message = "分量id不能为空")
    private Long instrumentModelId;

    @Schema(description = "取值条件，1：无，2：相对测值，3：首次测值：所有测次中第一次测值，4：时间范围内测值")
    @NotNull(message = "取值条件不能为空")
    private Integer dataCondition;

    @Schema(description = "数值或1：之前，2：之后")
    private Integer dataValue;

    @Schema(description = "第n条测值或单位，1：分钟，2：小时，3：天")
    private Integer dataUnit;

    @Schema(description = "指定时间")
    private LocalDateTime specifyTime;

}