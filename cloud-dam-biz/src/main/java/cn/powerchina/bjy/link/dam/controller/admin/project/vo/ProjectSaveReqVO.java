package cn.powerchina.bjy.link.dam.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 项目管理新增/修改 Request VO")
@Data
public class ProjectSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目名称")
    @NotEmpty(message = "项目名称不能为空")
    private String projectName;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "省编码")
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    @Schema(description = "市名称")
    private String cityName;

    @NotBlank(message = "市编码不能为空")
    @Schema(description = "市编码")
    private String cityCode;
    
    @Schema(description = "区名称")
    private String areaName;

    @NotBlank(message = "区编码不能为空")
    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "维度")
    private String latitude;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "项目管理员id")
    @NotNull(message = "请选择项目管理员")
    private Long managerUserId;

}