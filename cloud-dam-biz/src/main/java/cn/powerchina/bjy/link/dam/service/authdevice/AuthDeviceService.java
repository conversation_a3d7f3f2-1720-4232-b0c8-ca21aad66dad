package cn.powerchina.bjy.link.dam.service.authdevice;

import cn.powerchina.bjy.link.dam.controller.admin.authdevice.bo.AuthDeviceGroupBO;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthBindDeviceReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthDeviceSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 设备授权 Service 接口
 *
 * <AUTHOR>
 */
public interface AuthDeviceService {

    /**
     * 创建设备授权
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long saveAuthDevice(@Valid AuthDeviceSaveReqVO reqVO);

    /**
     * 绑定设备
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long bindDevice(@Valid AuthBindDeviceReqVO reqVO);

    /**
     * 获得设备分组
     *
     * @param resourceSpaceId
     * @return
     */
    List<AuthDeviceGroupBO> getAuthDeviceGroupList(Long resourceSpaceId);

    /**
     * 获得项目已授权分组
     *
     * @param resourceSpaceId
     * @param projectId
     * @return
     */
    List<Long> getAuthDeviceGroupIdList(Long resourceSpaceId, Long projectId);

    /**
     * 根据分组id查询项目id集合
     *
     * @param deviceGroupId
     * @return
     */
    List<Long> getProjectByDeviceGroupId(Long deviceGroupId);

    /**
     * 根据项目id删除设备授权
     *
     * @param projectId
     */
    void deleteAuthDeviceByProjectId(Long projectId);
}