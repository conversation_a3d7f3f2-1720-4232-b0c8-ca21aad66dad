package cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 设备采集策略新增/修改 Request VO")
@Data
public class DeviceStrategySaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @NotNull(message = "项目不能为空")
    @Schema(description = "项目id")
    private Long projectId;

    @NotNull(message = "策略名称不能为空")
    @Schema(description = "策略名称")
    private String strategyName;

    @NotNull(message = "采集方式不能为空")
    @Schema(description = "采集方式，1：定点采集，2：间隔采集，3：跨天采集")
    private Integer strategyType;

    @Schema(description = "时间间隔")
    private String timeInterval;

    @Schema(description = "时间点（英文逗号分隔）")
    private String timePoint;

}