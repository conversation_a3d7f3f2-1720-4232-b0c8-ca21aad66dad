package cn.powerchina.bjy.link.dam.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 用户信息新增/修改 Request VO")
@Data
public class UserUpdateInfoReqVO {

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "姓名不能为空")
    @Length(max = 32)
    private String name;

    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @Length(max = 32)
    private String deptName;

    @Schema(description = "职务", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @Length(max = 32)
    private String postName;

}