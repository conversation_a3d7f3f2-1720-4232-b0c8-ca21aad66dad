package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 工程分类管理新增/修改 Request VO")
@Data
@Builder
public class ProjectCategorySaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @NotNull(message = "请选择一个项目")
    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "父节点id")
    private Long parentId;

    @Schema(description = "节点名称")
    private String categoryName;

    @Schema(description = "节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组）")
    @NotNull(message = "请输入节点类型")
    private Integer categoryType;

    @Schema(description = "描述")
    private String categoryRemark;

    @Schema(description = "图片名称")
    private String imageName;

    @Schema(description = "图片地址")
    private String imagePath;

    @Schema(description = "业务id")
    private Long businessId;

}