package cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodeltemplate;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodeltemplate.InstrumentModelTemplateDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仪器类型模板-测量分量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentModelTemplateMapper extends BaseMapperX<InstrumentModelTemplateDO> {

    default PageResult<InstrumentModelTemplateDO> selectPage(InstrumentModelTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstrumentModelTemplateDO>()
                .eqIfPresent(InstrumentModelTemplateDO::getInstrumentId, reqVO.getInstrumentId())
                .likeIfPresent(InstrumentModelTemplateDO::getThingName, reqVO.getThingName())
                .eqIfPresent(InstrumentModelTemplateDO::getThingIdentity, reqVO.getThingIdentity())
                .eqIfPresent(InstrumentModelTemplateDO::getThingUnit, reqVO.getThingUnit())
                .eqIfPresent(InstrumentModelTemplateDO::getDataType, reqVO.getDataType())
                .eqIfPresent(InstrumentModelTemplateDO::getDownLimit, reqVO.getDownLimit())
                .eqIfPresent(InstrumentModelTemplateDO::getUpLimit, reqVO.getUpLimit())
                .eqIfPresent(InstrumentModelTemplateDO::getDecimalLimit, reqVO.getDecimalLimit())
                .eqIfPresent(InstrumentModelTemplateDO::getThingType, reqVO.getThingType())
                .eqIfPresent(InstrumentModelTemplateDO::getThingWeight, reqVO.getThingWeight())
                .betweenIfPresent(InstrumentModelTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InstrumentModelTemplateDO::getId));
    }

}