package cn.powerchina.bjy.link.dam.service.formulapoint;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointListVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo.PointFormulaModelVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulapoint.FormulaPointDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 公式关联测点 Service 接口
 *
 * <AUTHOR>
 */
public interface FormulaPointService {

    /**
     * 创建公式关联测点
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFormulaPoint(@Valid FormulaPointSaveReqVO createReqVO);

    /**
     * 更新公式关联测点
     *
     * @param updateReqVO 更新信息
     */
    void updateFormulaPoint(@Valid FormulaPointSaveReqVO updateReqVO);

    /**
     * 删除公式关联测点
     *
     * @param id 编号
     */
    void deleteFormulaPoint(Long id);

    /**
     * 获得公式关联测点
     *
     * @param id 编号
     * @return 公式关联测点
     */
    FormulaPointDO getFormulaPoint(Long id);

    /**
     * 获得公式关联测点分页
     *
     * @param pageReqVO 分页查询
     * @return 公式关联测点分页
     */
    PageResult<FormulaPointDO> getFormulaPointPage(FormulaPointPageReqVO pageReqVO);

    /**
     * 根据测点公式id查询关联分量
     *
     * @param pointFormulaId
     * @return
     */
    List<PointFormulaModelVO> getFormulaModelByPointFormulaId(Long pointFormulaId);

    /**
     * 查询测点公式关联的测点集合及测点信息
     *
     * @param pointFormulaId
     * @return
     */
    List<FormulaPointListVO> getFormulaPointList(Long pointFormulaId);

    /**
     * 查询测点公式关联的测点集合
     *
     * @param pointFormulaId
     * @return
     */
    List<FormulaPointDO> getFormulaPointByPointFormulaId(Long pointFormulaId);

}