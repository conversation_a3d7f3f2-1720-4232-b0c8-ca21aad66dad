package cn.powerchina.bjy.link.dam.controller.admin.projectmenu.bo;

import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/29
 */
@Data
public class ProjectMenuBO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目已分配的菜单ID
     */
    private List<Long> menuIds;

    /**
     * 菜单列表
     */
    private List<MenuBO> menuList;

    @Data
    public static class MenuBO {

        /**
         * 菜单编号
         */
        private Long id;
        /**
         * 菜单名称
         */
        private String name;
        /**
         * 父菜单 ID
         */
        private Long parentId;
        /**
         * 类型，参见 MenuTypeEnum 枚举类
         */
        private Integer type;

    }
}
