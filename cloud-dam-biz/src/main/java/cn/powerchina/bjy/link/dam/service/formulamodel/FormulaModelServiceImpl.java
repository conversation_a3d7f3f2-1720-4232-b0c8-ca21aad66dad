package cn.powerchina.bjy.link.dam.service.formulamodel;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointformula.PointFormulaDO;
import cn.powerchina.bjy.link.dam.dal.mysql.formulamodel.FormulaModelMapper;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.FORMULA_MODEL_NOT_EXISTS;

/**
 * 公式关联分量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FormulaModelServiceImpl implements FormulaModelService {

    @Resource
    private FormulaModelMapper formulaModelMapper;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private SnowFlakeUtil snowFlakeUtil;

    @Override
    public Long createFormulaModel(FormulaModelSaveReqVO createReqVO) {
        // 插入
        FormulaModelDO formulaModel = BeanUtils.toBean(createReqVO, FormulaModelDO.class);
        formulaModelMapper.insert(formulaModel);
        // 返回
        return formulaModel.getId();
    }

    @Override
    public void updateFormulaModel(FormulaModelSaveReqVO updateReqVO) {
        FormulaModelDO updateObj = BeanUtils.toBean(updateReqVO, FormulaModelDO.class);
        // 如果数据库中没有数据，id是从前端传递的，先保存数据
        if (formulaModelMapper.selectById(updateReqVO.getId()) == null) {
            formulaModelMapper.insert(updateObj);
        } else {
            // 更新
            formulaModelMapper.updateById(updateObj);
        }
    }

    @Override
    public void deleteFormulaModel(Long id) {
        // 校验存在
        validateFormulaModelExists(id);
        // 删除
        formulaModelMapper.deleteById(id);
    }

    private void validateFormulaModelExists(Long id) {
        if (formulaModelMapper.selectById(id) == null) {
            throw exception(FORMULA_MODEL_NOT_EXISTS);
        }
    }

    @Override
    public FormulaModelRespVO getFormulaModel(Long id) {
        FormulaModelRespVO formulaModelRespVO = new FormulaModelRespVO();
        //第一次编辑，返回一个主键id给前端
        if (Objects.isNull(id)) {
            formulaModelRespVO.setId(snowFlakeUtil.snowflakeId());
            return formulaModelRespVO;
        }

        FormulaModelDO formulaModelDO = formulaModelMapper.selectById(id);
        if (Objects.nonNull(formulaModelDO)) {
            BeanUtil.copyProperties(formulaModelDO, formulaModelRespVO);
            InstrumentModelDO instrumentModel = instrumentModelService.getInstrumentModel(formulaModelDO.getInstrumentModelId());
            if (Objects.nonNull(instrumentModel)) {
                formulaModelRespVO.setThingName(instrumentModel.getThingName());
            }
        }
        return formulaModelRespVO;
    }

    @Override
    public PageResult<FormulaModelDO> getFormulaModelPage(FormulaModelPageReqVO pageReqVO) {
        return formulaModelMapper.selectPage(pageReqVO);
    }

    @Override
    public List<FormulaModelDO> getFormulaModelByPointFormulaId(Long pointFormulaId) {
        return formulaModelMapper.selectList(new LambdaQueryWrapperX<FormulaModelDO>()
                .eq(FormulaModelDO::getPointFormulaId, pointFormulaId));
    }

}