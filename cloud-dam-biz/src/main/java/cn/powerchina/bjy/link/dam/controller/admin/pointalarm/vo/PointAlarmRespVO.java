package cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点报警信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointAlarmRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "监测时间")
    @ExcelProperty("监测时间")
    private LocalDateTime pointTime;

    @Schema(description = "分量id")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "测值")
    @ExcelProperty("测值")
    private String pointData;

    @Schema(description = "报警内容")
    @ExcelProperty("报警内容")
    private String alarmContent;

    @Schema(description = "报警时间")
    @ExcelProperty("报警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "处理状态，0：未处理，1：已处理")
    @ExcelProperty("处理状态，0：未处理，1：已处理")
    private Integer solutionStatus;

    @Schema(description = "处理情况")
    @ExcelProperty("处理情况")
    private String solutionContent;

    @Schema(description = "处理人id")
    @ExcelProperty("处理人id")
    private String solutionUserId;

    @Schema(description = "处理人姓名")
    @ExcelProperty("处理人姓名")
    private String solutionUserName;

    @Schema(description = "处理时间")
    @ExcelProperty("处理时间")
    private LocalDateTime solutionTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}