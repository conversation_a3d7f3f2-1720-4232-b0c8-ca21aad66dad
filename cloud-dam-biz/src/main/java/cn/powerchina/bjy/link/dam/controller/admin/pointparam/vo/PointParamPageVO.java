package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点计算参数分页 Request VO")
@Data
public class PointParamPageVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25970")
    private Long id;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "仪器类型")
    private String instrumentName;

    @Schema(description = "测点状态")
    private Integer pointState;

    @Schema(description = "测点参数")
    private String calcParam;

    @Schema(description = "有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    private Integer applyType;

}