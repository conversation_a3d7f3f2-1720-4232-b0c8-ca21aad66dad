package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice.IotDeviceDO;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.iotdevice.IotDeviceService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceSave;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Optional;

/**
 * 设备创建消息处理器
 */
@Component
public class DeviceCreateMsgProcessor implements MsgProcessor {

    @Autowired
    private IotDeviceService iotDeviceService;

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    public void process(String payload) {
        MqttReceiveData<DeviceSave> deviceData = JsonUtils.parseObject(payload, new TypeReference<MqttReceiveData<DeviceSave>>(){});
        DeviceSave deviceSave = deviceData.getMessage();

        // 新增物联网平台同步的设备
        LocalDateTime lastUpTime = Optional.ofNullable(deviceSave.getLastUpTime()).map(LocalDateTime::parse)
                .orElse(null);

        Boolean shadow = deviceSave.getShadow();

        IotDeviceDO iotDeviceDO = new IotDeviceDO();
        iotDeviceDO.setIotId(deviceSave.getId());
        iotDeviceDO.setProductCode(deviceSave.getProductCode());
        iotDeviceDO.setProjectCode(deviceSave.getProjectCode());
        iotDeviceDO.setDeviceName(deviceSave.getDeviceName());
        iotDeviceDO.setDeviceCode(deviceSave.getDeviceCode());
        iotDeviceDO.setParentCode(deviceSave.getParentCode());
        iotDeviceDO.setParentName(deviceSave.getParentName());
        iotDeviceDO.setDeviceSerial(deviceSave.getDeviceSerial());
        if (Objects.nonNull(shadow)) {
            iotDeviceDO.setShadow(shadow ? 1 : 0);
        }
        iotDeviceDO.setChannelCode(deviceSave.getChannelCode());
        iotDeviceDO.setMcuChannel(deviceSave.getMcuChannel());
        iotDeviceDO.setSlaveId(deviceSave.getSlaveId());
        iotDeviceDO.setDistributeState(deviceSave.getDistributeState());
        iotDeviceDO.setLinkState(deviceSave.getLinkState());
        iotDeviceDO.setRegisterState(deviceSave.getRegisterState());
        iotDeviceDO.setNodeType(deviceSave.getNodeType());
        iotDeviceDO.setExtra(deviceSave.getExtra());
        iotDeviceDO.setLastUpTime(lastUpTime);
        iotDeviceDO.setEdgeCode(deviceSave.getEdgeCode());
        iotDeviceDO.setDriverCode(deviceSave.getDriverCode());
        iotDeviceDO.setProductName(deviceSave.getProductName());
        iotDeviceDO.setRemark(deviceSave.getRemark());
        iotDeviceDO.setFirmName(deviceSave.getFirmName());
        iotDeviceDO.setProductModel(deviceSave.getProductModel());
        iotDeviceDO.setResourceSpaceId(deviceSave.getResourceSpaceId());
        iotDeviceDO.setSpaceName(deviceSave.getSpaceName());
        iotDeviceDO.setLongitude(deviceSave.getLongitude());
        iotDeviceDO.setLatitude(deviceSave.getLatitude());
        iotDeviceDO.setIotCreateTime(LocalDateTime.parse(deviceSave.getCreateTime()));

        iotDeviceService.createIotDevice(iotDeviceDO);
    }

    /**
     * 获取当前消息处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.DEVICE_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.DEVICE_CREATE.getDataType();
    }
}
