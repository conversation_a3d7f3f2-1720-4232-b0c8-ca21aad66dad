package cn.powerchina.bjy.link.dam.dal.dataobject.indexhead;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 大坝首页头部信息 DO
 *
 * <AUTHOR>
 */
@TableName("dam_index_head")
@KeySequence("dam_index_head_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexHeadDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 监测仪器数量
     */
    private Long pointDeviceTotal;
    /**
     * 监测仪器今日新增数量
     */
    private Long pointDeviceToday;
    /**
     * 监测数据数量
     */
    private Long pointDataTotal;
    /**
     * 监测数据今日新增数量
     */
    private Long pointDataToday;
    /**
     * 自动化设备数量
     */
    private Long deviceMcuTotal;
    /**
     * 自动化设备今日新增数量
     */
    private Long deviceMcuToday;
    /**
     * 在测自动化仪器数量
     */
    private Long deviceMcuRunTotal;
    /**
     * 在测自动化仪器今日新增数量
     */
    private Long deviceMcuRunToday;
    /**
     * 自动化监测数据数量
     */
    private Long pointDataMcuTotal;
    /**
     * 自动化监测数据今日新增数量
     */
    private Long pointDataMcuToday;

    /**
     * 生成时间
     */
    private String generateTime;

}
