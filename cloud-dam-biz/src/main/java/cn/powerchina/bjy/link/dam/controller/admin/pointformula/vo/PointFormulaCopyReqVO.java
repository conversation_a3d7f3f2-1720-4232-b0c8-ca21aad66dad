package cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: dzj
 * @CreateDate: 2024/12/23
 */
@Schema(description = "管理后台 - 分组测点信息新增/修改 Request VO")
@Data
public class PointFormulaCopyReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "请选择一个项目")
    private Long projectId;

    @Schema(description = "测点id集合")
    private List<Long> pointIds;

    @Schema(description = "当前测点id")
    @NotNull(message = "请选择当前测点")
    private Long pointId;
}
