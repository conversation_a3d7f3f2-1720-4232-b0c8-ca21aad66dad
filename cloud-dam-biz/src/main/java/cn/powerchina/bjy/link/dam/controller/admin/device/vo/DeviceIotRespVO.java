package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * IoT设备信息
 */
@Schema(description = "管理后台 - IoT设备信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceIotRespVO {
    @Schema(description = "设备id")
    private Long id;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品描述")
    private String description;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "设备标识")
    private String deviceSerial;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "最后上线时间")
    private LocalDateTime lastUpTime;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;
}
