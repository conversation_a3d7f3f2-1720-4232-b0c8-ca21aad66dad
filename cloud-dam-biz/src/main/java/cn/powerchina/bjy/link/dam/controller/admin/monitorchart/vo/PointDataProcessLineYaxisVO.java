package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-过程线--y轴
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "y轴")
@Data
public class PointDataProcessLineYaxisVO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "位置")
    private String position;

    @Schema(description = "是否对齐")
    private Boolean alignTicks;

    @Schema(description = "轴线")
    private PointDataProcessLineYaxisLineVO axisLine;

    @Schema(description = "刻度")
    private PointDataProcessLineAxisTickVO axisTick;

    @Schema(description = "splitLine")
    private PointDataProcessLineYsplitLineVO splitLine;

    @Schema(description = "nameTextStyle")
    private PointDataProcessLineYaxisNameTextStyleVO nameTextStyle;

    @Schema(description = "offset")
    private Integer offset;

    @Schema(description = "axisLabel")
    private PointDataProcessLineYaxisLabelVO axisLabel;
}
