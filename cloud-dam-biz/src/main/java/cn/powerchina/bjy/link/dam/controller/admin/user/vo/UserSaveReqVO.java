package cn.powerchina.bjy.link.dam.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 用户信息新增/修改 Request VO")
@Data
public class UserSaveReqVO {

    @Schema(description = "主键id", example = "28308")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "用户账号不能为空")
    @Length(min = 4, max = 16, message = "账号长度为 4-16 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;

    @Schema(description = "密码",  example = "123456")
    @Length(max = 32)
    private String password;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "姓名不能为空")
    @Length(max = 32)
    private String name;

    @Schema(description = "手机号码")
    @Length(max = 11)
    private String mobile;

    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @Length(max = 32)
    private String deptName;

    @Schema(description = "职务", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @Length(max = 32)
    private String postName;

    @Schema(description = "用户类型:(1系统管理员，2项目管理员，3普通用户)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "用户类型:(1系统管理员，2项目管理员，3普通用户)不能为空")
    private Integer userType;

    @Schema(description = "启用状态（0正常 1停用）",  example = "0")
    @NotNull(message = "启用状态（0正常 1停用）不能为空")
    private Integer status;

}