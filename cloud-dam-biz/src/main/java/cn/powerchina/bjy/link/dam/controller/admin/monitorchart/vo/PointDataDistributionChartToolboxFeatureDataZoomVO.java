package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "dataZoom")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartToolboxFeatureDataZoomVO {

    @Schema(description = "show")
    private Boolean show;

    @Schema(description = "yAxisIndex")
    @JsonProperty(value = "yAxisIndex")
    private String yAxisIndex;
    
}
