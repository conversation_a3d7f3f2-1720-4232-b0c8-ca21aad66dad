package cn.powerchina.bjy.link.dam.service.indexmiddlebottom;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexmiddlebottom.IndexMiddleBottomDO;
import jakarta.validation.Valid;

public interface IndexMiddleBottomService {

    /**
     * 创建首页中间和底部数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIndexMiddleBottom(@Valid IndexMiddleBottomSaveReqVO createReqVO);

    /**
     * 更新首页中间和底部数据
     *
     * @param updateReqVO 更新信息
     */
    void updateIndexMiddleBottom(@Valid IndexMiddleBottomSaveReqVO updateReqVO);

    /**
     * 删除首页中间和底部数据
     *
     * @param id 编号
     */
    void deleteIndexMiddleBottom(Long id);

    /**
     * 获得首页中间和底部数据
     *
     * @param id 编号
     * @return 首页中间和底部数据
     */
    IndexMiddleBottomDO getIndexMiddleBottom(Long id);

    /**
     * 获得首页中间和底部数据分页
     *
     * @param pageReqVO 分页查询
     * @return 首页中间和底部数据分页
     */
    PageResult<IndexMiddleBottomDO> getIndexMiddleBottomPage(IndexMiddleBottomPageReqVO pageReqVO);
}
