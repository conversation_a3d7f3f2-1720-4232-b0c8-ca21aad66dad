package cn.powerchina.bjy.link.dam.dal.mysql.formulapoint;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulapoint.FormulaPointDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 公式关联测点 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FormulaPointMapper extends BaseMapperX<FormulaPointDO> {

    default PageResult<FormulaPointDO> selectPage(FormulaPointPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FormulaPointDO>()
                .eqIfPresent(FormulaPointDO::getPointFormulaId, reqVO.getPointFormulaId())
                .eqIfPresent(FormulaPointDO::getPointId, reqVO.getPointId())
                .eqIfPresent(FormulaPointDO::getInstrumentModelId, reqVO.getInstrumentModelId())
                .eqIfPresent(FormulaPointDO::getApplyType, reqVO.getApplyType())
                .eqIfPresent(FormulaPointDO::getDataCondition, reqVO.getDataCondition())
                .eqIfPresent(FormulaPointDO::getDataValue, reqVO.getDataValue())
                .eqIfPresent(FormulaPointDO::getDataUnit, reqVO.getDataUnit())
                .betweenIfPresent(FormulaPointDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FormulaPointDO::getId));
    }

}