package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/10
 */
@Schema(description = "管理后台 - 测点绑定 Request VO")
@Data
public class DeviceBindReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "绑定类型，0：解绑，1：绑定")
    @NotNull(message = "请选择绑定类型")
    private Integer bindType;

    @Schema(description = "测点id")
    private Long pointId;
}
