package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 大坝设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DevicePageReqVO extends PageParam {

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "父设备名称")
    @ExcelProperty("父设备名称")
    private String parentName;

    @Schema(description = "父设备产品编码")
    @ExcelProperty("父设备产品编码")
    private String parentProductCode;

    @Schema(description = "父设备唯一标识")
    @ExcelProperty("父设备唯一标识")
    private String parentSerial;

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;

    @Schema(description = "测站id")
    private Long stationId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "策略id")
    private Long strategyId;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "在线状态（0-离线；1-在线；）")
    private Integer linkState;

    @Schema(description = "是否绑定测点，0：未绑定，1：已绑定")
    private Integer bindType;

    @Schema(description = "通道号")
    private String mcuChannel;

    @Schema(description = "测点编号")
    private String pointCode;

}