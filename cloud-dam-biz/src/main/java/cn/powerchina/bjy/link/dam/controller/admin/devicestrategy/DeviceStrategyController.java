package cn.powerchina.bjy.link.dam.controller.admin.devicestrategy;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategyPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategyRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategySaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy.DeviceStrategyDO;
import cn.powerchina.bjy.link.dam.service.devicestrategy.DeviceStrategyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备采集策略")
@RestController
@RequestMapping("/dam/device/strategy")
@Validated
public class DeviceStrategyController {

    @Resource
    private DeviceStrategyService deviceStrategyService;

    @PostMapping("/create")
    @Operation(summary = "创建设备采集策略")
//    @PreAuthorize("@ss.hasPermission('dam:device-strategy:create')")
    public CommonResult<Long> createDeviceStrategy(@Valid @RequestBody DeviceStrategySaveReqVO createReqVO) {
        return success(deviceStrategyService.createDeviceStrategy(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备采集策略")
//    @PreAuthorize("@ss.hasPermission('dam:device-strategy:update')")
    public CommonResult<Boolean> updateDeviceStrategy(@Valid @RequestBody DeviceStrategySaveReqVO updateReqVO) {
        deviceStrategyService.updateDeviceStrategy(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备采集策略")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:device-strategy:delete')")
    public CommonResult<Boolean> deleteDeviceStrategy(@RequestParam("id") Long id) {
        deviceStrategyService.deleteDeviceStrategy(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备采集策略")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:device-strategy:query')")
    public CommonResult<DeviceStrategyRespVO> getDeviceStrategy(@RequestParam("id") Long id) {
        DeviceStrategyDO deviceStrategy = deviceStrategyService.getDeviceStrategy(id);
        return success(BeanUtils.toBean(deviceStrategy, DeviceStrategyRespVO.class));
    }


    @GetMapping("/page")
    @Operation(summary = "获得设备采集策略分页")
//    @PreAuthorize("@ss.hasPermission('dam:device-strategy:query')")
    public CommonResult<PageResult<DeviceStrategyRespVO>> getDeviceStrategyPage(@Valid DeviceStrategyPageReqVO pageReqVO) {
        PageResult<DeviceStrategyDO> pageResult = deviceStrategyService.getDeviceStrategyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceStrategyRespVO.class));
    }

}