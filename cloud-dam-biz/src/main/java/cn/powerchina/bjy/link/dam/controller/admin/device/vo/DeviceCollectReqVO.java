package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: 设备实时采集
 * @Author: yhx
 * @CreateDate: 2024/9/10
 */
@Schema(description = "管理后台 - 设备实时采集 Request VO")
@Data
public class DeviceCollectReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "请选择一个项目")
    private Long projectId;

    @Schema(description = "设备编码，（，拓扑图页面传设备主键id）")
    private String deviceCode;

}
