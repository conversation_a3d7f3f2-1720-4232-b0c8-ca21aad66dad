package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工程分类管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectCategoryPageReqVO extends PageParam {

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "父节点id")
    private Long parentId;

    @Schema(description = "节点名称")
    private String categoryName;

    @Schema(description = "节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点）")
    private Integer categoryType;

    @Schema(description = "层级")
    private Integer categoryLevel;

    @Schema(description = "业务id")
    private Long businessId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}