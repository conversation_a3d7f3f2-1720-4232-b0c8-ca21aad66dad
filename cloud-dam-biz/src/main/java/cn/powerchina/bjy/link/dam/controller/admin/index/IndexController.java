package cn.powerchina.bjy.link.dam.controller.admin.index;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexHeadBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexPointDeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.vo.IndexHeadRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.index.vo.IndexPointDeviceRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.index.vo.IndexRespVO;
import cn.powerchina.bjy.link.dam.service.index.IndexService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description: 首页相关接口
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Tag(name = "管理后台 - 首页功能")
@RestController
@RequestMapping("/dam/index")
@Validated
public class IndexController {

    @Autowired
    private IndexService indexService;

    @GetMapping("/info")
    @Operation(summary = "获得首页信息")
    @Parameter(name = "projectId", description = "项目id", required = true)
    public CommonResult<IndexRespVO> getIndexInfo(@RequestParam("projectId") Long projectId) {
        IndexBO indexBO = indexService.findIndexInfo(projectId);
        return success(BeanUtils.toBean(indexBO, IndexRespVO.class));
    }

    @GetMapping("/head")
    @Operation(summary = "获得首页头部信息")
    @Parameter(name = "projectId", description = "项目id", required = true)
    public CommonResult<IndexHeadRespVO> getIndexHeadInfo(@RequestParam("projectId") Long projectId) {
        IndexHeadBO indexHeadBO = indexService.findIndexHeadInfo(projectId);
        return success(BeanUtils.toBean(indexHeadBO, IndexHeadRespVO.class));
    }

    @GetMapping("/instrument/middle")
    @Operation(summary = "获得首页仪器类型中间图像列表信息")
    @Parameter(name = "projectId", description = "项目id", required = true)
    public CommonResult<List<IndexPointDeviceRespVO>> getIndexInstrumentMiddleInfo(@RequestParam("projectId") Long projectId) {
        List<IndexPointDeviceBO> deviceBOList = indexService.findIndexInstrumentMiddle(projectId);
        return success(BeanUtils.toBean(deviceBOList, IndexPointDeviceRespVO.class));
    }

    @GetMapping("/instrument/list")
    @Operation(summary = "获得首页仪器类型底部列表信息")
    @Parameter(name = "projectId", description = "项目id", required = true)
    public CommonResult<List<IndexPointDeviceRespVO>> getIndexInstrumentListInfo(@RequestParam("projectId") Long projectId) {
        List<IndexPointDeviceBO> deviceBOList = indexService.findIndexInstrumentList(projectId);
        return success(BeanUtils.toBean(deviceBOList, IndexPointDeviceRespVO.class));
    }
    @GetMapping("/instrument/readIndexData")
    @Operation(summary = "获得首页仪器类型底部列表信息")
    public CommonResult<Boolean> readIndexData() {
        return success(indexService.readIndexData());
    }
}
