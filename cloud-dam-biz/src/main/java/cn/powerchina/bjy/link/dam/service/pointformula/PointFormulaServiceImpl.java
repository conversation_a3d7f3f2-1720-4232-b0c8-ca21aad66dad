package cn.powerchina.bjy.link.dam.service.pointformula;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.EffectiveTimeVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointformula.PointFormulaDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointformula.PointFormulaMapper;
import cn.powerchina.bjy.link.dam.enums.ApplyTypeEnum;
import cn.powerchina.bjy.link.dam.enums.DamConstant;
import cn.powerchina.bjy.link.dam.service.formulapoint.FormulaPointService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.instrumentparam.InstrumentParamService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointparam.PointParamService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 测点计算公式 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointFormulaServiceImpl implements PointFormulaService {

    @Resource
    private PointFormulaMapper pointFormulaMapper;

    @Resource
    private ProjectCategoryService categoryService;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private InstrumentModelMapper instrumentModelMapper;

    @Resource
    private InstrumentService instrumentService;

    @Resource
    private InstrumentParamService instrumentParamService;

    @Resource
    private FormulaPointService formulaPointService;

    @Resource
    private ProjectCategoryService projectCategoryService;

    @Resource
    private PointService pointService;

    @Resource
    private SnowFlakeUtil snowFlakeUtil;

    @Resource
    private PointParamService pointParamService;

    @Override
    @Transactional
    public Long createPointFormula(PointFormulaSaveReqVO createReqVO) {
        getListAndCheckTime(createReqVO);
        // 插入
        PointFormulaDO pointFormula = BeanUtils.toBean(createReqVO, PointFormulaDO.class);
        pointFormulaMapper.insert(pointFormula);
        // 返回
        return pointFormula.getId();
    }

    @Override
    @Transactional
    public void updatePointFormula(PointFormulaSaveReqVO updateReqVO) {
        PointFormulaDO updateObj = BeanUtils.toBean(updateReqVO, PointFormulaDO.class);
        getListAndCheckTime(updateReqVO);
        // 如果数据库中没有数据，id是从前端传递的，先保存数据
        if (pointFormulaMapper.selectById(updateReqVO.getId()) == null) {
            pointFormulaMapper.insert(updateObj);
        } else {
            // 更新
            pointFormulaMapper.updateById(updateObj);
        }
    }

    @Override
    public void deletePointFormula(Long id) {
        // 校验存在
        validatePointFormulaExists(id);
        // 删除
        pointFormulaMapper.deleteById(id);
    }

    private void validatePointFormulaExists(Long id) {
        if (pointFormulaMapper.selectById(id) == null) {
            throw exception(POINT_FORMULA_NOT_EXISTS);
        }
    }

    @Override
    public PointFormulaDO getPointFormula(Long id) {
        PointFormulaDO pointFormulaDO;
        if (Objects.isNull(id)) {
            pointFormulaDO = new PointFormulaDO();
            pointFormulaDO.setId(snowFlakeUtil.snowflakeId());
            return pointFormulaDO;
        }
        return pointFormulaMapper.selectById(id);
    }

    @Override
    public PageResult<PointFormulaPageVO> getPointFormulaPage(PointFormulaPageReqVO pageReqVO) {
        List<Long> pointList = projectCategoryService.getPointListByCategoryId(pageReqVO.getCategoryId());
        //空集合设置值
        if (CollectionUtil.isEmpty(pointList)) {
            pointList.add(Long.MAX_VALUE);
        }
        pageReqVO.setPointIdList(pointList);
        PageResult<PointFormulaPageVO> pointFormulaPageVOPageResult = pointFormulaMapper.selectPage(pageReqVO);
        //增加仪器类型
        ProjectCategoryLevelBO category = projectCategoryService.getProjectCategoryLevelBO(pageReqVO.getCategoryId());
        pointFormulaPageVOPageResult.getList().forEach(item -> {
            item.setInstrumentName(category.getInstrumentCategoryDO().getCategoryName());
        });

        return pointFormulaPageVOPageResult;
    }

    @Override
    public List<InstrumentModelDO> getInstrumentModelByPointId(Long pointId) {
        Long instrumentId = categoryService.getInstrumentIdByPointId(pointId);
        List<InstrumentModelDO> modelDOList = instrumentModelService.getModelByThingType(instrumentId, DamConstant.THING_TYPE);
        return modelDOList;
    }

    @Override
    public List<InstrumentParamDO> getInstrumentParamsByPointId(Long pointId) {
        Long instrumentId = categoryService.getInstrumentIdByPointId(pointId);
        return instrumentParamService.getListByInstrumentId(instrumentId);
    }

    @Override
    public List<PointFormulaModelVO> getFormulaModel(PointFormulaReqVO pointFormulaReqVO) {
        Long instrumentId = categoryService.getInstrumentIdByPointId(pointFormulaReqVO.getPointId());
        List<PointFormulaModelVO> pointFormulaModelVOS = instrumentModelMapper.selectJoinList(PointFormulaModelVO.class, new MPJLambdaWrapperX<InstrumentModelDO>()
                .selectAll(InstrumentModelDO.class).
                        selectAs(FormulaModelDO::getId, PointFormulaModelVO::getFormulaId)
                .selectAs(FormulaModelDO::getDataCondition, PointFormulaModelVO::getDataCondition)
                .leftJoin(FormulaModelDO.class, "f", on -> on.eq(FormulaModelDO::getInstrumentModelId, InstrumentModelDO::getId).eq(FormulaModelDO::getPointFormulaId, pointFormulaReqVO.getPointFormulaId()))
                .eq(InstrumentModelDO::getInstrumentId, instrumentId)
                .orderByDesc(InstrumentModelDO::getCreateTime));
        //关联测点的变量
        List<PointFormulaModelVO> pointModelList = formulaPointService.getFormulaModelByPointFormulaId(pointFormulaReqVO.getPointFormulaId());
        if (CollectionUtil.isNotEmpty(pointModelList)) {
            pointModelList.forEach(item -> {
                item.setThingName(MessageFormat.format("{0}.{1}", item.getPointCode(), item.getThingName()));
                item.setThingIdentity(MessageFormat.format("{0}.{1}", item.getPointCode(), item.getThingIdentity()));
            });
            pointFormulaModelVOS.addAll(pointModelList);
        }
        return pointFormulaModelVOS;
    }

    @Override
    public List<PointFormulaDO> getPointFormulaByPointId(Long pointId) {
        return pointFormulaMapper.selectList(new LambdaQueryWrapperX<PointFormulaDO>()
                .eq(PointFormulaDO::getPointId, pointId));
    }

    /**
     * 新增数据和已有数据进行比较，判断时间段是否有重合
     *
     * @param updateReqVO
     */
    private void getListAndCheckTime(PointFormulaSaveReqVO updateReqVO) {
        if (Objects.nonNull(updateReqVO.getEffectiveStartTime()) && Objects.nonNull(updateReqVO.getEffectiveEndTime())) {
            if (updateReqVO.getEffectiveStartTime().isAfter(updateReqVO.getEffectiveEndTime())) {
                throw exception(POINT_PARAM_TIME_ERROR);
            }
        }
        List<Integer> applyTypeList = new ArrayList<>();
        ApplyTypeEnum applyTypeEnum = ApplyTypeEnum.getByCode(updateReqVO.getApplyType());
        switch (applyTypeEnum) {
            case AUTO -> applyTypeList.addAll(Arrays.asList(ApplyTypeEnum.AUTO.getCode(), ApplyTypeEnum.ALL.getCode()));
            case MANUAL -> applyTypeList.addAll(Arrays.asList(ApplyTypeEnum.MANUAL.getCode(), ApplyTypeEnum.ALL.getCode()));
            default -> applyTypeList.addAll(Arrays.asList(ApplyTypeEnum.MANUAL.getCode(), ApplyTypeEnum.ALL.getCode(), ApplyTypeEnum.AUTO.getCode()));
        }

        //校验时间段重合
        List<PointFormulaDO> pointFormulaDOList = pointFormulaMapper.selectList(new LambdaQueryWrapperX<PointFormulaDO>()
                .eqIfPresent(PointFormulaDO::getInstrumentModelId, updateReqVO.getInstrumentModelId())
                //同一个测点
                .eqIfPresent(PointFormulaDO::getPointId, updateReqVO.getPointId())
                .inIfPresent(PointFormulaDO::getApplyType, applyTypeList).orderByDesc(PointFormulaDO::getEffectiveStartTime));
        //开始和结束时间，一个为空 就算是全空
        List<PointFormulaDO> nullList = pointFormulaDOList.stream().filter(item -> item.getEffectiveStartTime() == null || item.getEffectiveEndTime() == null).toList();
        if (Objects.nonNull(updateReqVO.getId())) {
            //排除本身
            nullList = nullList.stream().filter(item -> !item.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
            pointFormulaDOList = pointFormulaDOList.stream().filter(item -> !item.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(nullList)) {
            throw exception(POINT_EVALUATE_TIME_LAP);
        }
        if (null != updateReqVO.getEffectiveStartTime() && null != updateReqVO.getEffectiveEndTime()) {
            if (updateReqVO.getEffectiveStartTime().isAfter(updateReqVO.getEffectiveEndTime())) {
                throw exception(POINT_PARAM_TIME_LAP);
            }
            //排除时间为空的数据
            pointFormulaDOList = pointFormulaDOList.stream().filter(item -> item.getEffectiveStartTime() != null && item.getEffectiveEndTime() != null).toList();
            if (CollectionUtil.isNotEmpty(pointFormulaDOList)) {
                if (pointParamService.checkTimeOverLap(BeanUtils.toBean(pointFormulaDOList, EffectiveTimeVO.class), BeanUtils.toBean(updateReqVO, EffectiveTimeVO.class))) {
                    throw exception(POINT_PARAM_TIME_LAP);
                }
            }
        } else {
            if (CollectionUtil.isNotEmpty(pointFormulaDOList)) {
                throw exception(POINT_PARAM_TIME_LAP);
            }
        }
    }

    @Override
    @Transactional
    public void copyPointFormula(PointFormulaCopyReqVO reqVO) {
        //找出当前仪器类型拥有的测点
        List<Long> pointIdSelect = Optional.ofNullable(reqVO.getPointIds()).orElse(new ArrayList<>());
        List<PointFormulaDO> pointFormulaDOIdSelect = getPointFormulaByPointId(reqVO.getPointId());
        //查找出需要新增的，新增的就是修改categoryId
        pointIdSelect.forEach(item -> {
            List<PointFormulaDO> PointFormulaDOListAdd = getPointFormulaByPointId(item);
            if (!CollectionUtils.isEmpty(PointFormulaDOListAdd)) {
                PointFormulaDOListAdd.forEach(items -> {
                    deletePointFormula(items.getId());
                });
            }
            pointFormulaDOIdSelect.forEach(items -> {
                items.setPointId(item);
                items.setId(snowFlakeUtil.snowflakeId());
            });
            pointFormulaMapper.insertBatch(pointFormulaDOIdSelect);
        });
    }

    public String getInstrumentNameByPointId(Long pointId) {
        Long instrumentId = categoryService.getInstrumentIdByPointId(pointId);
        InstrumentDO instrument = instrumentService.getInstrument(instrumentId);
        if (instrument == null) {
            return null;
        }
        return instrument.getInstrumentName();
    }

    @Override
    public InstrumentDO getInstrumentByPointId(Long pointId) {
        Long instrumentId = categoryService.getInstrumentIdByPointId(pointId);
        return instrumentService.getInstrument(instrumentId);
    }
}