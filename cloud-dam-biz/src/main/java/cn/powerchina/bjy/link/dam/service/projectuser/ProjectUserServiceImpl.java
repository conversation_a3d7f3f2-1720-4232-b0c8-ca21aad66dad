package cn.powerchina.bjy.link.dam.service.projectuser;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.LoginReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectuser.ProjectUserDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import cn.powerchina.bjy.link.dam.dal.mysql.project.ProjectMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.projectuser.ProjectUserMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.user.UserMapper;
import cn.powerchina.bjy.link.dam.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.dam.enums.UserTypeEnum;
import cn.powerchina.bjy.link.dam.service.user.UserService;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 项目用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProjectUserServiceImpl implements ProjectUserService {

    @Resource
    private ProjectUserMapper projectUserMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserService userService;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleApi roleApi;

    @Resource
    private ProjectMapper projectMapper;

    @Override
    @Transactional
    public Long createProjectUser(ProjectUserSaveReqVO createReqVO) {
        Long userId = userService.createUser(createReqVO);

        // 插入项目用户关系数据
        saveProjectUser(createReqVO.getProjectId(), userId, null);

        //插入用户角色关系表
        Set<Long> userIds = new HashSet<>();
        userIds.add(userId);
        permissionApi.assignUserRole(userIds, new HashSet<>(createReqVO.getRoleIds()), true);

        // 返回
        return userId;
    }

    @Override
    @Transactional
    public void saveProjectUser(Long projectId, Long userId, Long userIdOrigin) {
        //删除旧的绑定关系
        if (Objects.nonNull(userIdOrigin)) {
            projectUserMapper.delete(new LambdaQueryWrapperX<ProjectUserDO>().eq(ProjectUserDO::getProjectId, projectId)
                    .eq(ProjectUserDO::getUserId, userIdOrigin));
        }
        //建立新的关联关系
        ProjectUserDO projectUser = new ProjectUserDO();
        projectUser.setProjectId(projectId);
        projectUser.setUserId(userId);
        projectUserMapper.insert(projectUser);
    }

    @Override
    @Transactional
    public void updateProjectUser(ProjectUserSaveReqVO updateReqVO) {
        //插入用户角色关系表
        Set<Long> userIds = new HashSet<>();
        userIds.add(updateReqVO.getId());
        permissionApi.assignUserRole(userIds, new HashSet<>(updateReqVO.getRoleIds()), false);
        // 更新
        userService.updateUser(updateReqVO);
    }

    @Override
    @Transactional
    public void deleteProjectUser(Long id) {
        // 删除
        userService.deleteUser(id);

        Map<String, Object> map = new HashMap<>();
        map.put("user_id", id);
        projectUserMapper.deleteByMap(map);
    }

    private void validateProjectUserExists(Long id) {
        if (userMapper.selectById(id) == null) {
            throw exception(PROJECT_USER_NOT_EXISTS);
        }
    }

    @Override
    public UserBO getProjectUser(Long id, Long projectId) {
        UserDO userDO = userMapper.selectById(id);
        UserBO userBO = new UserBO();
        BeanUtil.copyProperties(userDO, userBO);
        ProjectDO projectDO = projectMapper.selectById(projectId);
        if (Objects.isNull(projectDO)) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        userBO.setProjectName(projectDO.getProjectName());
        userBO.setProjectId(projectId);

        List<Long> userIds = new ArrayList<>();
        userIds.add(id);
        CommonResult<Map<Long, List<RoleRespDTO>>> mapCommonResult = roleApi.userRoles(userIds);
        List<RoleRespDTO> roleRespDTOS = mapCommonResult.getData().get(id);
        if (!CollectionUtils.isEmpty(roleRespDTOS)) {
            List<Long> roleIds = roleRespDTOS.stream().map(RoleRespDTO::getId).toList();
            userBO.setRoleIds(roleIds);
        }
        return userBO;
    }

    @Override
    public PageResult<UserBO> getProjectUserPage(ProjectUserPageReqVO pageReqVO) {
        ProjectDO projectDO = projectMapper.selectById(pageReqVO.getProjectId());
        if (Objects.isNull(projectDO)) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        //获取system role拼接的后缀
        String suffix = projectDO.getProjectCode().replace(SceneTypeEnum.DAM_CODE.getPrefix(), "");
        PageResult<UserBO> userBOPageResult = userMapper.selectProjectPage(pageReqVO);
        //查找用户id
        List<Long> userIds = userBOPageResult.getList().stream().map(UserBO::getId).toList();
        //根据用户id查找角色
        CommonResult<Map<Long, List<RoleRespDTO>>> mapCommonResult = roleApi.userRoles(userIds);
        if (mapCommonResult.isSuccess()) {
            userBOPageResult.getList().forEach(item -> {
                List<RoleRespDTO> roleRespDTOS = mapCommonResult.getCheckedData().get(item.getId());
                if (!CollectionUtils.isEmpty(roleRespDTOS)) {
                    List<String> roleNameList = roleRespDTOS.stream().map(item2 -> item2.getName().replace("-" + suffix, "")).toList();
                    item.setRoleNames(StringUtil.join(roleNameList, ","));
                }
            });
        } else {
            log.error("call system-api error {}", mapCommonResult.getMsg());
        }
        return userBOPageResult;
    }

    @Override
    public List<ProjectUserDO> getProjectUserByUserId(Long userId) {
        return projectUserMapper.selectList(new LambdaQueryWrapperX<ProjectUserDO>().eq(ProjectUserDO::getUserId, userId));
    }

    @Override
    public void deleteProjectUserByProjectId(Long projectId) {
        projectUserMapper.delete(new LambdaQueryWrapperX<ProjectUserDO>().eq(ProjectUserDO::getProjectId, projectId));
    }

    @Override
    public boolean login(LoginReqVO loginReqVO) {
        //项目管理员类型的用户，判断是否绑定项目
        UserDO user = userService.getUser(loginReqVO.getUsername(), UserTypeEnum.PROJECT_ADMIN.getType());
        if (Objects.nonNull(user)) {
            List<ProjectUserDO> projectUserDOList = getProjectUserByUserId(user.getId());
            if (CollectionUtils.isEmpty(projectUserDOList)) {
                throw exception(PROJECT_USER_NOT_BIND_PROJECT);
            }
        }
        return true;
    }

}