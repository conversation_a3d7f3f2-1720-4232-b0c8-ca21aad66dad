package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.HashSet;

@Schema(description = "管理后台 - 大坝设备新增/修改 Request VO")
@Data
public class DeviceSaveReqVO {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;

    @Schema(description = "测站id")
    private Long stationId;

    @Schema(description = "策略id")
    private Long strategyId;

    @NotNull(message = "采集方式不能为空")
    @Schema(description = "采集方式，1：定点采集，2：间隔采集，3：跨天采集")
    private Integer strategyType;

    @Schema(description = "时间间隔")
    private String timeInterval;

    @Schema(description = "时间点（英文逗号分隔）")
    private  String timePoint;

    public static void main(String[] args) {
        String s1="abc";
        String s2="a" + "b" + "c";
        System.out.println(s1 == s2);

    }
}