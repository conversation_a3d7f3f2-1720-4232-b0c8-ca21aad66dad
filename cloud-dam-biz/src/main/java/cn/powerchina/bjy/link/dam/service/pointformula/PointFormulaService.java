package cn.powerchina.bjy.link.dam.service.pointformula;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointformula.PointFormulaDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 测点计算公式 Service 接口
 *
 * <AUTHOR>
 */
public interface PointFormulaService {

    /**
     * 创建测点计算公式
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointFormula(@Valid PointFormulaSaveReqVO createReqVO);

    /**
     * 更新测点计算公式
     *
     * @param updateReqVO 更新信息
     */
    void updatePointFormula(@Valid PointFormulaSaveReqVO updateReqVO);

    /**
     * 删除测点计算公式
     *
     * @param id 编号
     */
    void deletePointFormula(Long id);

    /**
     * 获得测点计算公式
     *
     * @param id 编号
     * @return 测点计算公式
     */
    PointFormulaDO getPointFormula(Long id);

    /**
     * 获得测点计算公式分页
     *
     * @param pageReqVO 分页查询
     * @return 测点计算公式分页
     */
    PageResult<PointFormulaPageVO> getPointFormulaPage(PointFormulaPageReqVO pageReqVO);

    /**
     * 根据测点id获取仪器分量（中间值和成果值）
     *
     * @param pointId
     * @return
     */
    List<InstrumentModelDO> getInstrumentModelByPointId(Long pointId);

    /**
     * 根据测点id获取仪器参数
     *
     * @param pointId
     * @return
     */
    List<InstrumentParamDO> getInstrumentParamsByPointId(Long pointId);

    /**
     * 根据测点id和测点计算公式id获取计算公式分量
     *
     * @param pointFormulaReqVO
     * @return
     */
    List<PointFormulaModelVO> getFormulaModel(PointFormulaReqVO pointFormulaReqVO);

    /**
     * 根据测点id获取测点计算公式
     *
     * @param pointId
     * @return
     */
    List<PointFormulaDO> getPointFormulaByPointId(Long pointId);

    /**
     * 复制测点公式
     *
     * @param reqVO
     * @return
     */
    void copyPointFormula(PointFormulaCopyReqVO reqVO);

    /**
     * 根据测点id获取仪器name
     *
     * @param pointId
     * @return
     */
    String getInstrumentNameByPointId(Long pointId);

    InstrumentDO getInstrumentByPointId(Long pointId);

}