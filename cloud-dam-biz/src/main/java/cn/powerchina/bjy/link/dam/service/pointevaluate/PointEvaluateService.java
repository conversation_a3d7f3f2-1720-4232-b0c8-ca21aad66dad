package cn.powerchina.bjy.link.dam.service.pointevaluate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.bo.PointEvaluateBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 测点评价指标 Service 接口
 *
 * <AUTHOR>
 */
public interface PointEvaluateService {

    /**
     * 创建测点评价指标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointEvaluate(@Valid PointEvaluateSaveReqVO createReqVO);

    /**
     * 更新测点评价指标
     *
     * @param updateReqVO 更新信息
     */
    void updatePointEvaluate(@Valid PointEvaluateSaveReqVO updateReqVO);

    /**
     * 删除测点评价指标
     *
     * @param id 编号
     */
    void deletePointEvaluate(Long id);

    /**
     * 获得测点评价指标
     *
     * @param id 编号
     * @return 测点评价指标
     */
    PointEvaluateDO getPointEvaluate(Long id);

    /**
     * 获得测点评价指标分页
     *
     * @param pageReqVO 分页查询
     * @return 测点评价指标分页
     */
    PageResult<PointEvaluatePageRespVO> getPointEvaluatePage(PointEvaluatePageReqVO pageReqVO);

    /**
     * 查询评价指标集合，没有id的是分量默认的评价指标数据
     *
     * @param pageReqVO
     * @return
     */
    List<PointEvaluatePageRespVO> selectList(PointEvaluateReqVO pageReqVO);

    /**
     * 获得分量对应的评价指标
     *
     * @param pointId           测点id
     * @param instrumentModelId 分量id
     * @return
     */
    List<PointEvaluateDO> getPointEvaluateByPointIdAndModelId(Long pointId, Long instrumentModelId);

    /**
     * 获得测点评价指标
     *
     * @param id
     * @return
     */
    PointEvaluateBO getPointEvaluateBO(Long id);

    String resetEvaluateExtremeId(List<PointEvaluateSaveReqVO> saveReqVOList, Long evaluateExtremeId, Long id);

    List<PointEvaluatePageRespVO> getSelectedPoint(Long extremeId);
}