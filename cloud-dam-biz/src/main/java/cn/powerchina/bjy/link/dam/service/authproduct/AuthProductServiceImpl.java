package cn.powerchina.bjy.link.dam.service.authproduct;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.bo.AuthProductBO;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo.AuthProductPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct.IotProductDO;
import cn.powerchina.bjy.link.dam.dal.mysql.authproduct.AuthProductMapper;
import cn.powerchina.bjy.link.dam.service.iotproduct.IotProductService;
import cn.powerchina.bjy.link.iot.api.product.ProductApi;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductRespDTO;
import cn.powerchina.bjy.link.iot.enums.NodeTypeEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 产品授权 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AuthProductServiceImpl implements AuthProductService {

    @Resource
    private AuthProductMapper authProductMapper;

    @Autowired
    private IotProductService iotProductService;

    @Resource
    private ProductApi productApi;

    @Override
    public AuthProductDO getAuthProduct(Long id) {
        return authProductMapper.selectById(id);
    }

    @Override
    public PageResult<AuthProductDO> getAuthProductPage(AuthProductPageReqVO pageReqVO) {
        return authProductMapper.selectPage(pageReqVO);
    }

    @Override
    public AuthProductDO findProductByProjectIdProductCode(Long projectId, String productCode) {
        return authProductMapper.selectOne(new LambdaQueryWrapperX<AuthProductDO>()
                .eq(AuthProductDO::getProjectId, projectId)
                .eq(AuthProductDO::getProductCode, productCode));
    }

    @Override
    @Transactional
    public void addAuthProductByProductCode(Long projectId, List<String> productCodeList) {
        //查找数据库里存储的旧的产品授权
        List<AuthProductDO> productDODbList = getAuthProductListByProjectId(projectId);

        //当前项目已经存在的产品编码
        List<String> productCodeDbList = productDODbList.stream().map(AuthProductDO::getProductCode).toList();

        //查找需要增加的产品信息
        List<AuthProductDO> productDOAddList = new ArrayList<>();
        List<IotProductDO> iotProductDOList = iotProductService.listByProductCode(productCodeList);
        iotProductDOList.forEach(iotProductDO -> {
            if (!productCodeDbList.contains(iotProductDO.getProductCode())) {
                AuthProductDO authProductDO = new AuthProductDO();
                authProductDO.setProjectId(projectId);
                authProductDO.setProductCode(iotProductDO.getProductCode());
                authProductDO.setProductName(iotProductDO.getProductName());
                authProductDO.setFirmName(iotProductDO.getFirmName());
                authProductDO.setProductModel(iotProductDO.getProductModel());
                authProductDO.setNodeType(iotProductDO.getNodeType());
                productDOAddList.add(authProductDO);
            }
        });

        //插入新的产品授权
        if (!CollectionUtils.isEmpty(productDOAddList)) {
            authProductMapper.insertBatch(productDOAddList);
        }
    }

    @Override
    public List<AuthProductBO> getAuthProductSelectList(Long projectId) {
        List<AuthProductDO> productDOList = getAuthProductListByProjectId(projectId);
        return productDOList.stream().map(item -> AuthProductBO.builder().productCode(item.getProductCode())
                .productName(item.getProductName()).build()).distinct().toList();
    }

    @Override
    public List<AuthProductDO> getAuthProductListByProjectId(Long projectId) {
        return authProductMapper.selectList(new LambdaQueryWrapperX<AuthProductDO>()
                .eqIfPresent(AuthProductDO::getProjectId, projectId).orderByDesc(AuthProductDO::getId));
    }

    @Override
    public void deleteAuthProductByProductCode(Long projectId, List<String> productCodeList) {
        List<AuthProductDO> authProductDOS = authProductMapper.selectList(new LambdaQueryWrapperX<AuthProductDO>()
                .eqIfPresent(AuthProductDO::getProjectId, projectId).in(AuthProductDO::getProductCode, productCodeList));
        if (!CollectionUtils.isEmpty(authProductDOS)) {
            List<Long> ids = authProductDOS.stream().map(AuthProductDO::getId).collect(Collectors.toList());
            authProductMapper.deleteBatchIds(ids);
        }
    }

    /**
     * 根据产品编码删除产品授权
     *
     * @param productCode 产品编码
     */
    @Override
    public void deleteByProductCode(String productCode) {
        authProductMapper.delete(new LambdaQueryWrapperX<AuthProductDO>().eq(AuthProductDO::getProductCode, productCode));
    }

    /**
     * 根据产品编码获取产品授权
     *
     * @param productCode 产品编码
     * @return 产品授权
     */
    @Override
    public AuthProductDO getByProductCode(String productCode) {
        List<AuthProductDO> authProductDOList = authProductMapper.selectList(
                new LambdaQueryWrapperX<AuthProductDO>().eq(AuthProductDO::getProductCode, productCode));
        return authProductDOList.stream().findFirst().orElse(null);
    }

    /**
     * 根据产品编码更新产品授权
     *
     * @param authProductDO 产品授权信息
     */
    @Override
    public void updateByProductCode(AuthProductDO authProductDO) {
        authProductMapper.update(new LambdaUpdateWrapper<AuthProductDO>()
                .set(AuthProductDO::getProductName, authProductDO.getProductName())
                .set(AuthProductDO::getFirmName, authProductDO.getFirmName())
                .set(AuthProductDO::getProductModel, authProductDO.getProductModel())
                .set(AuthProductDO::getNodeType, authProductDO.getNodeType())
                .eq(AuthProductDO::getProductCode, authProductDO.getProductCode()));
    }

}