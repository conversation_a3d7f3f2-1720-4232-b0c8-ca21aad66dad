package cn.powerchina.bjy.link.dam.service.projectrole;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRolePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRoleRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.ProjectRoleSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo.RoleMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectrole.ProjectRoleDO;
import jakarta.validation.Valid;

/**
 * 项目角色信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectRoleService {

    /**
     * 创建项目角色信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectRole(@Valid ProjectRoleSaveReqVO createReqVO);

    /**
     * 更新项目角色信息
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectRole(@Valid ProjectRoleSaveReqVO updateReqVO);

    /**
     * 删除项目角色信息
     *
     * @param id 编号
     */
    void deleteProjectRole(Long id);

    /**
     * 获得项目角色信息
     *
     * @param id 编号
     * @return 项目角色信息
     */
    ProjectRoleDO getProjectRole(Long id);

    /**
     * 获得项目角色信息分页
     *
     * @param pageReqVO 分页查询
     * @return 项目角色信息分页
     */
    PageResult<ProjectRoleRespVO> getProjectRolePage(ProjectRolePageReqVO pageReqVO);

    /**
     * 保存角色菜单权限
     *
     * @param roleMenuSaveReqVO
     * @return
     */
    Long saveRoleMenu(RoleMenuSaveReqVO roleMenuSaveReqVO);

    /**
     * 获得项目角色信息
     *
     * @param projectId
     * @param roleName
     * @return 项目角色信息
     */
    ProjectRoleDO getProjectRoleByRoleName(Long projectId, String roleName);

}