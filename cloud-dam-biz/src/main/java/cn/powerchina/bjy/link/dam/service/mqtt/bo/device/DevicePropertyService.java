package cn.powerchina.bjy.link.dam.service.mqtt.bo.device;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description: 设备属性值
 * @Author: AI Assistant
 * @CreateDate: 2025/8/1
 */
@Data
public class DevicePropertyService {

    /**
     * 属性值
     */
    private Map<String, String> properties;

    /**
     * 上报时间
     */
    private Long reportTime;

    private String serviceId;

}
