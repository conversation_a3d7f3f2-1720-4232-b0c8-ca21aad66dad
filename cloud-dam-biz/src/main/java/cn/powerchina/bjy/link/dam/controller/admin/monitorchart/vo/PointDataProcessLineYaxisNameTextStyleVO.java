package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: yangjingtao
 * @CreateDate: 2025/06/30
 */
@Schema(description = "nameTextStyle")
@Data
public class PointDataProcessLineYaxisNameTextStyleVO {

    @Schema(description = "align")
    private String align;

    @Schema(description = "color")
    private String color;

    @Schema(description = "fontWeight")
    private String fontWeight;

    @Schema(description = "padding")
    private List<Integer> padding;
}
