package cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo;

import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplateSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 仪器类型模板新增/修改 Request VO")
@Data
public class InstrumentTemplateSaveListReqVO {

    @Valid
    @Schema(description = "仪器类型模板新增参数")
    private InstrumentTemplateSaveReqVO instrumentTemplateRespVO;

    @Valid
    @Schema(description = "计算参数List")
    private List<InstrumentParamTemplateSaveReqVO> instrumentParamTemplateSaveReqVOS;

    @Valid
    @Schema(description = "测量分量List")
    private List<InstrumentModelTemplateSaveReqVO> instrumentModelTemplateSaveReqVOS;

}