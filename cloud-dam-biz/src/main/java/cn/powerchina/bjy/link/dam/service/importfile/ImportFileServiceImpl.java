package cn.powerchina.bjy.link.dam.service.importfile;

import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.tenant.core.context.TenantContextHolder;
import cn.powerchina.bjy.cloud.framework.tenant.core.util.TenantUtils;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.ImportConfigModeSaveVo;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.ImportConfigModelVO;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.ImportConfigPointVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointDataJsonSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigPointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importerrorlog.ImportErrorLogDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importtask.ImportTaskDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigPointMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.importerrorlog.ImportErrorLogMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.importnode.ImportNodeMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.importtask.ImportTaskMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointdatajson.PointDataJsonMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointevaluate.PointEvaluateMapper;
import cn.powerchina.bjy.link.dam.dal.tdengine.DamPointDataMapper;
import cn.powerchina.bjy.link.dam.enums.DataStatusEnum;
import cn.powerchina.bjy.link.dam.service.importconfig.ImportConfigService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import cn.powerchina.bjy.link.dam.service.pointevaluate.PointEvaluateService;
import cn.powerchina.bjy.link.dam.service.user.UserService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import cn.hutool.core.io.IoUtil;
import cn.powerchina.bjy.cloud.infra.api.file.FileApi;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importfile.ImportFileDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;

import cn.powerchina.bjy.link.dam.dal.mysql.importfile.ImportFileMapper;
import org.springframework.web.multipart.MultipartFile;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;


/**
 * 导入信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ImportFileServiceImpl implements ImportFileService {

    @Resource
    private ImportFileMapper importFileMapper;
    @Resource
    private ImportNodeMapper importNodeMapper;
    @Resource
    private PointDataJsonService pointDataJsonService;
    @Resource
    private ImportConfigMapper importConfigMapper;
    @Resource
    private ImportConfigService importConfigService;
    @Resource
    private InstrumentModelMapper instrumentModelMapper;
    @Resource
    private InstrumentService instrumentService;
    @Resource
    private PointService pointService;
    @Resource
    private ImportTaskMapper importTaskMapper;
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private PointDataService pointDataService;
    @Resource
    private PointDataJsonMapper pointDataJsonMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private ImportErrorLogMapper importErrorLogMapper;
    @Resource
    private DamPointDataMapper damPointDataMapper;
    @Resource
    private UserService userService;
    @Resource
    private ImportConfigPointMapper importConfigPointMapper;
    @Resource
    private PointMapper pointMapper;
    @Resource
    private PointEvaluateService pointEvaluateService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private PointEvaluateMapper pointEvaluateMapper;
    public static final int STATUS_PROCESSING = 0; // 执行中
    public static final int STATUS_SUCCESS = 1;    // 成功
    public static final int STATUS_FAILED = 2;     // 失败
    public static final int STATUS_PARTIAL = 3;    // 部分成功

    @Override
    public Long createImportFile(ImportFileSaveReqVO createReqVO) {
        // 插入
        ImportFileDO importFile = BeanUtils.toBean(createReqVO, ImportFileDO.class);
        importFileMapper.insert(importFile);

        // 返回
        return importFile.getId();
    }

    @Override
    public void updateImportFile(ImportFileSaveReqVO reqVO) throws Exception {
            // 3. 更新文件内容但保留配置
            InputStream inputStream = reqVO.getFile().getInputStream();
            Workbook workbook = WorkbookFactory.create(inputStream);
            // 获取文件名
            String fileName = reqVO.getFile().getOriginalFilename();
            reqVO.setFileName(fileName);
            reqVO.setPointNumber(0);
            ImportFileDO updateFile = BeanUtils.toBean(reqVO, ImportFileDO.class);
            updateFile.setUpdateTime(LocalDateTime.now());
            String url = uploadFile(reqVO);
            updateFile.setFileUrl(url);
            importFileMapper.updateById(updateFile);
            reqVO.setFileUrl(url);
            importConfigService.createImportConfig(reqVO);
        }

    @Override
    public void deleteImportFile(Long id) {
        // 校验存在
        validateImportFileExists(id);
        // 删除
        importFileMapper.deleteById(id);
    }

    @Override
    public void deleteImportFileListByIds(List<Long> ids) {
        // 删除
        importFileMapper.deleteBatchIds(ids);
    }


    private void validateImportFileExists(Long id) {
        if (importFileMapper.selectById(id) == null) {
            throw exception(new ErrorCode(500, "数据不存在"));
        }
    }

    @Override
    public ImportFileDO getImportFile(Long id) {
        return importFileMapper.selectById(id);
    }

    @Override
    public PageResult<ImportFileDO> getImportFilePage(ImportFilePageReqVO pageReqVO) {
        return importFileMapper.selectPage(pageReqVO);
    }

    private String genFilePrePath(String projectId) {
        LocalDateTime now = LocalDateTime.now();
        return String.format("%s/", projectId);
    }


    public String uploadFile(ImportFileSaveReqVO fileUploadDTO) throws Exception {
        try {
            String path = genFilePrePath(String.valueOf(fileUploadDTO.getProjectId()));
            String url = fileApi.createFile(fileUploadDTO.getFileName(), path, IoUtil.readBytes(fileUploadDTO.getFile().getInputStream()));
            ImportFileDO filedo = importFileMapper.selectOne(new LambdaQueryWrapperX<ImportFileDO>()
                    .eq(ImportFileDO::getId, fileUploadDTO.getId())
            );
            filedo.setFileUrl(url);
            importFileMapper.updateById(filedo);
            return url;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

//    @Override
//    public String initiateMultipartUpload(FileUploadFragmentationDTO fileUploadDTO) throws Exception {
//        //分片上传的分路径
//        String path = genFilePrePath(String.valueOf(fileUploadDTO.getProjectId()))  + fileUploadDTO.getFileName() + "_" + fileUploadDTO.getTotalChunks() + "_" + fileUploadDTO.getIndex();
//        String type = FileTypeUtils.getMineType(fileUploadDTO.getContent(), fileUploadDTO.getFile().getOriginalFilename());
//        // 如果 name 为空，则使用 pathName 填充
//        String name = fileUploadDTO.getFileName();
//
//        String url = initiateMultipartUpload(path, type, fileUploadDTO.getContent());
//
//        return name;
//    }
//
//    public String initiateMultipartUpload(String path, String type, byte[] content) throws Exception {
//        FileUtil.writeBytes(content, path);
//        return "";
//    }

//
//    @Override
//    public String mergeChunksUpload(FileUploadFragmentationDTO fileUploadDTO) throws Exception {
//        //分片上传合并后路径
//
////        String type = FileTypeUtils.getMineType(fileUploadDTO.getContent(), fileUploadDTO.getFile().getOriginalFilename());
////        // 如果 name 为空，则使用 pathName 填充
////        String name = fileUploadDTO.getFileName();
//
////        File file = new File(path);
//        URL url1 = new URL(url);
//        HttpURLConnection connection = (HttpURLConnection) url1.openConnection();
//        connection.setRequestMethod("GET");
//
//        InputStream inputStream = connection.getInputStream();
//        //以上这样我们就得到了一个文件流；不管是视频，图片，音频，文本文件，都是可以的
//        //返回文件流
//        Workbook workbook = new XSSFWorkbook(inputStream);
//        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
//            Sheet sheet = workbook.getSheetAt(i);
//            System.out.println(sheet.getSheetName());
//        }
//        inputStream.close();
////        Long id;
////        Integer size;
//        try {

    /// /            url = mergeChunksUpload(path, fileUploadDTO.getTotalChunks());
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        return url;
//    }
//
//    public String mergeChunksUpload(String path, Integer totalChunks) throws Exception {
//        String filePath = path;
//
//        byte[] bytes = new byte[1024];
//        int len;
//        try (
//                FileOutputStream fileOutputStream = new FileOutputStream(filePath);
//        ) {
//            for (int i = 0; i < totalChunks; i++) {
//                String tp = filePath + "_" + totalChunks + "_" + i;
//                try (FileInputStream fileInputStream = new FileInputStream(tp)) {
//                    while ((len = fileInputStream.read(bytes)) != -1) {
//                        fileOutputStream.write(bytes, 0, len);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        } finally {
//            for (int i = 0; i < totalChunks; i++) {
//                new File(filePath + "_" + totalChunks + "_" + i).delete();
//            }
//        }
//
//        return "";
//    }
    @Override
    public Boolean importExcel(UploadFileVO uploadFileVO) {
        // 获取分布式锁，锁名基于项目ID
        String lockKey = "import:lock:" + uploadFileVO.getProjectId();
        RLock lock = redissonClient.getLock(lockKey);
        // 先检查锁是否已被占用
        if (lock.isLocked()) {
            throw exception(new ErrorCode(500, "当前项目有导入任务正在执行，请稍后再试"));
        }
        try {
            // 尝试获取锁，不设置过期时间
            boolean locked = lock.tryLock(0, TimeUnit.SECONDS); // 0表示无限等待
            if (!locked) {
                throw exception(new ErrorCode(500, "系统繁忙，请稍后再试"));
            }
            Long projectId = uploadFileVO.getProjectId();
            Long nodeId = uploadFileVO.getNodeId();
            if (uploadFileVO.getFiles() == null) {
                throw exception(new ErrorCode(500, "文件不能为空"));
            }


            Long loginUserId = WebFrameworkUtils.getLoginUserId();
                        for (MultipartFile file : uploadFileVO.getFiles()) {
                            System.out.println("开始处理文件：" + file.getOriginalFilename());
                             processFileUpload(file, projectId, nodeId, loginUserId);
                        }
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw exception(new ErrorCode(500, "导入任务被中断"));
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
        private void processFileUpload(MultipartFile file, Long projectId, Long nodeId, Long loginUserId) {
            try {
                String fileName = file.getOriginalFilename();
                ImportFileDO existingFile = importFileMapper.selectOne(new LambdaQueryWrapperX<ImportFileDO>()
                        .eq(ImportFileDO::getFileName, fileName)
                        .eq(ImportFileDO::getProjectId, projectId));
                ImportFileDO importFileDO = new ImportFileDO();
                importFileDO.setProjectId(projectId);
                importFileDO.setNodeId(nodeId);
                importFileDO.setFileName(fileName);
                importFileDO.setPointNumber(0);
                importFileDO.setUploadTime(LocalDateTime.now());
                importFileDO.setCreator(String.valueOf(loginUserId));
                ImportFileSaveReqVO importFileSaveReqVO = BeanUtils.toBean(importFileDO, ImportFileSaveReqVO.class);
                importFileSaveReqVO.setFileName(fileName);
                importFileSaveReqVO.setPointNumber(0);
                importFileSaveReqVO.setUploadTime(LocalDateTime.now());
                importFileSaveReqVO.setId(importFileDO.getId());
                importFileSaveReqVO.setNodeId(nodeId);
                importFileSaveReqVO.setFile(file);
                if (existingFile != null) {
                    existingFile.setFileName(fileName);
                    existingFile.setPointNumber(0);
                    existingFile.setUploadTime(LocalDateTime.now());
                    existingFile.setCreator(String.valueOf(loginUserId));
                    existingFile.setNodeId(nodeId);
                    existingFile.setProjectId(projectId);
                    importFileMapper.updateById(existingFile);
                    importFileSaveReqVO.setId(existingFile.getId());
                } else {
                    importFileMapper.insert(importFileDO);
                    importFileSaveReqVO.setId(importFileDO.getId());
                }
                long tenantId = TenantContextHolder.getTenantId();

                CompletableFuture.runAsync(() -> {
                    TenantUtils.execute(tenantId, () -> {
                        try {
                            importConfigService.createImportConfig(importFileSaveReqVO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                });
                CompletableFuture.runAsync(() -> {
                    TenantUtils.execute(tenantId, () -> {
                        try {
                            uploadFile(importFileSaveReqVO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                });
            } catch (Exception e) {
                log.error("文件上传失败", e);
                throw exception(new ErrorCode(500, "文件上传失败"));
            }
        }
    @Override
    public List<ImportFileRespVO> getFIleList(Long projectId, Long nodeId) {
        ArrayList<ImportFileRespVO> fileRespVOS = new ArrayList<>();
        if (!Objects.nonNull(projectId)) {
            throw exception(new ErrorCode(500, "项目不能为空"));
        }
        if (!Objects.nonNull(nodeId)) {
            throw exception(new ErrorCode(500, "节点不能为空"));
        }
        if(0==nodeId) {
            List<ImportNodeDO> importNodeDOS = importNodeMapper.selectList(new LambdaQueryWrapper<ImportNodeDO>()
                    .eq(ImportNodeDO::getProjectId, projectId));
            for (ImportNodeDO node : importNodeDOS) {
                // 设置节点全名，格式为 父节点/.../本节点
                String fullNodeName = buildFullNodeName(projectId, node);
                List<ImportFileDO> importFileDOs = importFileMapper.selectList(new LambdaQueryWrapper<ImportFileDO>()
                        .eq(ImportFileDO::getNodeId, node.getId())
                        .eq(ImportFileDO::getProjectId, projectId));
                if(importFileDOs.size()>0){
                    for (ImportFileDO importFileDO : importFileDOs) {
                        ImportFileRespVO importFileRespVO = BeanUtils.toBean(importFileDO, ImportFileRespVO.class);
                        importFileRespVO.setNodeName(fullNodeName);
                        fileRespVOS.add(importFileRespVO);
                    }
                }
            }
            // 在返回前对结果集进行排序
            fileRespVOS.sort(Comparator.comparing(ImportFileRespVO::getCreateTime).reversed());
            return fileRespVOS;
        }
        //根据测点id和项目id获得所有子节点数据
        // 查询直接子节点
        ImportNodeDO importNodeDO = importNodeMapper.selectOne(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getProjectId, projectId)
                .eq(ImportNodeDO::getId, nodeId));
        List<ImportNodeDO> allChildNodes = getAllChildNodes(projectId, nodeId);
        allChildNodes.add(importNodeDO);
        for (ImportNodeDO node : allChildNodes) {
            // 设置节点全名，格式为 父节点/.../本节点
            String fullNodeName = buildFullNodeName(projectId, node);
            List<ImportFileDO> importFileDOs = importFileMapper.selectList(new LambdaQueryWrapper<ImportFileDO>()
                    .eq(ImportFileDO::getNodeId, node.getId())
                    .eq(ImportFileDO::getProjectId, projectId));
            if(importFileDOs.size()>0){
                for (ImportFileDO importFileDO : importFileDOs) {
                    ImportFileRespVO importFileRespVO = BeanUtils.toBean(importFileDO, ImportFileRespVO.class);
                    importFileRespVO.setNodeName(fullNodeName);
                    fileRespVOS.add(importFileRespVO);
                }
            }
        }
        // 在返回前对结果集进行排序
        fileRespVOS.sort(Comparator.comparing(ImportFileRespVO::getCreateTime).reversed());
        return fileRespVOS;
    }

    /**
     * 递归构建节点全名，格式为 父节点/.../本节点
     *
     * @param projectId 项目ID
     * @param node      当前节点
     * @return 节点全名
     */
    private String buildFullNodeName(Long projectId, ImportNodeDO node) {
        if (node == null) {
            return "";
        }
        if (node.getParentId() == null || node.getParentId() == 0) {
            return node.getName();
        }
        ImportNodeDO parentNode = importNodeMapper.selectOne(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getProjectId, projectId)
                .eq(ImportNodeDO::getId, node.getParentId()));
        if (parentNode == null) {
            return node.getName();
        }
        return buildFullNodeName(projectId, parentNode) + "/" + node.getName();
    }

@Override
public Boolean importFile(ImportFileVO importVO) {
    // 获取分布式锁，锁名基于项目ID
    String lockKey = "import:lock:" + importVO.getProjectId();
    RLock lock = redissonClient.getLock(lockKey);
    // 先检查锁是否已被占用
    if (lock.isLocked()) {
        throw exception(new ErrorCode(500, "当前项目有导入任务正在执行，请稍后再试"));
    }
    try {
        // 尝试获取锁，不设置过期时间
        boolean locked = lock.tryLock(0, TimeUnit.SECONDS); // 0表示无限等待
        if (!locked) {
            throw exception(new ErrorCode(500, "系统繁忙，请稍后再试"));
        }

        Boolean b = importVO.getUploadNew();
        LocalDate endTime = importVO.getEndTime();
        LocalDate startTime = importVO.getStartTime();
        List<ImportFileSaveReqVO> reqVOS = importVO.getImportFileSaveReqVOS();

        if (!b) {
            if (Objects.isNull(endTime) || Objects.isNull(startTime)) {
                throw exception(new ErrorCode(500, "指定导入起止时间不能为空"));
            }
            if (startTime.isAfter(endTime)) {
                throw exception(new ErrorCode(500, "开始时间不能小于结束时间"));
            }
        }
        for (ImportFileSaveReqVO reqVO : reqVOS) {
            // 创建导入任务
            ImportTaskDO task = new ImportTaskDO();
            task.setProjectId(reqVO.getProjectId());
            task.setNodeId(reqVO.getNodeId());
            task.setFileName(reqVO.getFileName());
            task.setTaskStatus(STATUS_PROCESSING); // 执行中
            task.setTotalNumber(0L);
            task.setUploadNumber(0L);
            task.setUploadTime(reqVO.getUploadTime());
            task.setExecuteTime(LocalDateTime.now());
            UserDO userDO = userService.getUser(WebFrameworkUtils.getLoginUserId());
            task.setCreator(userDO.getName());
            importTaskMapper.insert(task);
            task.setTaskId(task.getId());
            importTaskMapper.updateById(task);
            long tenantId = TenantContextHolder.getTenantId(); //租户Id
            Long loginUserId = WebFrameworkUtils.getLoginUserId();
            // 异步处理导入任务
//            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processImportTask(tenantId, importVO, reqVO, task, b
                        , startTime, endTime, loginUserId,lock);
//            });
        }
        // 立即返回成功，不等待异步任务完成
        return true;
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        throw exception(new ErrorCode(500, "导入任务被中断"));
    } finally {
        // 确保锁被释放
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
//    @Transactional(rollbackFor = Exception.class)
    public void processImportTask( long tenantId,ImportFileVO importVO, ImportFileSaveReqVO reqVO, ImportTaskDO task,
                                  Boolean b, LocalDate startTime, LocalDate endTime,Long loginUserId,RLock lock){
        TenantUtils.execute(tenantId, () -> {
            try {
                if (Objects.isNull(reqVO.getFileUrl())) {
                    throw exception(new ErrorCode(500, reqVO.getFileName() + "表文件路径不存在，请检查文件是否已上传"));
                }
                AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
                URL url1 = new URL(reqVO.getFileUrl());
                HttpURLConnection connection = (HttpURLConnection) url1.openConnection();
                connection.setRequestMethod("GET");
                InputStream inputStream = connection.getInputStream();
                Workbook workbook = WorkbookFactory.create(inputStream);
                int numberOfSheets = workbook.getNumberOfSheets();
                AtomicReference<Integer> pointNum= new AtomicReference<>(0);
                List<ImportConfigPointVO> workAdd = new ArrayList<>();
                Map<Long, ImportConfigModeSaveVo> modelMap = new HashMap<Long, ImportConfigModeSaveVo>();
                Map<Long, Sheet> modelsheet = new HashMap<Long, Sheet>();
                for (int i = 0; i < numberOfSheets; i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    String sheetName = workbook.getSheetName(i) + "&@" + (i + 1);
                    ImportConfigDO importConfigDO = importConfigMapper.selectOne(new LambdaQueryWrapper<ImportConfigDO>()
                            .eq(ImportConfigDO::getImportId, reqVO.getId())
                            .eq(ImportConfigDO::getSheetName, sheetName));
                    if (importConfigDO == null) {
                        continue;
                    }
                    //获取sheet下测点列表
                    List<ImportConfigPointVO> workList = getWorkList(importConfigDO.getId());
                    if (workList.isEmpty()) {
                        continue;
                    }
                    //获取测点下的分量列表
                    for (ImportConfigPointVO importConfigPointVO : workList) {
                        ImportConfigModeSaveVo model = importConfigService.getModelList(importConfigPointVO.getId());
                        if (model.getModelList().size() > 1) {
                            modelMap.put(importConfigPointVO.getId(), model);
                            workAdd.add(importConfigPointVO);
                            pointNum.set(pointNum.get() + 1);
                            modelsheet.put(importConfigPointVO.getId(), sheet);
                        }
                    }
                }
                    if(workAdd.size()>0){
                        for (ImportConfigPointVO importConfigPointVO : workAdd) {
                            Sheet sheet = modelsheet.get(importConfigPointVO.getId());
                            String sheetName = sheet.getSheetName();
                            CompletableFuture.runAsync(() -> {
                                TenantUtils.execute(tenantId, () -> {
                                    Integer uploadNumber=0;
                                    Integer totalNumber=0;
                                    Integer status = DataStatusEnum.UNDETERMINED.getType();
                                    PointDO point = pointService.getPoint(importConfigPointVO.getPointId());
                                    List<InstrumentModelDO> instrumentModelDOList= instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>()
                                            .eq(InstrumentModelDO::getInstrumentId, point.getInstrumentId())
                                            .eq(InstrumentModelDO::getProjectId, importVO.getProjectId()))
                                            ;// 使用已有的getCellValue方法
                                    Map<Long, InstrumentModelDO> insmodelMap=instrumentModelDOList.stream().collect(Collectors.toMap(InstrumentModelDO::getId, instrumentModelDO -> instrumentModelDO));
                                    PointEvaluateReqVO pointEvaluateReqVO = new PointEvaluateReqVO();
                                    pointEvaluateReqVO.setPointIdList(new ArrayList<>(Collections.singletonList(importConfigPointVO.getPointId())));
                                    pointEvaluateReqVO.setProjectId(point.getProjectId());
                                    List<PointEvaluatePageRespVO> pointEvaluateDOS = pointEvaluateMapper.selectList1(pointEvaluateReqVO);
                                    ImportConfigModeSaveVo model= modelMap.get(importConfigPointVO.getId());
                                    pointDataService.defineDevicePropertyData(importVO.getProjectId(), point.getInstrumentId());
                                    Integer startLine = model.getStartLine();
                                    List<ImportConfigModelVO> modelList = model.getModelList();

                                    LocalDate finalEndTime = LocalDate.now();
                                    LocalDate finalStartTime = LocalDate.now();
                                    PointDataJsonDO lastPointDataJson=null;
                                    if(!b){
                                        finalEndTime = endTime;
                                        finalStartTime = startTime;

                                        if (finalStartTime.equals(finalEndTime)) {
                                            finalEndTime = finalEndTime.plusDays(1);
                                        }
                                        try {
                                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                            pointDataJsonService.delByPointTime(reqVO.getProjectId().toString(),
                                                    importConfigPointVO.getInstrumentId().toString(),
                                                    importConfigPointVO.getPointId().toString(),
                                                    model.getIsAutomation() == 0 ? 2 : 1,
                                                    finalStartTime.atStartOfDay().format(formatter),
                                                    finalEndTime.atStartOfDay().format(formatter));
                                        } catch (Exception e) {

                                        }
                                    }else{
                                        lastPointDataJson= pointDataJsonService.getLastPointDataJson(
                                                reqVO.getProjectId(),
                                                importConfigPointVO.getInstrumentId(),
                                                importConfigPointVO.getPointId(), model.getIsAutomation() == 0 ? 2 : 1);
                                    }
                                    for (Row row : sheet) {
                                        boolean isEmptyRow = true;
                                        try {
                                            if (row.getRowNum() < startLine - 1) {
                                                continue;
                                            }
                                            // 检查整行是否为空
                                            if (isRowEmpty(row)) {
                                                continue; // 跳过空行
                                            }
                                            PointDataJsonSaveReqVO pointDataJsonSaveReqVO = new PointDataJsonSaveReqVO();
                                            pointDataJsonSaveReqVO.setPointId(importConfigPointVO.getPointId());

                                            // 根据modelList配置读取各列数据
                                            LocalDateTime executeTime = null;
                                            List<Map<String, Object>> componentDataList = new ArrayList<>();
//
                                            for (ImportConfigModelVO configModel : modelList) {
                                                try {
                                                    if(configModel.getThingColumn()==null){
                                                        throw exception(new ErrorCode(500, configModel.getThingIdentity()+"未配置导入列数！"));
                                                    }
                                                    if ("日期".equals(configModel.getThingIdentity())) {
                                                        Cell executeTimeCell = row.getCell(configModel.getThingColumn() - 1);
                                                        if (executeTimeCell != null) {
                                                            if (executeTimeCell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(executeTimeCell)) {
                                                                executeTime = executeTimeCell.getLocalDateTimeCellValue();
                                                            } else if (executeTimeCell.getCellType() == CellType.STRING) {
                                                                String dateStr = executeTimeCell.getStringCellValue();
                                                                try {
                                                                    // 尝试解析"yyyy/MM/dd"格式
                                                                    executeTime = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd"))
                                                                            .atStartOfDay();
                                                                } catch (Exception e1) {
                                                                    try {
                                                                        // 尝试解析"yyyy-MM-dd"格式
                                                                        executeTime = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                                                                                .atStartOfDay();
                                                                    } catch (Exception e2) {
                                                                        // 如果都失败，尝试原生的LocalDateTime解析
                                                                        executeTime = LocalDateTime.parse(dateStr);
                                                                    }
                                                                }
                                                            }
                                                        } else {
                                                            throw exception(new ErrorCode(500, "日期未配置"));
                                                        }
                                                    }
                                                    if (executeTime == null) {
                                                        throw exception(new ErrorCode(500, "日期格式不正确"));
                                                    }
                                                    if (!"日期".equals(configModel.getThingIdentity())) {
                                                        Cell cell = row.getCell(configModel.getThingColumn() - 1);
                                                        Object cellValue = getCellValue(cell);
                                                        InstrumentModelDO instrumentModelDO= insmodelMap.get(configModel.getInstrumentModelId());
                                                        if (instrumentModelDO.getDataType() != null) {
                                                            switch (instrumentModelDO.getDataType()) {
                                                                case 1: // Integer
                                                                    if (!(cellValue instanceof Integer || cellValue instanceof Long)) {
                                                                        throw exception(new ErrorCode(500, String.format(
                                                                                "数据类型不匹配: 列[%d] 应为整数型, 实际为 %s",
                                                                                configModel.getThingColumn(),
                                                                                cellValue != null ? cellValue.getClass().getSimpleName() : "null"
                                                                        )));
                                                                    }
                                                                    break;
                                                                case 2: // Float
                                                                case 3: // Double
                                                                    if (!(cellValue instanceof Double || cellValue instanceof Float)) {
                                                                        throw exception(new ErrorCode(500, String.format(
                                                                                "数据类型不匹配: 列[%d] 应为浮点型, 实际为 %s",
                                                                                configModel.getThingColumn(),
                                                                                cellValue != null ? cellValue.getClass().getSimpleName() : "null"
                                                                        )));
                                                                    }
                                                                    // 对浮点型数据进行四舍五入处理
                                                                    if (cellValue != null) {
                                                                        int decimalLimit = instrumentModelDO.getDecimalLimit() != null ?
                                                                                instrumentModelDO.getDecimalLimit() : 2;
                                                                        BigDecimal bd = new BigDecimal(cellValue.toString());
                                                                        cellValue = bd.setScale(decimalLimit, RoundingMode.HALF_UP).doubleValue();
                                                                    }
                                                                    break;
                                                                case 4: // Enum
                                                                case 5: // Boolean
                                                                    if (!(cellValue instanceof Boolean)) {
                                                                        throw exception(new ErrorCode(500, String.format(
                                                                                "数据类型不匹配: 列[%d] 应为布尔型, 实际为 %s",
                                                                                configModel.getThingColumn(),
                                                                                cellValue != null ? cellValue.getClass().getSimpleName() : "null"
                                                                        )));
                                                                    }
                                                                    break;
                                                                case 6: // Text
                                                                    if (!(cellValue instanceof String)) {
                                                                        throw exception(new ErrorCode(500, String.format(
                                                                                "数据类型不匹配: 列[%d] 应为文本型, 实际为 %s",
                                                                                configModel.getThingColumn(),
                                                                                cellValue != null ? cellValue.getClass().getSimpleName() : "null"
                                                                        )));
                                                                    }
                                                                    break;
                                                                case 7: // Date
                                                                    if (!(cellValue instanceof LocalDateTime)) {
                                                                        throw exception(new ErrorCode(500, String.format(
                                                                                "数据类型不匹配: 列[%d] 应为日期型, 实际为 %s",
                                                                                configModel.getThingColumn(),
                                                                                cellValue != null ? cellValue.getClass().getSimpleName() : "null"
                                                                        )));
                                                                    }
                                                                    break;
                                                            }
                                                        }
                                                        Map<String, Object> componentData = new HashMap<>();
                                                        componentData.put("thingName", instrumentModelDO.getThingName());
                                                        componentData.put("thingIdentity", configModel.getThingIdentity());
                                                        componentData.put("instrumentModelId", configModel.getInstrumentModelId());
                                                        componentData.put("thingValue", cellValue);
                                                        componentDataList.add(componentData);
                                                    }
                                                } catch (Exception e) {
                                                    // 记录错误信息
                                                    ImportErrorLogDO errorLog = new ImportErrorLogDO();
                                                    errorLog.setPointId(importConfigPointVO.getPointId());
                                                    errorLog.setPointCode(point.getPointCode());
                                                    errorLog.setTaskId(task.getId());
                                                    errorLog.setNodeId(reqVO.getNodeId());
                                                    errorLog.setProjectId(reqVO.getProjectId());
                                                    errorLog.setExecuteTime(LocalDateTime.now());
                                                    errorLog.setFileName(reqVO.getFileName());
                                                    errorLog.setSheetName(sheetName);
                                                    if (e instanceof NullPointerException || e instanceof ClassCastException || e.getMessage().contains("could not be parsed")) {
                                                        errorLog.setErrorRemark("数据类型与导入配置不匹配");
                                                    } else {
                                                        errorLog.setErrorRemark(e.getMessage());
                                                    }
                                                    // 记录错误位置(行号从1开始)并添加列信息
                                                    Integer columnName = configModel.getThingColumn()==null ? 0 : configModel.getThingColumn();
                                                    if (columnName != null) {
                                                        errorLog.setErrorLine("行：" + (row.getRowNum() + 1) + ", 列：" + columnName);
                                                    } else {
                                                        errorLog.setErrorLine("行：" + (row.getRowNum() + 1));
                                                    }
                                                    errorLog.setCreateTime(LocalDateTime.now());
                                                    importErrorLogMapper.insert(errorLog);
                                                    // 失败时增加计数
                                                    totalNumber=totalNumber+1;
//                                                    task.setTotalNumber(task.getTotalNumber() + 1);
//                                                    importTaskMapper.updateById(task);
                                                    isEmptyRow = false;
//                                                    break; // 继续处理下一行
                                                }
                                            }
                                            if (!isEmptyRow) {
                                                continue; // 跳过
                                            }
                                            if(!b){
                                                if (!(executeTime.isAfter(finalStartTime.atStartOfDay().minusNanos(1)) &&
                                                        executeTime.isBefore(finalEndTime.plusDays(1).atStartOfDay()))) {
                                                    continue;
                                                }
                                            } else {
                                                if (!Objects.isNull(lastPointDataJson)) {
                                                    if (Objects.nonNull(lastPointDataJson.getPointTime()) && !executeTime.isAfter(lastPointDataJson.getPointTime())) {
                                                        continue;
                                                    }
                                                }
                                            }
                                            if (componentDataList.isEmpty()) {
                                                continue;
                                            }
//                                比较判定状态
                                            if (pointEvaluateDOS.size() > 0) {
                                                // 初始化数据状态为正常
                                                int finalStatus = DataStatusEnum.NORMAL.getType();

                                                // 遍历每个分量数据
                                                for (Map<String, Object> componentData : componentDataList) {
                                                    BigDecimal value = new BigDecimal(componentData.get("thingValue").toString());
                                                    Long instrumentModelId = Long.valueOf(componentData.get("instrumentModelId").toString());

                                                    // 查找对应的评价指标
                                                    PointEvaluatePageRespVO evaluate = pointEvaluateDOS.stream()
                                                            .filter(e -> e.getInstrumentModelId().equals(instrumentModelId))
                                                            .findFirst()
                                                            .orElse(null);

                                                    if (evaluate != null) {
                                                        // 单个分量数据状态判定
                                                        int componentStatus = DataStatusEnum.NORMAL.getType();

                                                        if (value.compareTo(new BigDecimal(evaluate.getAbnormalUp())) > 0 ||
                                                                value.compareTo(new BigDecimal(evaluate.getAbnormalDown())) < 0) {
                                                            componentStatus = DataStatusEnum.ERROR_DATA.getType();
                                                        } else if (value.compareTo(new BigDecimal(evaluate.getWaringUp())) > 0 ||
                                                                value.compareTo(new BigDecimal(evaluate.getWaringDown())) < 0) {
                                                            componentStatus = DataStatusEnum.ANOMALOUS.getType();
                                                        }

                                                        // 综合判定一组数据状态
                                                        if (componentStatus == DataStatusEnum.ERROR_DATA.getType()) {
                                                            finalStatus = DataStatusEnum.ERROR_DATA.getType();
                                                        } else if (componentStatus == DataStatusEnum.ANOMALOUS.getType() &&
                                                                finalStatus != DataStatusEnum.ERROR_DATA.getType()) {
                                                            finalStatus = DataStatusEnum.ANOMALOUS.getType();
                                                        }
                                                    }
                                                }
                                                status = finalStatus;
                                            }
                                            // 保存数据
//                                    InstrumentDO instrument = instrumentService.getInstrument(point.getInstrumentId());
                                            PointDataJsonSaveReqVO dataJsonSaveReqVO = new PointDataJsonSaveReqVO();
                                            dataJsonSaveReqVO.setProjectId(importVO.getProjectId());
                                            dataJsonSaveReqVO.setPointId(point.getId());
                                            dataJsonSaveReqVO.setPointTime(executeTime);
                                            dataJsonSaveReqVO.setPointData(JSONObject.toJSONString(componentDataList));
                                            dataJsonSaveReqVO.setInstrumentId(point.getInstrumentId());
                                            dataJsonSaveReqVO.setDataType(model.getIsAutomation() == 0 ? 2 : 1);
                                            dataJsonSaveReqVO.setImportId(reqVO.getId());
                                            dataJsonSaveReqVO.setUserId(String.valueOf(loginUserId));
                                            dataJsonSaveReqVO.setDataStatus(status);
                                            // 插入
                                            PointDataJsonDO pointDataJson = BeanUtils.toBean(dataJsonSaveReqVO, PointDataJsonDO.class);
//                                Long loginUserId = WebFrameworkUtils.getLoginUserId();
                                            pointDataJson.setReviewer(loginUserId.toString());

                                            pointDataJson.setReviewer(String.valueOf(loginUserId));
                                            pointDataJson.setReviewName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
                                            pointDataJsonService.insertPointDataUpload(dataJsonSaveReqVO);
                                            // 成功时增加计数
                                            uploadNumber=uploadNumber+1;
                                            totalNumber=totalNumber+1;
                                        } catch (Exception e) {
                                            // 记录错误信息
                                            ImportErrorLogDO errorLog = new ImportErrorLogDO();
                                            errorLog.setPointId(importConfigPointVO.getPointId());
                                            errorLog.setTaskId(task.getId());
                                            errorLog.setNodeId(reqVO.getNodeId());
                                            errorLog.setProjectId(reqVO.getProjectId());
                                            errorLog.setExecuteTime(LocalDateTime.now());
                                            errorLog.setFileName(reqVO.getFileName());
                                            errorLog.setSheetName(sheetName);
                                            // 记录错误位置(行号从1开始)
                                            String errorPosition = "行:" + (row.getRowNum() + 1);
                                            errorLog.setErrorLine(errorPosition);
                                            errorLog.setErrorRemark(e.getMessage());
                                            errorLog.setCreateTime(LocalDateTime.now());
                                            importErrorLogMapper.insert(errorLog);
                                            // 失败时增加计数
                                            totalNumber=totalNumber+1;
//                                            task.setTotalNumber(task.getTotalNumber() + 1);
//                                            importTaskMapper.updateById(task);
//                                            continue; // 继续处理下一行
                                        }
                                    }
                                    task.setUploadNumber(task.getUploadNumber() + Long.valueOf(uploadNumber));
                                    task.setTotalNumber(task.getTotalNumber() + Long.valueOf(totalNumber));
                                    importTaskMapper.updateById(task);
                                    if(pointNum.get()==1){
                                        if (task.getUploadNumber().equals( task.getTotalNumber())) {
                                            task.setTaskStatus(STATUS_SUCCESS);
                                        } else if (task.getUploadNumber() < task.getTotalNumber()&&task.getUploadNumber()>0 ) {
                                            task.setTaskStatus(STATUS_PARTIAL);
                                        }else{
                                            task.setTaskStatus(STATUS_FAILED);
                                        }
                                        importTaskMapper.updateById(task);
                                        if (lock != null && lock.isHeldByCurrentThread()) {
                                            lock.unlock();
                                        }
                                    }else{
                                        pointNum.set(pointNum.get() - 1);
                                    }
                                });
                            });
                        }
                    }
                if(pointNum.get() ==0){
                            task.setUploadNumber(0L);
                            task.setTotalNumber(0L);
                            task.setTaskStatus(STATUS_SUCCESS);
                            importTaskMapper.updateById(task);
                }
                // 在导入循环结束后更新任务状态
//                if (task.getTotalNumber().equals(0L)) {
//                    task.setTaskStatus(STATUS_SUCCESS);
//                } else if (task.getUploadNumber().equals( task.getTotalNumber())) {
//                    task.setTaskStatus(STATUS_SUCCESS);
//                } else if (task.getUploadNumber() > 0) {
//                    task.setTaskStatus(STATUS_PARTIAL);
//                } else {
//                    task.setTaskStatus(STATUS_FAILED);
//                }
//                importTaskMapper.updateById(task);
            } catch (Exception e) {
                task.setTaskStatus(STATUS_FAILED);
                importTaskMapper.updateById(task);
                log.error("异步导入文件异常", e);
            } finally {
//                // 更新任务状态 - 确保最终状态正确
//                if (task.getTotalNumber().equals(0L)) {
//                    task.setTaskStatus(STATUS_SUCCESS);
//                } else if (task.getUploadNumber().equals( task.getTotalNumber())) {
//                    task.setTaskStatus(STATUS_SUCCESS);
//                } else if (task.getUploadNumber() > 0) {
//                    task.setTaskStatus(STATUS_PARTIAL);
//                } else {
//                    task.setTaskStatus(STATUS_FAILED);
//                }
//                importTaskMapper.updateById(task);
            }
        });
    }

    private List<ImportConfigPointVO> getWorkList(Long id) {
        List<ImportConfigPointDO> importConfigPointDOS = importConfigPointMapper.selectList(new LambdaQueryWrapperX<ImportConfigPointDO>()
                .eq(ImportConfigPointDO::getSheetConfigId, id)
        );
        List<ImportConfigPointVO> importConfigPointVOS = new ArrayList<>();
        for (ImportConfigPointDO importConfigPointDO : importConfigPointDOS) {
            PointDO pointDO = pointMapper.selectById(importConfigPointDO.getPointId());
            ImportConfigPointVO importConfigPointVO = BeanUtils.toBean(importConfigPointDO, ImportConfigPointVO.class);
            importConfigPointVO.setPointName(pointDO.getPointName());
            importConfigPointVO.setPointCode(pointDO.getPointCode());
            importConfigPointVOS.add(importConfigPointVO);
        };
        return importConfigPointVOS;
    }

    // 判断行是否为空的工具方法
    private boolean isRowEmpty(Row row) {
        if (row == null) return true;
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }
        // 辅助方法：获取单元格值
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    @Override
    public void moveFileExcel(ImportFileMoveVO importFileMoveVOs) {
        for (ImportFileSaveReqVO updateReqVO : importFileMoveVOs.getImportFileSaveReqVO()) {
            // 校验存在
            validateImportFileExists(updateReqVO.getId());
            // 更新
            ImportFileDO updateObj = BeanUtils.toBean(updateReqVO, ImportFileDO.class);
            importFileMapper.updateById(updateObj);
            // 更新导入任务
            List<ImportTaskDO> importTaskDOS = importTaskMapper.selectList(new LambdaQueryWrapper<ImportTaskDO>()
                    .eq(ImportTaskDO::getFileName, updateReqVO.getFileName()));
            if(importTaskDOS.size()>0){
                for (ImportTaskDO importTaskDO : importTaskDOS) {
                    importTaskDO.setNodeId(updateReqVO.getNodeId());
                    importTaskMapper.updateById(importTaskDO);
                    //更新错误日志
                    List<ImportErrorLogDO> importErrorLogDOS = importErrorLogMapper.selectList(new LambdaQueryWrapper<ImportErrorLogDO>()
                           .eq(ImportErrorLogDO::getTaskId, importTaskDO.getId()));
                    if(importErrorLogDOS.size()>0){
                        for (ImportErrorLogDO importErrorLogDO : importErrorLogDOS) {
                            importErrorLogDO.setNodeId(updateReqVO.getNodeId());
                            importErrorLogMapper.updateById(importErrorLogDO);
                        }
                    }
                }
            }
        }
    }


    /**
     * 获取指定节点下的所有子节点（递归）
     *
     * @param projectId 项目ID
     * @param parentId  父节点ID
     * @return 子节点列表
     */
    private List<ImportNodeDO> getAllChildNodes(Long projectId, Long parentId) {
        List<ImportNodeDO> result = new ArrayList<>();
        // 查询直接子节点
        List<ImportNodeDO> directChildren = importNodeMapper.selectList(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getProjectId, projectId)
                .eq(ImportNodeDO::getParentId, parentId));
        if (directChildren != null && !directChildren.isEmpty()) {
            result.addAll(directChildren);
            for (ImportNodeDO child : directChildren) {
                // 递归查询子节点的子节点
                result.addAll(getAllChildNodes(projectId, child.getId()));
            }
        }
        return result;
    }
}