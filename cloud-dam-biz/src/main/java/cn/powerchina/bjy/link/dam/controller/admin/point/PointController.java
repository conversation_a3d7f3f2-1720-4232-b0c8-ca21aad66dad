package cn.powerchina.bjy.link.dam.controller.admin.point;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点信息")
@RestController
@RequestMapping("/dam/point")
@Validated
public class PointController {

    @Resource
    private PointService pointService;

    @PostMapping("/create")
    @Operation(summary = "创建测点信息")
//    @PreAuthorize("@ss.hasPermission('dam:point:create')")
    public CommonResult<Long> createPoint(@Valid @RequestBody PointSaveReqVO createReqVO) {
        return success(pointService.createPoint(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新测点信息")
//    @PreAuthorize("@ss.hasPermission('dam:point:update')")
    public CommonResult<Boolean> updatePoint(@Valid @RequestBody PointSaveReqVO updateReqVO) {
        pointService.updatePoint(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测点信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point:delete')")
    public CommonResult<Boolean> deletePoint(@RequestParam("id") Long id) {
        pointService.deletePoint(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测点信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    public CommonResult<PointRespVO> getPoint(@RequestParam("id") Long id) {
        PointBO pointBO = pointService.getPointBO(id);
        return success(BeanUtils.toBean(pointBO, PointRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点信息分页")
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    public CommonResult<PageResult<PointRespVO>> getPointPage(@Valid PointPageReqVO pageReqVO) {
        PageResult<PointBO> pageResult = pointService.getPointBOPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得测点信息列表")
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    @Parameter(name = "categoryId", description = "测点工程目录结构", required = true)
    public CommonResult<List<PointSimpleRespVO>> getPointList(@RequestParam("categoryId") Long categoryId) {
        List<PointDO> pointDOList = pointService.getPointDOListByCategoryId(categoryId);
        return success(BeanUtils.toBean(pointDOList, PointSimpleRespVO.class));
    }

    @GetMapping("/list/select")
    @Operation(summary = "获得测点信息下拉列表")
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    @Parameter(name = "projectId", description = "项目id", required = true)
    @Parameter(name = "pointId", description = "需要过滤的测点id", required = false)
    public CommonResult<List<PointSimpleRespVO>> getPointListSelect(@RequestParam(value = "projectId") Long projectId,
                                                                    @RequestParam(value = "pointId", required = false) Long pointId) {
        List<PointDO> pointDOList = pointService.getPointDOListByProjectId(projectId, pointId);
        return success(BeanUtils.toBean(pointDOList, PointSimpleRespVO.class));
    }

    @PostMapping("/group/point/save")
    @Operation(summary = "保存分组测点信息")
//    @PreAuthorize("@ss.hasPermission('dam:point:create')")
    public CommonResult<Boolean> saveGroupPoint(@Valid @RequestBody PointGroupSaveReqVO reqVO) {
        pointService.saveGroupPoint(reqVO);
        return success(true);
    }

    @GetMapping("/page/remove")
    @Operation(summary = "获得过滤后的测点信息")
//    @PreAuthorize("@ss.hasPermission('dam:point:query')")
    public CommonResult<PageResult<PointRespVO>> getPointRemoveBOPage(@Valid PointPageReqVO pageReqVO,@RequestParam Long pointId) {
        PageResult<PointBO> pageResult = pointService.getPointRemoveBOPage(pageReqVO,pointId);
        return success(BeanUtils.toBean(pageResult, PointRespVO.class));
    }

}