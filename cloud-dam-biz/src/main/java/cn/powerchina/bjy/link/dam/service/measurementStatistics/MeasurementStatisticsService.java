package cn.powerchina.bjy.link.dam.service.measurementStatistics;


import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataStatisticsVO;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

public interface MeasurementStatisticsService {
    /**
     * 测点数据统计
     * @param statisticsVO
     * @return
     */
    List<PointDataBO> pointStatistics(PointDataStatisticsVO statisticsVO);

    /**
     * 测点数据导出
     *
     * @param statisticsVO 导出参数
     * @return
     */
    byte[] exportExcel(PointDataStatisticsVO statisticsVO);
}
