package cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparamtemplate;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 仪器类型模板-计算参数 DO
 *
 * <AUTHOR>
 */
@TableName("dam_instrument_param_template")
@KeySequence("dam_instrument_param_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstrumentParamTemplateDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 仪器类型模板id
     */
    private Long instrumentId;
    /**
     * 参数名称
     */
    private String thingName;
    /**
     * 参数标识符
     */
    private String thingIdentity;
    /**
     * 单位
     */
    private String thingUnit;
    /**
     * 小数位
     */
    private Integer decimalLimit;
    /**
     * 权重，数字越小越靠前
     */
    private Integer thingWeight;

}