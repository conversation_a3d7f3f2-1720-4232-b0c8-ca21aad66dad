package cn.powerchina.bjy.link.dam.controller.admin.instrument;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplateSaveListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.TreeVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 仪器类型")
@RestController
@RequestMapping("/dam/instrument")
@Validated
public class InstrumentController {

    @Resource
    private InstrumentService instrumentService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器类型")
    //@PreAuthorize("@ss.hasPermission('dam:instrument:create')")
    public CommonResult<Long> createInstrument(@Valid @RequestBody InstrumentSaveReqVO createReqVO) {
        return success(instrumentService.createInstrument(createReqVO));
    }

    @PostMapping("/insert-list")
    @Operation(summary = "创建仪器类型")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-template:create')")
    public CommonResult<Long> insertListInstrument(@Valid @RequestBody InstrumentSaveListReqVO createReqVO) {
        return success(instrumentService.insertListInstrument(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器类型")
   // @PreAuthorize("@ss.hasPermission('dam:instrument:update')")
    public CommonResult<Boolean> updateInstrument(@Valid @RequestBody InstrumentSaveReqVO updateReqVO) {
        instrumentService.updateInstrument(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器类型")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('dam:instrument:delete')")
    public CommonResult<Boolean> deleteInstrument(@RequestParam("id") Long id) {
        instrumentService.deleteInstrument(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('dam:instrument:query')")
    public CommonResult<InstrumentRespVO> getInstrument(@RequestParam("id") Long id) {
        InstrumentDO instrument = instrumentService.getInstrument(id);
        return success(BeanUtils.toBean(instrument, InstrumentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器类型分页")
    //@PreAuthorize("@ss.hasPermission('dam:instrument:query')")
    public CommonResult<PageResult<InstrumentRespVO>> getInstrumentPage(@Valid InstrumentPageReqVO pageReqVO) {
        PageResult<InstrumentDO> pageResult = instrumentService.getInstrumentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentRespVO.class));
    }

    @GetMapping("/check/point")
    @Operation(summary = "查看仪器类型下测点：有测点true，无测点：false")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('dam:instrument:query')")
    public CommonResult<Boolean> checkPoint(@RequestParam("id") Long id) {
        return success(instrumentService.checkPoint(id));
    }

    @GetMapping("/get-list-tree")
    @Operation(summary = "获得仪器类型模板列表树")
    @Parameter(name = "projectId", description = "projectId", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('dam:instrument-template:query')")
    public CommonResult<List<TreeVO>> getListTree(@RequestParam("projectId") Long projectId) {
        List<TreeVO> res = instrumentService.getListTree(projectId);
        return success(res);
    }


    @GetMapping("/get-model-param")
    @Operation(summary = "获得仪器类型-总列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('dam:instrument:query')")
    public CommonResult<InstrumenListReqVO> getInstrumentAndModelAndParam(@RequestParam("id") Long id,@RequestParam("projectId") Long projectId) {
        InstrumenListReqVO instrument = instrumentService.getInstrumentAndModelAndParam(id,projectId);
        return success(instrument);
    }
}