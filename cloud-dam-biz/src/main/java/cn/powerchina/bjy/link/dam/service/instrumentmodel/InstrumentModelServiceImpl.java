package cn.powerchina.bjy.link.dam.service.instrumentmodel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.bo.SelectBO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointInformationVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamTableRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproductmodel.IotProductModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.enums.PointParamsTableEnum;
import cn.powerchina.bjy.link.dam.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.instrumentparam.InstrumentParamService;
import cn.powerchina.bjy.link.dam.service.iotproductmodel.IotProductModelService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import cn.powerchina.bjy.link.iot.api.product.ProductApi;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductModelRespDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 仪器类型-测量分量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentModelServiceImpl implements InstrumentModelService {

    @Resource
    private InstrumentModelMapper instrumentModelMapper;

    @Resource
    private ProductApi productApi;

    @Resource
    private InstrumentService instrumentService;

    @Resource
    private PointService pointService;

    @Resource
    @Lazy
    private PointDataJsonService pointDataJsonService;

    @Resource
    @Lazy
    private InstrumentParamService instrumentParamService;

    @Autowired
    private AuthProductService authProductService;

    @Autowired
    private IotProductModelService iotProductModelService;


    @Override
    public Long createInstrumentModel(InstrumentModelSaveReqVO createReqVO) {
        validateSaveReq(createReqVO);
        // 插入
        InstrumentModelDO instrumentModel = BeanUtils.toBean(createReqVO, InstrumentModelDO.class);
        instrumentModelMapper.insert(instrumentModel);
        // 返回
        return instrumentModel.getId();
    }

    @Override
    public void updateInstrumentModel(InstrumentModelSaveReqVO updateReqVO) {
        // 校验存在
        validateInstrumentModelExists(updateReqVO.getId());
        //修改公式，不需要验证分量标识是否相同
        if (Objects.nonNull(updateReqVO.getThingIdentity())) {
            validateSaveReq(updateReqVO);
        }
        // 更新
        InstrumentModelDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentModelDO.class);
        instrumentModelMapper.updateById(updateObj);
    }

    @Override
    public List<InstrumentModelDO> getListUsingByFormula(Long instrumentId, String thingIdentity) {
        return instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>().
                eq(InstrumentModelDO::getInstrumentId, instrumentId).like(InstrumentModelDO::getCalcFormula, thingIdentity));
    }

    @Override
    public List<PointParamTableRespVO> getPointModelTable(Long pointId, List<Integer> thingTypeList) {
        List<PointParamTableRespVO> modelTableList = getTable(pointId, thingTypeList);
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.DATA_TYPE.getLabelName(), PointParamsTableEnum.DATA_TYPE.getFieldName()));
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.DATA_STATUS.getLabelName(), PointParamsTableEnum.DATA_STATUS.getFieldName()));
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.REVIEW_STATUS.getLabelName(), PointParamsTableEnum.REVIEW_STATUS.getFieldName()));
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.REVIEW_NAME.getLabelName(), PointParamsTableEnum.REVIEW_NAME.getFieldName()));
        return modelTableList;
    }

    /**
     * 动态表头的共用部分
     *
     * @param pointId
     * @return
     */
    @NotNull
    private List<PointParamTableRespVO> getTable(Long pointId, List<Integer> thingTypeList) {
        List<InstrumentModelDO> instrumentParamDOList = getInstrumentModelList(pointId);
        List<PointParamTableRespVO> modelTableList = new ArrayList<>();
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.POINT_CODE.getLabelName(), PointParamsTableEnum.POINT_CODE.getFieldName()));
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.POINT_TIME.getLabelName(), PointParamsTableEnum.POINT_TIME.getFieldName()));

        InstrumentModelServiceImpl.this.getModelTable(thingTypeList, instrumentParamDOList, modelTableList);
        return modelTableList;
    }

    public List<PointParamTableRespVO> getModelTable(Long pointId, List<Integer> thingTypeList) {
        List<InstrumentModelDO> instrumentParamDOList = getInstrumentModelList(pointId);
        List<PointParamTableRespVO> modelTableList = new ArrayList<>();
        getModelTable(thingTypeList, instrumentParamDOList, modelTableList);
        return modelTableList;
    }

    @Override
    public List<InstrumentModelDO> listByIdList(List<Long> idList) {
        List<InstrumentModelDO> instrumentModelDOList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(idList)) {
            instrumentModelDOList = instrumentModelMapper.selectList(
                    new LambdaQueryWrapperX<InstrumentModelDO>().in(InstrumentModelDO::getId, idList));
        }
        return instrumentModelDOList;
    }

    private void getModelTable(List<Integer> thingTypeList, List<InstrumentModelDO> instrumentParamDOList, List<PointParamTableRespVO> modelTableList) {
        if (!CollectionUtils.isEmpty(instrumentParamDOList)) {
            if (CollectionUtil.isEmpty(thingTypeList)) {
                instrumentParamDOList.forEach(item -> {
                    String labelName = item.getThingName();
                    if (!StringUtils.isEmpty(item.getThingUnit())) {
                        labelName = MessageFormat.format("{0}({1})", item.getThingName(), item.getThingUnit());
                    }
                    modelTableList.add(new PointParamTableRespVO(labelName, item.getThingIdentity()));

                });
            } else {
                instrumentParamDOList.forEach(item -> {
                    if (thingTypeList.contains(item.getThingType())) {
                        String labelName = item.getThingName();
                        if (!StringUtils.isEmpty(item.getThingUnit())) {
                            labelName = MessageFormat.format("{0}({1})", item.getThingName(), item.getThingUnit());
                        }
                        modelTableList.add(new PointParamTableRespVO(labelName, item.getThingIdentity()));
                    }

                });
            }
        }
    }


    @Override
    public List<PointParamTableRespVO> getManualPointTable(Long pointId) {
        List<PointParamTableRespVO> modelTableList = getTable(pointId, null);
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.CREATOR.getLabelName(), PointParamsTableEnum.CREATOR.getFieldName()));
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.DATA_STATUS.getLabelName(), PointParamsTableEnum.DATA_STATUS.getFieldName()));
        modelTableList.add(new PointParamTableRespVO(PointParamsTableEnum.CREATE_TIME.getLabelName(), PointParamsTableEnum.CREATE_TIME.getFieldName()));
        return modelTableList;
    }

    @Override
    public List<LocalDateTime> getCurrentTime(Long pointId) {
        List<LocalDateTime> list=new ArrayList<>();
        PointBO pointBO = pointService.getPointBO(pointId);
        //默认查询最新数据当前年份所有数据
        PointDataJsonDO lastPointDataJson1 = pointDataJsonService.getLastPointDataJson(pointBO.getProjectId(),pointBO.getInstrumentId(),pointId,1);
        PointDataJsonDO lastPointDataJson2 = pointDataJsonService.getLastPointDataJson(pointBO.getProjectId(),pointBO.getInstrumentId(),pointId,2);
        if (lastPointDataJson1 != null||lastPointDataJson2 != null) {
            LocalDateTime startTime=LocalDateTime.now();
            LocalDateTime endTime=LocalDateTime.now();
            if(lastPointDataJson1 != null&&lastPointDataJson2 == null){
                startTime= lastPointDataJson1.getPointTime().withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                endTime=lastPointDataJson1.getPointTime();
            }else if(lastPointDataJson1 == null && lastPointDataJson2 != null){
                startTime= lastPointDataJson2.getPointTime().withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                endTime= lastPointDataJson2.getPointTime();
            }else{
                if(lastPointDataJson1.getPointTime().isAfter(lastPointDataJson2.getPointTime())){
                    startTime = lastPointDataJson1.getPointTime().withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = lastPointDataJson1.getPointTime();
                }else{
                    startTime = lastPointDataJson2.getPointTime().withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = lastPointDataJson2.getPointTime();
                }
            }
            list.add(startTime);
            list.add(endTime);
        }
        return list;
    }

    /**
     * 验证表单
     *
     * @param updateReqVO
     */
    private void validateSaveReq(InstrumentModelSaveReqVO updateReqVO) {
        validateNameExists(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingName());
        instrumentParamService.validateNameExists(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingName());
        validateThingIdentityExists(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingIdentity());
        //验证参数是否有相同标识符
        instrumentParamService.validateThingIdentityExists(null, updateReqVO.getInstrumentId(), updateReqVO.getThingIdentity());
        validateThingIdentityIotBind(updateReqVO.getId(), updateReqVO.getInstrumentId(), updateReqVO.getThingIdentityIot());
    }

    @Override
    public void deleteInstrumentModel(Long id) {
        // 校验存在
        InstrumentModelDO instrumentModelDO = validateInstrumentModelExists(id);
        List<InstrumentModelDO> modelList = getListUsingByFormula(instrumentModelDO.getInstrumentId(), instrumentModelDO.getThingIdentity());
        if (!CollectionUtils.isEmpty(modelList)) {
            throw exception(INSTRUMENT_MODEL_USING);
        }
        //校验是否绑定测点
        Boolean bindPoint = instrumentService.checkPoint(instrumentModelDO.getInstrumentId());
        if (bindPoint) {
            throw exception(INSTRUMENT_USING_POINT);
        }
        // 删除
        instrumentModelMapper.deleteById(id);
    }

    @Override
    public InstrumentModelDO validateInstrumentModelExists(Long id) {
        InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectById(id);
        if (instrumentModelDO == null) {
            throw exception(INSTRUMENT_MODEL_NOT_EXISTS);
        }
        return instrumentModelDO;
    }

    @Override
    public InstrumentModelDO getInstrumentModel(Long id) {
        return instrumentModelMapper.selectById(id);
    }

    @Override
    public PageResult<InstrumentModelRespVO> getInstrumentModelPage(InstrumentModelPageReqVO pageReqVO) {
        PageResult<InstrumentModelDO> instrumentModelDOPageResult = instrumentModelMapper.selectPage(pageReqVO);
        //设置iot产品名称
        List<InstrumentModelRespVO> instrumentModelRespVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(instrumentModelDOPageResult.getList())) {
            AuthProductDO authProductDO = getAuthProductDO(pageReqVO);
            Map<String, String> thingIotMap = new HashMap<>();
            if (Objects.nonNull(authProductDO)) {
                CommonResult<List<ProductModelRespDTO>> productModelResult = productApi.getProductModelByProductCode(authProductDO.getProductCode(), ThingModeTypeEnum.PROPERTY.getType());
                if (!CollectionUtils.isEmpty(productModelResult.getData())) {
                    thingIotMap.putAll(productModelResult.getData().stream().collect(Collectors.toMap(ProductModelRespDTO::getThingIdentity, ProductModelRespDTO::getThingName)));
                }
            }
            instrumentModelDOPageResult.getList().forEach(item -> {
                InstrumentModelRespVO modelRespVO = new InstrumentModelRespVO();
                BeanUtil.copyProperties(item, modelRespVO);
                modelRespVO.setProductName(Objects.nonNull(authProductDO) ? authProductDO.getProductName() : null);
                modelRespVO.setThingNameIot(StringUtils.isNotBlank(item.getThingIdentityIot()) ? thingIotMap.get(item.getThingIdentityIot()) : null);
                instrumentModelRespVOList.add(modelRespVO);
            });
        }
        return new PageResult<>(instrumentModelRespVOList, instrumentModelDOPageResult.getTotal());
    }

    @Override
    public List<InstrumentModelRespVO> getInstrumentModelLists(InstrumentModelRespVO pageReqVO) {
        List<InstrumentModelDO> instrumentModelDOPageResult = instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>().eq(InstrumentModelDO::getInstrumentId, pageReqVO.getInstrumentId())
                .eq(InstrumentModelDO::getProjectId, pageReqVO.getProjectId())
                .orderByAsc(InstrumentModelDO::getThingWeight));
        //设置iot产品名称
        List<InstrumentModelRespVO> instrumentModelRespVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(instrumentModelDOPageResult)) {
            AuthProductDO authProductDO = getAuthProductDO(pageReqVO);
            Map<String, String> thingIotMap = new HashMap<>();
            if (Objects.nonNull(authProductDO)) {
                CommonResult<List<ProductModelRespDTO>> productModelResult = productApi.getProductModelByProductCode(authProductDO.getProductCode(), ThingModeTypeEnum.PROPERTY.getType());
                if (!CollectionUtils.isEmpty(productModelResult.getData())) {
                    thingIotMap.putAll(productModelResult.getData().stream().collect(Collectors.toMap(ProductModelRespDTO::getThingIdentity, ProductModelRespDTO::getThingName)));
                }
            }
            instrumentModelDOPageResult.forEach(item -> {
                InstrumentModelRespVO modelRespVO = new InstrumentModelRespVO();
                BeanUtil.copyProperties(item, modelRespVO);
                modelRespVO.setProductName(Objects.nonNull(authProductDO) ? authProductDO.getProductName() : null);
                modelRespVO.setThingNameIot(StringUtils.isNotBlank(item.getThingIdentityIot()) ? thingIotMap.get(item.getThingIdentityIot()) : null);
                instrumentModelRespVOList.add(modelRespVO);
            });
        }
        return instrumentModelRespVOList;
    }

    @Override
    public List<SelectBO> selectProductIdentityList(Long instrumentId) {
        InstrumentDO instrument = instrumentService.getInstrument(instrumentId);
        if (Objects.nonNull(instrument) && Objects.nonNull(instrument.getProductCode())) {
            List<IotProductModelDO> iotProductModelDOList = iotProductModelService.listByProductCodeThingType(instrument.getProductCode(), ThingModeTypeEnum.PROPERTY.getType());
            if (!CollectionUtils.isEmpty(iotProductModelDOList)) {
                return iotProductModelDOList.stream().map(item -> SelectBO.builder().name(item.getThingName())
                        .value(item.getThingIdentity()).build()).distinct().toList();
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<SelectBO> getProductCodeIdentity(String productCode) {
        if(!StringUtils.isEmpty(productCode))
        {
            List<IotProductModelDO> iotProductModelDOList = iotProductModelService.listByProductCodeThingType(productCode, ThingModeTypeEnum.PROPERTY.getType());
            if (!CollectionUtils.isEmpty(iotProductModelDOList)) {
                return iotProductModelDOList.stream().map(item -> SelectBO.builder().name(item.getThingName())
                        .value(item.getThingIdentity()).build()).distinct().toList();
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<InstrumentModelDO> getModelByThingType(Long instrumentId, List<Integer> typeList) {
        if (CollectionUtil.isEmpty(typeList)) {
            throw exception(INSTRUMENT_MODEL_THING_REQUIRED);
        }
        return instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>().eq(InstrumentModelDO::getInstrumentId, instrumentId).
                in(InstrumentModelDO::getThingType, typeList));
    }

    @Override
    public List<InstrumentModelDO> getModelByInstrumentId(Long instrumentId) {
        return instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>().eq(InstrumentModelDO::getInstrumentId, instrumentId)
                .orderByAsc(InstrumentModelDO::getThingType));
    }

    @Override
    public List<InstrumentModelDO> getInstrumentModelList(Long pointId) {
        PointDO point = pointService.validatePointExists(pointId);
        return getModelByInstrumentId(point.getInstrumentId());
    }

    @Override
    public InstrumentModelDO getModelByInstrumentIdAndThingIdentityIot(Long instrumentId, String thingIdentityIot) {
        return instrumentModelMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelDO>().
                eq(InstrumentModelDO::getThingIdentityIot, thingIdentityIot).eq(InstrumentModelDO::getInstrumentId, instrumentId));
    }

    /**
     * 分量名称是否存在
     *
     * @param id
     * @param name
     */
    @Override
    public void validateNameExists(Long id, Long instrumentId, String name) {
        InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelDO>().
                eq(InstrumentModelDO::getThingName, name).eq(InstrumentModelDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentModelDO.getId()))) {
            throw exception(ErrorCodeConstants.INSTRUMENT_MODEL_NAME_EXISTS);
        }
    }

    @Override
    public void updateListInstrumentModel(List<InstrumentModelSaveReqVO> updateReqVO) {
        for (InstrumentModelSaveReqVO reqVO : updateReqVO) {
            if (null != reqVO.getId() && "".equals(reqVO.getId())) {
                validateSaveReq(reqVO);
            }
        }
        for (InstrumentModelSaveReqVO reqVO : updateReqVO) {
            if (null != reqVO.getId() && "".equals(reqVO.getId())) {
                updateInstrumentModel(reqVO);
            } else {
                createInstrumentModel(reqVO);
            }
        }
    }

    /**
     * 标识符是否存在
     *
     * @param id
     * @param instrumentId
     * @param thingIdentity
     */
    @Override
    public void validateThingIdentityExists(Long id, Long instrumentId, String thingIdentity) {
        InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelDO>().
                eq(InstrumentModelDO::getThingIdentity, thingIdentity).eq(InstrumentModelDO::getInstrumentId, instrumentId));
        if (Objects.nonNull(instrumentModelDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentModelDO.getId()))) {
            throw exception(ErrorCodeConstants.INSTRUMENT_MODEL_THING_IDENTITY_EXISTS);
        }
    }

    /**
     * iot标识符是否已经绑定
     *
     * @param id
     * @param instrumentId
     * @param thingIdentity
     */
    private void validateThingIdentityIotBind(Long id, Long instrumentId, String thingIdentity) {
        if (StringUtils.isNotEmpty(thingIdentity)) {
            InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectOne(new LambdaQueryWrapperX<InstrumentModelDO>().
                    eq(InstrumentModelDO::getThingIdentityIot, thingIdentity).eq(InstrumentModelDO::getInstrumentId, instrumentId));
            if (Objects.nonNull(instrumentModelDO) && (Objects.isNull(id) || !Objects.equals(id, instrumentModelDO.getId()))) {
                throw exception(ErrorCodeConstants.INSTRUMENT_MODEL_THING_IDENTITY_IOT_BIND);
            }
        }
    }

    /**
     * 查询仪器类型绑定的产品名称
     *
     * @param pageReqVO
     * @return
     */
    @Nullable
    private AuthProductDO getAuthProductDO(InstrumentModelPageReqVO pageReqVO) {
        InstrumentDO instrument = instrumentService.getInstrument(pageReqVO.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
        return Objects.nonNull(instrument.getProductCode()) ?
                authProductService.findProductByProjectIdProductCode(instrument.getProjectId(), instrument.getProductCode()) : null;
    }

    /**
     * 查询仪器类型绑定的产品名称
     *
     * @param pageReqVO
     * @return
     */
    @Nullable
    private AuthProductDO getAuthProductDO(InstrumentModelRespVO pageReqVO) {
        InstrumentDO instrument = instrumentService.getInstrument(pageReqVO.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
        return Objects.nonNull(instrument.getProductCode()) ?
                authProductService.findProductByProjectIdProductCode(instrument.getProjectId(), instrument.getProductCode()) : null;
    }

}