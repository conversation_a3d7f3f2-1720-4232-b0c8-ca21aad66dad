package cn.powerchina.bjy.link.dam.dal.mysql.pointparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamPageVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointparam.PointParamDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测点计算参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointParamMapper extends BaseMapperX<PointParamDO> {

    default PageResult<PointParamPageVO> selectPage(PointParamPageReqVO reqVO) {
        MPJLambdaWrapperX<PointParamDO> wrapper = (MPJLambdaWrapperX<PointParamDO>) new MPJLambdaWrapperX<PointParamDO>()
                .selectAll(PointParamDO.class)
                .selectAs(PointDO::getPointCode, PointParamPageVO::getPointCode)
                .selectAs(PointDO::getPointState, PointParamPageVO::getPointState)
                .leftJoin(PointDO.class, "p", on -> on.eq(PointDO::getId, PointParamDO::getPointId))
                .eqIfExists(PointParamDO::getProjectId, reqVO.getProjectId())
                .in(PointParamDO::getPointId, reqVO.getPointIdList())
                .eqIfExists(PointParamDO::getApplyType, reqVO.getApplyType())
                .eqIfExists(PointDO::getPointState, reqVO.getPointState())
                .likeIfExists(PointDO::getPointCode, reqVO.getPointCode())
                .orderByDesc(PointParamDO::getCreateTime);
        return selectJoinPage(reqVO, PointParamPageVO.class, wrapper);
    }

}