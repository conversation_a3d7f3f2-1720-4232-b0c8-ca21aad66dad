package cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测点数据json DO
 *
 * <AUTHOR>
 */
@TableName("dam_point_data_json")
@KeySequence("dam_point_data_json_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointDataJsonDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 监测时间
     */
    private LocalDateTime pointTime;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 分量值json，包括分量id、分量标识符、分量名称、分量原始值、分量值
     */
    private String pointData;
    /**
     * 采集类型(1：自动化采集，2：人工录入）
     */
    private Integer dataType;
    /**
     * 数据状态(1：正常数据，0：异常数据）
     */
    private Integer dataStatus;
    /**
     * 审核状态
     */
    private Integer reviewStatus;
    /**
     * 审核人
     */
    private String reviewer;
    private String reviewName;
    /**
     * 审核意见
     */
    private String reviewOpinion;
    /**
     * 回滚测点数据导入id
     */
    private Long rollbackImportId;

}