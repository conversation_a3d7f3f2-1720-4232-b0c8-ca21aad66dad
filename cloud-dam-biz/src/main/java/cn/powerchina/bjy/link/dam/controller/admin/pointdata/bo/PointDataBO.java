package cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/23
 */
@Data
public class PointDataBO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "测点编码")
    private String pointCode;

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "分量单位")
    private String thingUnit;

    @Schema(description = "最小值")
    private BigDecimal valueMin;

    @Schema(description = "最小值监测时间")
    private LocalDateTime valueMinPointTime;

    @Schema(description = "最大值")
    private BigDecimal valueMax;

    @Schema(description = "最大值监测时间")
    private LocalDateTime valueMaxPointTime;

    @Schema(description = "首值")
    private BigDecimal valueFirst;

    @Schema(description = "首值监测时间")
    private LocalDateTime valueFirstPointTime;

    @Schema(description = "尾值")
    private BigDecimal valueLast;

    @Schema(description = "尾值监测时间")
    private LocalDateTime valueLastPointTime;

    @Schema(description = "平均值")
    private BigDecimal valueAverage;

    @Schema(description = "变幅")
    private BigDecimal valueRange;

    @Schema(description = "历史最大值")
    private BigDecimal valueMaxHistory;

    @Schema(description = "历史最小值")
    private BigDecimal valueMinHistory;

    @Schema(description = "测次")
    private Long countNum;

    @Schema(description = "年份/月份")
    private String yearAndMonth;

    @Schema(description = "历史最小大时间")
    private LocalDateTime valueMinHistoryPointTime;

    @Schema(description = "历史最大时间")
    private LocalDateTime valueMaxHistoryPointTime;

//    public BigDecimal getValueRange() {
//        if (this.valueMaxHistory == null || this.valueMinHistory == null) {
//            return null;
//        }
//
//        return BigDecimalUtils.calculateValueSub(valueMaxHistory, valueMinHistory, 2);
//    }
}
