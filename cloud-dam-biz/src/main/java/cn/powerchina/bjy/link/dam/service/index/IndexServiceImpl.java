package cn.powerchina.bjy.link.dam.service.index;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexHeadBO;
import cn.powerchina.bjy.link.dam.controller.admin.index.bo.IndexPointDeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.bo.PointDataInstrumentBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.enums.DataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.NodeTypeEnum;
import cn.powerchina.bjy.link.dam.enums.PointStateEnum;
import cn.powerchina.bjy.link.dam.enums.PointTypeEnum;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.indexhead.IndexHeadService;
import cn.powerchina.bjy.link.dam.service.indexmiddlebottom.IndexMiddleBottomService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.dam.util.MyCalculateUtils;
import cn.powerchina.bjy.link.dam.util.MyDateUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 首页信息
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Service
@Slf4j
public class IndexServiceImpl implements IndexService {

    @Autowired
    private PointService pointService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private PointDataJsonService pointDataJsonService;

    @Autowired
    private InstrumentService instrumentService;

    @Resource
    private ThreadPoolTaskExecutor damThreadPoolTaskExecutor;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private IndexHeadService indexHeadService;

    @Autowired
    private IndexMiddleBottomService indexMiddleBottomService;

    @Override
    public IndexBO findIndexInfo(Long projectId) {
        IndexBO indexBO = new IndexBO();
        List<CompletableFuture<Void>> result = new ArrayList<>();
        result.add(CompletableFuture.runAsync(() -> {
            Date currentDate = MyDateUtils.getParseDate(MyDateUtils.getFormatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
            //设置监测仪器数量
            indexBO.setPointDeviceTotal(pointService.countPointByProjectId(projectId, null, null, null, null));
            indexBO.setPointDeviceToday(pointService.countPointByProjectId(projectId, null, null, null, currentDate));
            //设置监测数据数量
            indexBO.setPointDataTotal(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, null, null));
            indexBO.setPointDataToday(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, null, currentDate));
            //设置自动化设备数量
            indexBO.setDeviceMcuTotal(deviceService.countDeviceByProjectIdAndNodeType(projectId, NodeTypeEnum.EDGE.getType(), null));
            indexBO.setDeviceMcuToday(deviceService.countDeviceByProjectIdAndNodeType(projectId, NodeTypeEnum.EDGE.getType(), currentDate));
            //设置在测自动化仪器数量
            indexBO.setDeviceMcuRunTotal(pointService.countPointByProjectId(projectId, null, PointStateEnum.NORMAL.getType(), Arrays.asList(PointTypeEnum.ALL.getType(), PointTypeEnum.AUTO.getType()), null));
            indexBO.setDeviceMcuRunToday(pointService.countPointByProjectId(projectId, null, PointStateEnum.NORMAL.getType(), Arrays.asList(PointTypeEnum.ALL.getType(), PointTypeEnum.AUTO.getType()), currentDate));
            //设置自动化监测数据数量
            indexBO.setPointDataMcuTotal(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, DataTypeEnum.AUTO_COLLECTION.getType(), null));
            indexBO.setPointDataMcuToday(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, DataTypeEnum.AUTO_COLLECTION.getType(), currentDate));
        }, damThreadPoolTaskExecutor));
        //查找仪器类型
        result.add(CompletableFuture.runAsync(() -> {
            List<InstrumentDO> instrumentDOList = instrumentService.getInstrumentList(projectId);
            List<IndexBO.PointDeviceInfo> deviceInfoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(instrumentDOList)) {
                List<Long> instrumentIdList = instrumentDOList.stream().map(InstrumentDO::getId).toList();
                List<PointDataInstrumentBO> instrumentBOList = pointDataJsonService.findPointDataInstrumentBOList(projectId, instrumentIdList);

                Map<Long, PointDataInstrumentBO> instrumentBOMap = instrumentBOList.stream().collect(Collectors.toMap(PointDataInstrumentBO::getInstrumentId, Function.identity()));
                instrumentDOList.forEach(item -> {
                    IndexBO.PointDeviceInfo deviceInfo = new IndexBO.PointDeviceInfo();
                    deviceInfo.setInstrumentName(item.getInstrumentName());
                    deviceInfo.setInstrumentCount(pointService.countPointByProjectId(projectId, item.getId(), null, null, null));
                    PointDataInstrumentBO instrumentBO = instrumentBOMap.get(item.getId());
                    if (Objects.nonNull(instrumentBO)) {
                        deviceInfo.setPointTimeFirst(instrumentBO.getPointTimeFirst());
                        deviceInfo.setPointTimeRecent(instrumentBO.getPointTimeRecent());
                    }
                    deviceInfo.setPointDataCount(Objects.isNull(instrumentBO) ? 0L : instrumentBO.getPointDataCount());
                    deviceInfoList.add(deviceInfo);
                });
                Long totalCount = deviceInfoList.stream().mapToLong(IndexBO.PointDeviceInfo::getInstrumentCount).sum();
                deviceInfoList.forEach(item -> {
                    item.setInstrumentRate(MyCalculateUtils.calculate(item.getInstrumentCount(), totalCount, 2));
                });
            }
            indexBO.setPointDeviceInfoList(deviceInfoList);
        }, damThreadPoolTaskExecutor));
        CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
        return indexBO;
    }

    @Override
    public IndexHeadBO findIndexHeadInfo(Long projectId) {
        IndexHeadBO indexHeadBO = new IndexHeadBO();
        List<CompletableFuture<Void>> result = new ArrayList<>();
        Date currentDate = MyDateUtils.getParseDate(MyDateUtils.getFormatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        result.add(CompletableFuture.runAsync(() -> {
            //设置监测数据数量
            indexHeadBO.setPointDataTotal(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, null, null));
            indexHeadBO.setPointDataToday(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, null, currentDate));
        }, damThreadPoolTaskExecutor));
        result.add(CompletableFuture.runAsync(() -> {
            //设置自动化监测数据数量
            indexHeadBO.setPointDataMcuTotal(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, DataTypeEnum.AUTO_COLLECTION.getType(), null));
            indexHeadBO.setPointDataMcuToday(pointDataJsonService.countPointDataJsonByProjectIdAndDataType(projectId, DataTypeEnum.AUTO_COLLECTION.getType(), currentDate));
        }, damThreadPoolTaskExecutor));

        result.add(CompletableFuture.runAsync(() -> {
            //设置监测仪器数量
            indexHeadBO.setPointDeviceTotal(pointService.countPointByProjectId(projectId, null, null, null, null));
            indexHeadBO.setPointDeviceToday(pointService.countPointByProjectId(projectId, null, null, null, currentDate));
        }, damThreadPoolTaskExecutor));

        result.add(CompletableFuture.runAsync(() -> {
            //设置自动化设备数量
            indexHeadBO.setDeviceMcuTotal(deviceService.countDeviceByProjectIdAndNodeType(projectId, NodeTypeEnum.EDGE.getType(), null));
            indexHeadBO.setDeviceMcuToday(deviceService.countDeviceByProjectIdAndNodeType(projectId, NodeTypeEnum.EDGE.getType(), currentDate));
        }, damThreadPoolTaskExecutor));
        result.add(CompletableFuture.runAsync(() -> {
            //设置在测自动化仪器数量
            indexHeadBO.setDeviceMcuRunTotal(pointService.countPointByProjectId(projectId, null, PointStateEnum.NORMAL.getType(), Arrays.asList(PointTypeEnum.ALL.getType(), PointTypeEnum.AUTO.getType()), null));
            indexHeadBO.setDeviceMcuRunToday(pointService.countPointByProjectId(projectId, null, PointStateEnum.NORMAL.getType(), Arrays.asList(PointTypeEnum.ALL.getType(), PointTypeEnum.AUTO.getType()), currentDate));
        }, damThreadPoolTaskExecutor));
        CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
        return indexHeadBO;
    }

    @Override
    public List<IndexPointDeviceBO> findIndexInstrumentMiddle(Long projectId) {
        List<InstrumentDO> instrumentDOList = instrumentService.getInstrumentList(projectId);
        List<IndexPointDeviceBO> deviceInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(instrumentDOList)) {
            instrumentDOList.forEach(item -> {
                IndexPointDeviceBO deviceInfo = new IndexPointDeviceBO();
                deviceInfo.setInstrumentName(item.getInstrumentName());
                deviceInfo.setInstrumentCount(pointService.countPointByProjectId(projectId, item.getId(), null, null, null));
                deviceInfoList.add(deviceInfo);
            });
            Long totalCount = deviceInfoList.stream().mapToLong(IndexPointDeviceBO::getInstrumentCount).sum();
            deviceInfoList.forEach(item -> {
                item.setInstrumentRate(MyCalculateUtils.calculate(item.getInstrumentCount(), totalCount, 2));
            });
        }
        return deviceInfoList;
    }

    @Override
    public List<IndexPointDeviceBO> findIndexInstrumentList(Long projectId) {
        List<InstrumentDO> instrumentDOList = instrumentService.getInstrumentList(projectId);
        List<IndexPointDeviceBO> deviceInfoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(instrumentDOList)) {
            return Collections.emptyList();
        }

        //long start = System.currentTimeMillis();
        List<Long> instrumentIdList = instrumentDOList.stream().map(InstrumentDO::getId).toList();
        List<CompletableFuture<Void>> result = new ArrayList<>();
        List<PointDataInstrumentBO> instrumentBOList = new CopyOnWriteArrayList<>();
        instrumentIdList.forEach(o -> result.add(CompletableFuture.runAsync(() -> {
            PointDataInstrumentBO pointDataInstrumentBO = pointDataJsonService.findPointDataInstrumentBO(projectId, o);
            if(pointDataInstrumentBO != null) {
                pointDataInstrumentBO.setInstrumentId(o);
                CollectionUtil.addAll(instrumentBOList, pointDataInstrumentBO);
            }
        })));
        CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
        //System.err.println(System.currentTimeMillis() - start);

        Map<Long, PointDataInstrumentBO> instrumentBOMap = instrumentBOList.stream().collect(Collectors.toMap(PointDataInstrumentBO::getInstrumentId, Function.identity()));
        instrumentDOList.forEach(item -> {
            IndexPointDeviceBO deviceInfo = new IndexPointDeviceBO();
            deviceInfo.setInstrumentName(item.getInstrumentName());
            deviceInfo.setInstrumentCount(pointService.countPointByProjectId(projectId, item.getId(), null, null, null));
            PointDataInstrumentBO instrumentBO = instrumentBOMap.get(item.getId());
            if (Objects.nonNull(instrumentBO)) {
                deviceInfo.setPointTimeFirst(instrumentBO.getPointTimeFirst());
                deviceInfo.setPointTimeRecent(instrumentBO.getPointTimeRecent());
            }
            deviceInfo.setPointDataCount(Objects.isNull(instrumentBO) ? 0L : instrumentBO.getPointDataCount());
            deviceInfoList.add(deviceInfo);
        });
        Long totalCount = deviceInfoList.stream().mapToLong(IndexPointDeviceBO::getInstrumentCount).sum();
        deviceInfoList.forEach(item -> {
            item.setInstrumentRate(MyCalculateUtils.calculate(item.getInstrumentCount(), totalCount, 2));
        });
//        if (!CollectionUtils.isEmpty(instrumentDOList)) {
//            List<Long> instrumentIdList = instrumentDOList.stream().distinct().map(InstrumentDO::getId).toList();
//            List<PointDataInstrumentBO> instrumentBOList = new ArrayList<>();
//            List<CompletableFuture<Void>> result = new ArrayList<>();
//            for (Long instrumentId : instrumentIdList) {
//                result.add(CompletableFuture.runAsync(() -> {
//                    instrumentBOList.add(pointDataJsonService.findPointDataInstrumentBO(projectId, instrumentId));
//                }, damThreadPoolTaskExecutor));
//            }
//            CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
//            Map<Long, PointDataInstrumentBO> instrumentBOMap = instrumentBOList.stream().collect(Collectors.toMap(PointDataInstrumentBO::getInstrumentId, Function.identity(), (x1, x2) -> x1));
//            instrumentDOList.forEach(item -> {
//                IndexPointDeviceBO deviceInfo = new IndexPointDeviceBO();
//                deviceInfo.setInstrumentName(item.getInstrumentName());
//                deviceInfo.setInstrumentCount(pointService.countPointByProjectId(projectId, item.getId(), null, null, null));
//                PointDataInstrumentBO instrumentBO = instrumentBOMap.get(item.getId());
//                if (Objects.nonNull(instrumentBO)) {
//                    deviceInfo.setPointTimeFirst(instrumentBO.getPointTimeFirst());
//                    deviceInfo.setPointTimeRecent(instrumentBO.getPointTimeRecent());
//                }
//                deviceInfo.setPointDataCount(Objects.isNull(instrumentBO) ? 0L : instrumentBO.getPointDataCount());
//                deviceInfoList.add(deviceInfo);
//            });
//            Long totalCount = deviceInfoList.stream().mapToLong(IndexPointDeviceBO::getInstrumentCount).sum();
//            deviceInfoList.forEach(item -> {
//                item.setInstrumentRate(MyCalculateUtils.calculate(item.getInstrumentCount(), totalCount, 2));
//            });
//        }
        return deviceInfoList;
    }

    @Override
    public Boolean readIndexData() {
        List<ProjectDO> list = projectService.getAllProjectList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (!CollectionUtils.isEmpty(list)) {
            log.info("project size {}", list.size());
            list.stream().forEach(item -> {
                try {
                    IndexHeadBO indexHeadBO = findIndexHeadInfo(item.getId());
                    if (indexHeadBO != null) {
                        IndexHeadSaveReqVO indexHeadSaveReqVO = new IndexHeadSaveReqVO();
                        indexHeadSaveReqVO.setProjectId(item.getId());
                        indexHeadSaveReqVO.setPointDeviceTotal(indexHeadBO.getPointDeviceTotal());
                        indexHeadSaveReqVO.setPointDeviceToday(indexHeadBO.getPointDeviceToday());
                        indexHeadSaveReqVO.setPointDataTotal(indexHeadBO.getPointDataTotal());
                        indexHeadSaveReqVO.setPointDataToday(indexHeadBO.getPointDataToday());
                        indexHeadSaveReqVO.setDeviceMcuTotal(indexHeadBO.getDeviceMcuTotal());
                        indexHeadSaveReqVO.setDeviceMcuToday(indexHeadBO.getDeviceMcuToday());
                        indexHeadSaveReqVO.setDeviceMcuRunToday(indexHeadBO.getDeviceMcuRunToday());
                        indexHeadSaveReqVO.setDeviceMcuRunTotal(indexHeadBO.getDeviceMcuRunTotal());
                        indexHeadSaveReqVO.setPointDataMcuTotal(indexHeadBO.getPointDataMcuTotal());
                        indexHeadSaveReqVO.setPointDataMcuToday(indexHeadBO.getPointDataMcuToday());
                        indexHeadSaveReqVO.setGenerateTime(sdf.format(new Date()));
                        indexHeadService.createIndexHead(indexHeadSaveReqVO);
                    }
                    List<IndexPointDeviceBO> findIndexInstrumentList = findIndexInstrumentList(item.getId());
                    if (!CollectionUtils.isEmpty(findIndexInstrumentList)) {
                        findIndexInstrumentList.stream().forEach(e -> {
                            IndexMiddleBottomSaveReqVO indexMiddleBottomSaveReqVO = new IndexMiddleBottomSaveReqVO();
                            BeanUtils.copyProperties(e, indexMiddleBottomSaveReqVO);
                            indexMiddleBottomSaveReqVO.setProjectId(item.getId());
                            if (e.getInstrumentCount() != null) {
                                indexMiddleBottomSaveReqVO.setInstrumentCount(e.getInstrumentCount());
                            }
                            if (e.getPointDataCount() != null) {
                                indexMiddleBottomSaveReqVO.setPointDataCount(e.getPointDataCount());
                            }
                            indexMiddleBottomSaveReqVO.setGenerateTime(sdf.format(new Date()));
                            indexMiddleBottomService.createIndexMiddleBottom(indexMiddleBottomSaveReqVO);
                        });
                    }

                } catch (Exception e) {
                    log.error("project {} index info error ", item.getProjectCode());
                }
            });
        }
        return true;
    }
}
