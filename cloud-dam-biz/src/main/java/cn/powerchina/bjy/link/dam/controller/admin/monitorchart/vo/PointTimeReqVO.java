package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 监测图形-分布图的参数
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图 Request VO")
@Data
@ToString(callSuper = true)
public class PointTimeReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @ArraySchema(
            arraySchema = @Schema(description = "测点id"),
            schema = @Schema(implementation = Long.class)
    )
    @NotNull(message = "测点id不能为空")
    @NotEmpty(message = "测点id不能为空")
    private List<Long> pointIdList;

    @Schema(description = "开始时间，yyyy-MM-dd HH:mm:ss格式")
    @NotNull(message = "开始时间不能为空")
    private String minTime;

    @Schema(description = "截至时间，yyyy-MM-dd HH:mm:ss格式")
    @NotNull(message = "截至时间不能为空")
    private String maxTime;
}
