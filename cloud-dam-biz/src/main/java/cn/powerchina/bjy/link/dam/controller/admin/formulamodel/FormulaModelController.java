package cn.powerchina.bjy.link.dam.controller.admin.formulamodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import cn.powerchina.bjy.link.dam.service.formulamodel.FormulaModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 公式关联分量")
@RestController
@RequestMapping("/dam/formula/model")
@Validated
public class FormulaModelController {

    @Resource
    private FormulaModelService formulaModelService;

    @PutMapping("/update")
    @Operation(summary = "更新公式关联分量")
//    @PreAuthorize("@ss.hasPermission('dam:formula-model:update')")
    public CommonResult<Boolean> updateFormulaModel(@Valid @RequestBody FormulaModelSaveReqVO updateReqVO) {
        formulaModelService.updateFormulaModel(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得公式关联分量")
    @Parameter(name = "id", description = "编号", example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:formula-model:query')")
    public CommonResult<FormulaModelRespVO> getFormulaModel(@RequestParam(value = "id", required = false) Long id) {
        FormulaModelRespVO formulaModel = formulaModelService.getFormulaModel(id);
        return success(formulaModel);
    }

}