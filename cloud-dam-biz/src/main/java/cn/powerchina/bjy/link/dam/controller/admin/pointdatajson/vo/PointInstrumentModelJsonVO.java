package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型-测量分量BO Request VO")
@Data
public class PointInstrumentModelJsonVO {

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "标识符")
    private String thingIdentity;

    private BigDecimal thingValue;

    @Schema(description = "分量类型，1：原始值，2：中间值，3：成果值")
    private Integer thingType;
    /**
     * 数据状态(0：未审核，1：正常数据，2：异常数据，3：错误数据）
     */
    private Integer dataStatus;

}