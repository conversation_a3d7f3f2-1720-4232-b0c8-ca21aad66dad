package cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 网关设备采集日志新增/修改 Request VO")
@Data
@Builder
public class DeviceCollectLogSaveReqVO {

    private Long id;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "策略id")
    private Long strategyId;

}