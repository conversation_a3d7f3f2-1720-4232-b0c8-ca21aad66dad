package cn.powerchina.bjy.link.dam.controller.admin.point.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 测点分量
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Data
public class PointInstrumentModelBO {

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "测点编码")
    private String pointCode;

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "分量单位")
    private String thingUnit;
}
