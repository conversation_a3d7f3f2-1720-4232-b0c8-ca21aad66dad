package cn.powerchina.bjy.link.dam.controller.admin.point.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/9
 */
@Data
public class PointBO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 工程分类id
     */
    private Long categoryId;
    /**
     * 组别
     */
    private String categoryGroupName;
    /**
     * 节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点）
     */
    private Integer categoryType;
    /**
     * 测点编号
     */
    private String pointCode;
    /**
     * 测点名称
     */
    private String pointName;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 仪器台账id
     */
    private Long accountId;
    /**
     * 仪器类型名称
     */
    private String instrumentName;
    /**
     * 工程结构id
     */
    private Long structCategoryId;
    /**
     * 工程部位
     */
    private String structCategoryName;
    /**
     * 生产厂家
     */
    private String produceName;
    /**
     * 出厂编号
     */
    private String produceCode;
    /**
     * 仪器型号
     */
    private String produceModel;
    /**
     * 量程规格
     */
    private String rangeSpecifications;
    /**
     * 监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量
     */
    private Integer measureItem;
    /**
     * 重要程度，1：一般，2：重要
     */
    private Integer importantLevel;
    /**
     * 安装时间
     */
    private LocalDateTime installTime;
    /**
     * 基准时间
     */
    private LocalDateTime referenceTime;
    /**
     * 虚拟测点，0：否，1：是
     */
    private Integer virtualPoint;
    /**
     * 安装位置
     */
    private String installPosition;
    /**
     * 安装高程（m）
     */
    private String installHeight;
    /**
     * 埋设位置
     */
    private String hidePosition;
    /**
     * 埋设高程
     */
    private String hideHeight;
    /**
     * 埋设深度
     */
    private String hideDeepth;
    /**
     * 上下桩号
     */
    private String updownPile;
    /**
     * 左右桩号
     */
    private String leftrightPile;
    /**
     * X坐标（m）
     */
    private String xCoordinate;
    /**
     * Y坐标（m）
     */
    private String yCoordinate;
    /**
     * Z坐标（m）
     */
    private String zCoordinate;
    /**
     * 测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他
     */
    private Integer pointState;
    /**
     * 测量类型，1：人/自一体，2：自动化，3：人工
     */
    private Integer pointType;
    /**
     * 测量性质，1：运行期，2：施工期
     */
    private Integer pointStage;

    /**
     * 是否绑定设备，0：未绑定，1：已绑定
     */
    private Integer bindType;

    private Integer manualFrequency;

    /**
     * 自动化观测频次
     */
    private Integer automatedFrequency;
}
