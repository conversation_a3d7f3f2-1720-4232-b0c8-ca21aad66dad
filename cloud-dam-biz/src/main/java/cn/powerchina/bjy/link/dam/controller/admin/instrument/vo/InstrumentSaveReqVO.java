package cn.powerchina.bjy.link.dam.controller.admin.instrument.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 仪器类型新增/修改 Request VO")
@Data
public class InstrumentSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "仪器类型名称")
    @Length(max = 32)
    private String instrumentName;

    @Schema(description = "测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它")
    private Integer measurePrinciple;

    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @Schema(description = "计量单位，1：个，2：支，3：套")
    private Integer measureUnit;

    @Schema(description = "备注")
    @Length(max = 64)
    private String remark;

    @Schema(description = "关联iot产品编码")
    private String productCode;

    /**
     * 仪器类型模板id
     */
    private Long templateId;
}