package cn.powerchina.bjy.link.dam.service.pointdataimport;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.link.dam.config.DamConfig;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdataimport.PointDataImportDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointdataimport.PointDataImportMapper;
import cn.powerchina.bjy.link.dam.enums.DamConstant;
import cn.powerchina.bjy.link.dam.enums.DataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.PointParamsTableEnum;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import com.alibaba.excel.EasyExcel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.DamConstant.IMPORT_TYPE_COVER;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 测点数据导入 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PointDataImportServiceImpl implements PointDataImportService {

    @Resource
    private PointDataImportMapper pointDataImportMapper;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private PointDataJsonService pointDataJsonService;

    @Resource
    private DamConfig damConfig;

    @Resource
    private ThreadPoolTaskExecutor damThreadPoolTaskExecutor;

    @Resource
    private RedisTemplate<String, Integer> redisTemplate;

    @Override
    public Long createPointDataImport(PointDataImportSaveReqVO createReqVO, MultipartFile file) {
        //覆盖，先删除数据
        if (IMPORT_TYPE_COVER.equals(createReqVO.getImportType())) {
            if (Objects.isNull(createReqVO.getStartTime()) || Objects.isNull(createReqVO.getEndTime())) {
                throw exception(POINT_DATA_IMPORT_TIME_NOT_NULL);
            }
            if (createReqVO.getStartTime().isAfter(createReqVO.getEndTime())) {
                throw exception(POINT_DATA_IMPORT_START_END_TIME);
            }
            createReqVO.setEndTime(createReqVO.getEndTime().plusDays(1));
        } else {
            //追加数据，获取数据库中最新的时间
            PointDataJsonDO lastPointDataJson = pointDataJsonService.getLastPointDataJson(createReqVO.getProjectId(), createReqVO.getInstrumentId(), createReqVO.getPointId(),DataTypeEnum.MANUAL.getType());
            if (Objects.nonNull(lastPointDataJson)) {
                createReqVO.setLastPointTime(lastPointDataJson.getPointTime());
            }
        }
        //设置分量map，key=分量标识符，value=分量id
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(createReqVO.getPointId());
        if (CollectionUtils.isEmpty(instrumentModelList)) {
            throw exception(INSTRUMENT_MODEL_NOT_EXISTS);
        }
        Map<Integer, InstrumentModelDO> thingIdentityMap = new HashMap<>();
        //excel中分量从第一列开始
        Integer i = createReqVO.getTemplateType() != 2 ? 1 : 2;
        if (!CollectionUtils.isEmpty(instrumentModelList)) {
            for (InstrumentModelDO item : instrumentModelList) {
                thingIdentityMap.put(i, item);
                i = i + 1;
                String labelName = item.getThingName();
                if (Objects.nonNull(item.getThingUnit())) {
                    labelName = MessageFormat.format("{0}({1})", item.getThingName(), item.getThingUnit());
                }
                createReqVO.getThingIdentityNameList().add(labelName);
            }
            createReqVO.setInstrumentId(instrumentModelList.get(0).getInstrumentId());
        }
        createReqVO.setThingIdentityMap(thingIdentityMap);
        createReqVO.setUserId(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        // 插入
        PointDataImportDO pointDataImport = BeanUtils.toBean(createReqVO, PointDataImportDO.class);
        pointDataImportMapper.insert(pointDataImport);
        //测点有导入任务，并且执行结束的信息还没返回给前端
        String key = String.format(DamConstant.POINT_TASK_STATUS_KEY, pointDataImport.getPointId());
        redisTemplate.opsForValue().set(key, 1);
        Long id = pointDataImport.getId();
        createReqVO.setImportId(id);
        //覆盖导入，删除测点数据
        if (IMPORT_TYPE_COVER.equals(createReqVO.getImportType()) && createReqVO.getTemplateType() == 1) {
            pointDataJsonService.updateByPointIdAndTime(createReqVO.getPointId(), createReqVO.getStartTime().atStartOfDay(), createReqVO.getEndTime().atStartOfDay(), DataTypeEnum.MANUAL.getType(), true, id);
        }
        //异步处理
        damThreadPoolTaskExecutor.execute(new Runnable() {
            @Override
            @Transactional
            public void run() {
                String message = null;
                Integer importStatus = 2;
                try {
                    EasyExcel.read(file.getInputStream(), new ResultDataListener(pointDataJsonService, createReqVO, damConfig.getBatchCount(), damThreadPoolTaskExecutor))
                            //读取文件时会自动判断文件类型，不需设置excelType
                            // .excelType(ExcelTypeEnum.XLSX)
                            .sheet().doRead();
                    message = "导入成功";
                    importStatus = 2;
                } catch (Exception e) {
                    //恢复删除的数据
                    if (IMPORT_TYPE_COVER.equals(createReqVO.getImportType()) && createReqVO.getTemplateType() == 1) {
                        pointDataJsonService.updateByPointIdAndTime(createReqVO.getPointId(), createReqVO.getStartTime().atStartOfDay(), createReqVO.getEndTime().atStartOfDay(), DataTypeEnum.MANUAL.getType(), false, id);
                    }
                    message = e.getMessage();
                    importStatus = 3;
                } finally {
                    //修改导入状态
                    PointDataImportDO pointDataImportDO = pointDataImportMapper.selectById(id);
                    if (null != pointDataImportDO) {
                        pointDataImportDO.setImportStatus(importStatus);
                        pointDataImportDO.setMessage(message);
                        pointDataImportMapper.updateById(pointDataImportDO);
                        log.info("{} 导入完成", id);
                    }
                }
            }
        });
        // 返回
        return id;
    }

    @Override
    public void updatePointDataImport(PointDataImportSaveReqVO updateReqVO) {
        // 校验存在
        validatePointDataImportExists(updateReqVO.getId());
        // 更新
        PointDataImportDO updateObj = BeanUtils.toBean(updateReqVO, PointDataImportDO.class);
        pointDataImportMapper.updateById(updateObj);
    }

    @Override
    public void deletePointDataImport(Long id) {
        // 校验存在
        validatePointDataImportExists(id);
        // 删除
        pointDataImportMapper.deleteById(id);
    }

    private void validatePointDataImportExists(Long id) {
        if (pointDataImportMapper.selectById(id) == null) {
            throw exception(POINT_DATA_IMPORT_NOT_EXISTS);
        }
    }

    @Override
    public PointDataImportDO getPointDataImport(Long id) {
        return pointDataImportMapper.selectById(id);
    }

    @Override
    public PageResult<PointDataImportDO> getPointDataImportPage(PointDataImportPageReqVO pageReqVO) {
        return pointDataImportMapper.selectPage(pageReqVO);
    }

    @Override
    public List<List<String>> getTemplateHead(Long pointId, Integer templateType) {
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(pointId);
        List<List<String>> list = new ArrayList<List<String>>();
        if (2 == templateType) {
            List<String> head0 = new ArrayList<String>();
            head0.add(PointParamsTableEnum.POINT_CODE.getLabelName());
            list.add(head0);
        }
        List<String> head1 = new ArrayList<String>();
        head1.add(PointParamsTableEnum.POINT_TIME.getLabelName());
        list.add(head1);

        if (!CollectionUtils.isEmpty(instrumentModelList)) {
            instrumentModelList.forEach(item -> {
                String labelName = item.getThingName();
                if (Objects.nonNull(item.getThingUnit())) {
                    labelName = MessageFormat.format("{0}({1})", item.getThingName(), item.getThingUnit());
                }
                List<String> headModel = new ArrayList<String>();
                headModel.add(labelName);
                list.add(headModel);
            });
        }
        return list;
    }

    @Override
    public PointDataImportDO getLastPointDataImportDO(Long pointId) {
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        List<PointDataImportDO> pointDataImportDOS = pointDataImportMapper.selectList(new LambdaQueryWrapperX<PointDataImportDO>()
                .eq(PointDataImportDO::getPointId, pointId).eq(PointDataImportDO::getCreator, String.valueOf(loginUserId))
                .orderByDesc(PointDataImportDO::getCreateTime)
                .last("limit 1"));
        if (CollectionUtils.isEmpty(pointDataImportDOS)) {
            return new PointDataImportDO();
        }
        //如果任务已执行完成，移除测点任务执行状态
        PointDataImportDO pointDataImportDO = pointDataImportDOS.get(0);
        if (pointDataImportDO.getImportStatus() != 1) {
            String key = String.format(DamConstant.POINT_TASK_STATUS_KEY, pointId);
            redisTemplate.delete(key);
        }
        return pointDataImportDO;
    }

}