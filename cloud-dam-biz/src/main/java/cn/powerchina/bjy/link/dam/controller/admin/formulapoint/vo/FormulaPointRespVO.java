package cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 公式关联测点 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FormulaPointRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15140")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "测点公式id", example = "11278")
    @ExcelProperty("测点公式id")
    private Long pointFormulaId;

    @Schema(description = "关联测点id", example = "23886")
    @ExcelProperty("关联测点id")
    private Long pointId;

    @Schema(description = "分量id", example = "26093")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "数据范围，1：全部，2：自动化，3：人工", example = "2")
    @ExcelProperty("数据范围，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值")
    @ExcelProperty("取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值")
    private Integer dataCondition;

    @Schema(description = "数值或1：日统计值，2：月统计值")
    @ExcelProperty("数值或1：日统计值，2：月统计值")
    private Integer dataValue;

    @Schema(description = "单位，1：分钟，2：小时，3：天或1：平均值，2：最大值，3：最小值，4：累计值")
    @ExcelProperty("单位，1：分钟，2：小时，3：天或1：平均值，2：最大值，3：最小值，4：累计值")
    private Integer dataUnit;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}