package cn.powerchina.bjy.link.dam.controller.admin.point.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/9
 */
@Data
public class PointSimpleRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点编号")
    @ExcelProperty("测点编号")
    private String pointCode;

    @Schema(description = "测点名称")
    @ExcelProperty("测点名称")
    private String pointName;
}
