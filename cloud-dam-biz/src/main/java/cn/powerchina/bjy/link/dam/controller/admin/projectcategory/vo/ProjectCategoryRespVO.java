package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 工程分类管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectCategoryRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private String id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "父节点id")
    @ExcelProperty("父节点id")
    private String parentId;

    @Schema(description = "节点名称")
    @ExcelProperty("节点名称")
    private String categoryName;

    @Schema(description = "节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点，6：网关设备（采集仪），7：网关子设备（传感器））")
    @ExcelProperty("节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点），6：网关设备（采集仪），7：网关子设备（传感器））")
    private Integer categoryType;

    @Schema(description = "层级")
    @ExcelProperty("层级")
    private Integer categoryLevel;

    @Schema(description = "业务id")
    @ExcelProperty("业务id")
    private Long businessId;

    @Schema(description = "连接状态（0-离线；1-在线；）")
    @ExcelProperty("连接状态（0-离线；1-在线；）")
    private Integer linkState;

    @Schema(description = "测量类型，1：人/自一体，2：自动化，3：人工")
    private Integer pointType;

}