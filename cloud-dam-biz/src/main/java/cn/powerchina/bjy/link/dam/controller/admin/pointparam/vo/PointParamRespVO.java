package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 测点计算参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointParamRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25970")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "20428")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id", example = "10551")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "测点参数")
    @ExcelProperty("测点参数")
    private String calcParam;

    @Schema(description = "有效开始时间")
    @ExcelProperty("有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    @ExcelProperty("有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    @ExcelProperty("适用类型，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}