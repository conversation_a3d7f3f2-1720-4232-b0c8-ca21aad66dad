package cn.powerchina.bjy.link.dam.controller.admin.importfile;

import cn.hutool.core.io.IoUtil;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;

import cn.powerchina.bjy.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static cn.powerchina.bjy.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importfile.ImportFileDO;
import cn.powerchina.bjy.link.dam.service.importfile.ImportFileService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 导入信息")
@RestController
@RequestMapping("/dam/import-file")
@Validated
public class ImportFileController {

    @Resource
    private ImportFileService importFileService;

    @PostMapping("/create")
    @Operation(summary = "创建导入信息")
    @PreAuthorize("@ss.hasPermission('dam:import-file:create')")
    public CommonResult<Long> createImportFile(@Valid @RequestBody ImportFileSaveReqVO createReqVO) {
        return success(importFileService.createImportFile(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新导入信息")
//    @PreAuthorize("@ss.hasPermission('dam:import-file:update')")
    public CommonResult<Boolean> updateImportFile(@Valid @RequestBody ImportFileSaveReqVO updateReqVO) throws Exception{
        importFileService.updateImportFile(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除导入信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:import-file:delete')")
    public CommonResult<Boolean> deleteImportFile(@RequestParam("id") Long id) {
        importFileService.deleteImportFile(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除导入信息")
                @PreAuthorize("@ss.hasPermission('dam:import-file:delete')")
    public CommonResult<Boolean> deleteImportFileList(@RequestParam("ids") List<Long> ids) {
        importFileService.deleteImportFileListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得导入信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('dam:import-file:query')")
    public CommonResult<ImportFileRespVO> getImportFile(@RequestParam("id") Long id) {
        ImportFileDO importFile = importFileService.getImportFile(id);
        return success(BeanUtils.toBean(importFile, ImportFileRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得导入信息分页")
//    @PreAuthorize("@ss.hasPermission('dam:import-file:query')")
    public CommonResult<PageResult<ImportFileRespVO>> getImportFilePage(@Valid ImportFilePageReqVO pageReqVO) {
        PageResult<ImportFileDO> pageResult = importFileService.getImportFilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImportFileRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出导入信息 Excel")
//    @PreAuthorize("@ss.hasPermission('dam:import-file:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImportFileExcel(@Valid ImportFilePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImportFileDO> list = importFileService.getImportFilePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "导入信息.xls", "数据", ImportFileRespVO.class,
                        BeanUtils.toBean(list, ImportFileRespVO.class));
    }
    @PostMapping("/uploadExcel")
    @Operation(summary = "上传Excel")
    public CommonResult<Boolean> uploadFileExcel(@Valid  UploadFileVO uploadFileVO) throws IOException {
        Boolean b = importFileService.importExcel(uploadFileVO);
        return success(b);
    }

    @GetMapping("/list")
    @Operation(summary = "获得导入列表")
//    @PreAuthorize("@ss.hasPermission('dam:import-file:query')")
    public CommonResult<List<ImportFileRespVO>> getImportFilePage(@RequestParam("projectId") Long projectId,
                                                                  @RequestParam("nodeId") Long nodeId) {
        List<ImportFileRespVO> list= importFileService.getFIleList(projectId, nodeId);
        return success(list);
    }
    @PostMapping("/import")
    @Operation(summary = "导入Excel")
    public CommonResult<Boolean> ImportFile(@Valid @RequestBody ImportFileVO  importFileVO)  {
        Boolean b = importFileService.importFile(importFileVO);
        return success(b);
    }

    @PostMapping("/move")
    @Operation(summary = "文件移动")
    public CommonResult<Boolean> moveFileExcel(@Valid @RequestBody ImportFileMoveVO importFileMoveVO) {
        importFileService.moveFileExcel(importFileMoveVO);
        return success(true);
    }
}