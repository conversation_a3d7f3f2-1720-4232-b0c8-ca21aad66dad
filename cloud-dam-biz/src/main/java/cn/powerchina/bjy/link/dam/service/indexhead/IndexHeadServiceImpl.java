package cn.powerchina.bjy.link.dam.service.indexhead;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexhead.IndexHeadDO;
import cn.powerchina.bjy.link.dam.dal.mysql.indexhead.IndexHeadMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INDEX_HEAD_NOT_EXISTS;

/**
 * 大坝首页头部信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IndexHeadServiceImpl implements IndexHeadService {

    @Resource
    private IndexHeadMapper indexHeadMapper;

    @Override
    public Long createIndexHead(IndexHeadSaveReqVO createReqVO) {
        // 插入
        IndexHeadDO indexHead = BeanUtils.toBean(createReqVO, IndexHeadDO.class);
        indexHeadMapper.insert(indexHead);
        // 返回
        return indexHead.getId();
    }

    @Override
    public void updateIndexHead(IndexHeadSaveReqVO updateReqVO) {
        // 校验存在
        validateIndexHeadExists(updateReqVO.getId());
        // 更新
        IndexHeadDO updateObj = BeanUtils.toBean(updateReqVO, IndexHeadDO.class);
        indexHeadMapper.updateById(updateObj);
    }

    @Override
    public void deleteIndexHead(Long id) {
        // 校验存在
        validateIndexHeadExists(id);
        // 删除
        indexHeadMapper.deleteById(id);
    }

    private void validateIndexHeadExists(Long id) {
        if (indexHeadMapper.selectById(id) == null) {
            throw exception(INDEX_HEAD_NOT_EXISTS);
        }
    }

    @Override
    public IndexHeadDO getIndexHead(Long id) {
        return indexHeadMapper.selectById(id);
    }

    @Override
    public PageResult<IndexHeadDO> getIndexHeadPage(IndexHeadPageReqVO pageReqVO) {
        return indexHeadMapper.selectPage(pageReqVO);
    }

}
