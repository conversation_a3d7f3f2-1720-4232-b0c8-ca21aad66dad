package cn.powerchina.bjy.link.dam.service.pointevaluate;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.bo.PointDataInstrumentBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.bo.PointEvaluateBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.EffectiveTimeVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointevaluate.PointEvaluateMapper;
import cn.powerchina.bjy.link.dam.enums.ApplyTypeEnum;
import cn.powerchina.bjy.link.dam.enums.PointTypeEnum;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.pointparam.PointParamService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 测点评价指标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PointEvaluateServiceImpl implements PointEvaluateService {

    @Resource
    private PointEvaluateMapper pointEvaluateMapper;

    @Autowired
    private PointParamService pointParamService;

    @Autowired
    private InstrumentModelService instrumentModelService;

    @Autowired
    private ProjectCategoryService projectCategoryService;

    @Override
    public Long createPointEvaluate(PointEvaluateSaveReqVO createReqVO) {
        // 插入
        getListAndCheckTime(createReqVO);
        PointEvaluateDO pointEvaluate = BeanUtils.toBean(createReqVO, PointEvaluateDO.class);
        instrumentModelService.validateInstrumentModelExists(createReqVO.getInstrumentModelId());
        pointEvaluateMapper.insert(pointEvaluate);
        // 返回
        return pointEvaluate.getId();
    }

    @Override
    public void updatePointEvaluate(PointEvaluateSaveReqVO updateReqVO) {
        // 校验存在
        validatePointEvaluateExists(updateReqVO.getId());
        getListAndCheckTime(updateReqVO);
        // 更新
        PointEvaluateDO updateObj = BeanUtils.toBean(updateReqVO, PointEvaluateDO.class);
        instrumentModelService.validateInstrumentModelExists(updateReqVO.getInstrumentModelId());
        pointEvaluateMapper.updateById(updateObj);
    }

    @Override
    public void deletePointEvaluate(Long id) {
        // 校验存在
        validatePointEvaluateExists(id);
        // 删除
        pointEvaluateMapper.deleteById(id);
    }

    private void validatePointEvaluateExists(Long id) {
        PointEvaluateDO pointEvaluateDO = pointEvaluateMapper.selectById(id);
        if (pointEvaluateDO == null) {
            throw exception(POINT_EVALUATE_NOT_EXISTS);
        }
    }

    @Override
    public PointEvaluateDO getPointEvaluate(Long id) {
        return pointEvaluateMapper.selectById(id);
    }

    @Override
    public PageResult<PointEvaluatePageRespVO> getPointEvaluatePage(PointEvaluatePageReqVO pageReqVO) {
        List<Long> pointList = projectCategoryService.getPointListByCategoryId(pageReqVO.getCategoryId());
        //空集合设置值
        if (CollectionUtil.isEmpty(pointList)) {
            pointList.add(Long.MAX_VALUE);
        }
        pageReqVO.setPointIdList(pointList);
        PageResult<PointEvaluatePageRespVO> PointEvaluatePageResp = pointEvaluateMapper.selectPage(pageReqVO);
        //增加仪器类型列
        ProjectCategoryLevelBO category = projectCategoryService.getProjectCategoryLevelBO(pageReqVO.getCategoryId());
        PointEvaluatePageResp.getList().forEach(item -> {
            item.setInstrumentName(category.getInstrumentCategoryDO().getCategoryName());
            InstrumentModelDO instrumentModelDO = instrumentModelService.getInstrumentModel(item.getInstrumentModelId());
            if (Objects.nonNull(instrumentModelDO)) {
                item.setThingName(instrumentModelDO.getThingName());
                item.setThingUnit(instrumentModelDO.getThingUnit());
            }
        });

        return PointEvaluatePageResp;
    }

    @Override
    public List<PointEvaluatePageRespVO> selectList(PointEvaluateReqVO pageReqVO) {
        //时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        LocalDateTime startTime = LocalDateTime.parse("1970-01-01 08:00:00", formatter);
        LocalDateTime endTime = LocalDateTime.parse("2100-12-31 23:59:59", formatter);
        List<Long> pointList = projectCategoryService.getPointListByCategoryId(pageReqVO.getCategoryId());
        //空集合设置值
        if (CollectionUtil.isEmpty(pointList)) {
            return null;
        }

        pageReqVO.setPointIdList(pointList);
        List<PointEvaluatePageRespVO> evaluatePageRespVOList = pointEvaluateMapper.selectList1(pageReqVO);
        //增加仪器类型列
        ProjectCategoryLevelBO category = projectCategoryService.getProjectCategoryLevelBO(pageReqVO.getCategoryId());
        evaluatePageRespVOList.forEach(item -> {
            item.setInstrumentName(category.getInstrumentCategoryDO().getCategoryName());
            item.setDefaultEvaluate(false);
            if (item.getEffectiveStartTime() == null) {
                item.setEffectiveStartTime(startTime);
            }
            if (item.getEffectiveEndTime() == null) {
                item.setEffectiveEndTime(endTime);
            }
        });

        //设置评价指标的分量
        List<String> pointInstrumentIdList = evaluatePageRespVOList.stream()
                .map(vo -> vo.getPointCode() + "-" + vo.getInstrumentModelId())
                .collect(Collectors.toList());
        //默认的评价指标
        List<PointEvaluatePageRespVO> defaultModelList = pointEvaluateMapper.selectModelList(pageReqVO);
        //没有设置评价指标的分量（页面展示）
        List<PointEvaluatePageRespVO> modelList = new ArrayList<>();
        defaultModelList.forEach(item -> {
            if (!pointInstrumentIdList.contains(item.getPointCode() + "-" + item.getInstrumentModelId())) {
                item.setInstrumentName(category.getInstrumentCategoryDO().getCategoryName());
                //时间
                item.setEffectiveStartTime(startTime);
                item.setEffectiveEndTime(endTime);
                item.setApplyType(ApplyTypeEnum.ALL.getCode());
                item.setDefaultEvaluate(true);
                modelList.add(item);
            }
        });
        evaluatePageRespVOList.addAll(modelList);
        //成果值
        if (CollectionUtil.isNotEmpty(pageReqVO.getThingTypeList())) {
            List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(pointList.get(0));
            List<Long> modelIdList = instrumentModelList.stream().filter(item -> pageReqVO.getThingTypeList().contains(item.getThingType())).map(InstrumentModelDO::getId).toList();
            evaluatePageRespVOList = evaluatePageRespVOList.stream().filter(item -> modelIdList.contains(item.getInstrumentModelId())).toList();
        }

        return evaluatePageRespVOList;
    }

    @Override
    public List<PointEvaluateDO> getPointEvaluateByPointIdAndModelId(Long pointId, Long instrumentModelId) {
        return pointEvaluateMapper.selectList(new LambdaQueryWrapperX<PointEvaluateDO>()
                .eq(PointEvaluateDO::getPointId, pointId)
                .eq(PointEvaluateDO::getInstrumentModelId, instrumentModelId)
                .orderByDesc(PointEvaluateDO::getEffectiveStartTime));
    }

    @Override
    public PointEvaluateBO getPointEvaluateBO(Long id) {
        PointEvaluateDO evaluateDO = getPointEvaluate(id);
        if (Objects.nonNull(evaluateDO)) {
            PointEvaluateBO evaluateBO = new PointEvaluateBO();
            org.springframework.beans.BeanUtils.copyProperties(evaluateDO, evaluateBO);
            if (Objects.nonNull(evaluateBO.getInstrumentModelId())) {
                InstrumentModelDO instrumentModelDO = instrumentModelService.getInstrumentModel(evaluateBO.getInstrumentModelId());
                if (Objects.nonNull(instrumentModelDO)) {
                    evaluateBO.setDownLimit(instrumentModelDO.getDownLimit());
                    evaluateBO.setUpLimit(instrumentModelDO.getUpLimit());
                    evaluateBO.setDecimalLimit(instrumentModelDO.getDecimalLimit());
                }
            }
            return evaluateBO;
        }
        return null;
    }

    @Override
    public String resetEvaluateExtremeId(List<PointEvaluateSaveReqVO> saveReqVOList, Long evaluateExtremeId, Long id) {
        pointEvaluateMapper.delete(new LambdaQueryWrapperX<PointEvaluateDO>()
                .eq(PointEvaluateDO::getEvaluateExtremeId, evaluateExtremeId)
                .ne(PointEvaluateDO::getId, id));
        if (saveReqVOList.size() <= 0) {
            return null;
        }
        StringBuilder errorMsg = new StringBuilder();
        for (PointEvaluateSaveReqVO pointEvaluateSaveReqVO : saveReqVOList) {
            try {
                createPointEvaluate(pointEvaluateSaveReqVO);
            } catch (Exception e) {
                if (errorMsg.length() > 0) {
                    errorMsg.append("、");
                }
                errorMsg.append(pointEvaluateSaveReqVO.getPointCode());
                log.warn("有效时段重合:{},{}", pointEvaluateSaveReqVO.getPointId(), pointEvaluateSaveReqVO.getProjectId());
            }
        }
        if (errorMsg.length() > 1) {
            errorMsg.append("有效时段重合！");
        }
        return errorMsg.toString();
    }

    @Override
    public List<PointEvaluatePageRespVO> getSelectedPoint(Long extremeId) {
        List<PointEvaluatePageRespVO> pointEvaluatePageRespVOS = pointEvaluateMapper.selectSelectedList(extremeId);
        return pointEvaluatePageRespVOS;
    }

    private void getListAndCheckTime(PointEvaluateSaveReqVO updateReqVO) {
        List<Integer> applyTypeList = new ArrayList<>();
        ApplyTypeEnum applyTypeEnum = ApplyTypeEnum.getByCode(updateReqVO.getApplyType());
        switch (applyTypeEnum) {
            case AUTO:
                applyTypeList.addAll(Arrays.asList(ApplyTypeEnum.AUTO.getCode(), ApplyTypeEnum.ALL.getCode()));
                break;
            case MANUAL:
                applyTypeList.addAll(Arrays.asList(ApplyTypeEnum.MANUAL.getCode(), ApplyTypeEnum.ALL.getCode()));
                break;
            default:
                applyTypeList.addAll(Collections.singletonList(ApplyTypeEnum.ALL.getCode()));
                applyTypeList.addAll(Arrays.asList(ApplyTypeEnum.MANUAL.getCode(), ApplyTypeEnum.AUTO.getCode()));
                break;
        }
        //校验时间段重合
        List<PointEvaluateDO> pointEvaluateDOList = pointEvaluateMapper.selectList(new LambdaQueryWrapperX<PointEvaluateDO>()
                .eq(PointEvaluateDO::getPointId, updateReqVO.getPointId())
                .eq(PointEvaluateDO::getInstrumentModelId, updateReqVO.getInstrumentModelId()).inIfPresent(PointEvaluateDO::getApplyType, applyTypeList)
                .orderByDesc(PointEvaluateDO::getEffectiveStartTime));
        if (null != updateReqVO.getEvaluateExtremeId()) {
            List<PointEvaluateDO> pointEvaluateDOS = pointEvaluateMapper.selectList(new LambdaQueryWrapperX<PointEvaluateDO>()
                    .eq(PointEvaluateDO::getEvaluateExtremeId, updateReqVO.getEvaluateExtremeId()));
            if (null != pointEvaluateDOS) {
                List<Long> collect = pointEvaluateDOS.stream().map(s -> s.getId()).collect(Collectors.toList());
                pointEvaluateDOList = pointEvaluateDOList.stream().filter(item -> !collect.contains(item.getId())).collect(Collectors.toList());
            }
        }
        //开始和结束时间，一个为空 就算是全空
        List<PointEvaluateDO> nullList = pointEvaluateDOList.stream().filter(item -> item.getEffectiveStartTime() == null || item.getEffectiveEndTime() == null).toList();
        if (Objects.nonNull(updateReqVO.getId())) {
            //排除本身
            nullList = nullList.stream().filter(item -> !item.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
            pointEvaluateDOList = pointEvaluateDOList.stream().filter(item -> !item.getId().equals(updateReqVO.getId())).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(nullList)) {
            throw exception(POINT_EVALUATE_TIME_LAP);
        }
        if (null != updateReqVO.getEffectiveStartTime() && null != updateReqVO.getEffectiveEndTime()) {
            if (updateReqVO.getEffectiveStartTime().isAfter(updateReqVO.getEffectiveEndTime())) {
                throw exception(POINT_PARAM_TIME_ERROR);
            }
            //排除时间为空的数据
            pointEvaluateDOList = pointEvaluateDOList.stream().filter(item -> item.getEffectiveStartTime() != null && item.getEffectiveEndTime() != null).toList();
            if (CollectionUtil.isNotEmpty(pointEvaluateDOList)) {
                if (pointParamService.checkTimeOverLap(BeanUtils.toBean(pointEvaluateDOList, EffectiveTimeVO.class), BeanUtils.toBean(updateReqVO, EffectiveTimeVO.class))) {
                    throw exception(POINT_EVALUATE_TIME_LAP);
                }
            }
        } else {
            if (CollectionUtil.isNotEmpty(pointEvaluateDOList)) {
                throw exception(POINT_EVALUATE_TIME_LAP);
            }
        }
    }
}
