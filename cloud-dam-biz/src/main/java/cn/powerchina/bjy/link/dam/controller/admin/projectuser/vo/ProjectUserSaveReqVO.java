package cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo;

import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 项目用户新增/修改 Request VO")
@Data
public class ProjectUserSaveReqVO extends UserSaveReqVO {

    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12622")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "角色id集合")
    @NotNull(message = "角色权限不能为空")
    private List<Long> roleIds;

}