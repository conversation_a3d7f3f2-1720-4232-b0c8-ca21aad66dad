package cn.powerchina.bjy.link.dam.controller.admin.point.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 测点信息新增/修改 Request VO")
@Data
public class PointSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "工程分类id")
    @NotNull(message = "请选择仪器类型或分组")
    private Long categoryId;

    @NotBlank(message = "请输入测点编号")
    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "仪器台账id")
    private Long accountId;

    @Schema(description = "测点名称")
    private String pointName;

    @Schema(description = "量程规格")
    private String rangeSpecifications;

    @NotNull(message = "请选择监测项目")
    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @NotNull(message = "请选择重要程度")
    @Schema(description = "重要程度，1：一般，2：重要")
    private Integer importantLevel;

    @Schema(description = "安装时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime installTime;

    @Schema(description = "基准时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime referenceTime;

    @NotNull(message = "请选择虚拟测点")
    @Schema(description = "虚拟测点，0：否，1：是")
    private Integer virtualPoint;

    @Schema(description = "安装位置")
    private String installPosition;

    @Schema(description = "安装高程（m）")
    private String installHeight;

    @Schema(description = "埋设位置")
    private String hidePosition;

    @Schema(description = "埋设高程")
    private String hideHeight;

    @Schema(description = "埋设深度")
    private String hideDeepth;

    @Schema(description = "上下桩号")
    private String updownPile;

    @Schema(description = "左右桩号")
    private String leftrightPile;

    @Schema(description = "X坐标（m）")
    private String xCoordinate;

    @Schema(description = "Y坐标（m）")
    private String yCoordinate;

    @Schema(description = "Z坐标（m）")
    private String zCoordinate;

    @NotNull(message = "请选择测点状态")
    @Schema(description = "测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他")
    private Integer pointState;

    @NotNull(message = "请选择测量类型")
    @Schema(description = "测量类型，1：人/自一体，2：自动化，3：人工")
    private Integer pointType;

    @Schema(description = "测量性质，1：运行期，2：施工期")
    private Integer pointStage;

    @Schema(description = "人工观测频次")
    private Integer manualFrequency;

    @Schema(description = "自动化观测频次")
    private Integer automatedFrequency;

}