package cn.powerchina.bjy.link.dam.service.pointdatajson;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataTDDO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.bo.PointDataInstrumentBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamTableRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointdata.PointDataMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointdatajson.PointDataJsonMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointevaluate.PointEvaluateMapper;
import cn.powerchina.bjy.link.dam.dal.tdengine.DamPointDataMapper;
import cn.powerchina.bjy.link.dam.enums.DataStatusEnum;
import cn.powerchina.bjy.link.dam.enums.DataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.InstrumentThingTypeEnum;
import cn.powerchina.bjy.link.dam.enums.ReviewStatusEnum;
import cn.powerchina.bjy.link.dam.framework.tdengine.TDengineTableField;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.DamConstant.SYSTEM_NAME;
import static cn.powerchina.bjy.link.dam.enums.DamConstant.SYSTEM_USER_ID;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 测点数据json Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PointDataJsonServiceImpl implements PointDataJsonService {

    @Resource
    private PointDataJsonMapper pointDataJsonMapper;

    @Resource
    private PointService pointService;

    @Resource
    @Lazy
    private PointDataService pointDataService;

    @Resource
    private PointDataMapper pointDataMapper;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private InstrumentService instrumentService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DamPointDataMapper damPointDataMapper;
    @Resource
    private PointEvaluateMapper pointEvaluateMapper;

    @Resource
    private PointMapper pointMapper;
    @Override
    public Long createPointDataJson(PointDataJsonSaveReqVO createReqVO) {
//        List<PointDataDO> pointDataDOList = batchPointData(createReqVO);
//        // 插入
//        PointDataJsonDO pointDataJson = BeanUtils.toBean(createReqVO, PointDataJsonDO.class);
////        pointDataJson.setDataType(DataTypeEnum.MANUAL.getType());
//            createReqVO.setDataType(createReqVO.getIsAutomation() == 0 ? 2 : 1);
//        setPointJsonStatus(pointDataDOList, pointDataJson);
//        pointDataJsonMapper.insert(pointDataJson);
//        // 返回
//        return pointDataJson.getId();
        pointDataService.defineDevicePropertyData(createReqVO.getProjectId(),createReqVO.getInstrumentId());
        insertPointData(createReqVO);
        return 1L;
    }

    /**
     * 获取数据状态
     *
     * @param pointDataDOList
     * @return
     */
    public void setPointJsonStatus(List<PointDataDO> pointDataDOList, PointDataJsonDO pointDataJson) {
        //正常状态
        List<PointDataDO> anomalousDataList = pointDataDOList.stream().filter(item -> DataStatusEnum.NORMAL.getType().equals(item.getDataStatus())).toList();
        Integer status = CollectionUtil.isNotEmpty(anomalousDataList) ? DataStatusEnum.NORMAL.getType() : null;
        //未判定状态
        anomalousDataList = pointDataDOList.stream().filter(item -> DataStatusEnum.UNDETERMINED.getType().equals(item.getDataStatus())).toList();
        status = CollectionUtil.isNotEmpty(anomalousDataList) ? DataStatusEnum.UNDETERMINED.getType() : status;
        //异常状态
        anomalousDataList = pointDataDOList.stream().filter(item -> DataStatusEnum.ANOMALOUS.getType().equals(item.getDataStatus())).toList();
        status = CollectionUtil.isNotEmpty(anomalousDataList) ? DataStatusEnum.ANOMALOUS.getType() : status;
        //错误状态
        anomalousDataList = pointDataDOList.stream().filter(item -> DataStatusEnum.ERROR_DATA.getType().equals(item.getDataStatus())).toList();
        status = CollectionUtil.isNotEmpty(anomalousDataList) ? DataStatusEnum.ERROR_DATA.getType() : status;
        pointDataJson.setDataStatus(status);
        if (!DataStatusEnum.NORMAL.getType().equals(status)) {
            pointDataJson.setReviewStatus(ReviewStatusEnum.NOT_REVIEWED.getType());
        } else {
            pointDataJson.setReviewName(SYSTEM_NAME);
            pointDataJson.setReviewer(SYSTEM_USER_ID);
            pointDataJson.setReviewStatus(ReviewStatusEnum.APPROVED.getType());
        }
        //创建人
        pointDataJson.setCreator(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        pointDataJson.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
    }

    @Override
    public PointDO getPointByProjectIdAndPointCode(Long projectId, String pointCode) {
        return pointService.getPointByProjectIdAndPointCode(projectId, pointCode);
    }

    @Override
    public List<PointDataJsonDO> getPointDataJsonList(PointDataReqVO reqVO) {
//        LambdaQueryWrapperX<PointDataJsonDO> pointDataJsonDOMPJLambdaWrapperX = new LambdaQueryWrapperX<PointDataJsonDO>()
//                .eqIfPresent(PointDataJsonDO::getPointId, reqVO.getPointId())
//                .betweenIfPresent(PointDataJsonDO::getPointTime, reqVO.getPointTime())
//                .eqIfPresent(PointDataJsonDO::getDataType, reqVO.getDataType());
//        pointDataJsonDOMPJLambdaWrapperX.select(PointDataJsonDO::getPointData, PointDataJsonDO::getPointTime, PointDataJsonDO::getDataType);
//        // 审核状态
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(reqVO.getReviewStatusList())) {
//            pointDataJsonDOMPJLambdaWrapperX.in(PointDataJsonDO::getReviewStatus, reqVO.getReviewStatusList());
//        }
//        // 数据状态
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(reqVO.getDataStatusList())) {
//            pointDataJsonDOMPJLambdaWrapperX.in(PointDataJsonDO::getDataStatus, reqVO.getDataStatusList());
//        }
//        pointDataJsonDOMPJLambdaWrapperX.orderByAsc(PointDataJsonDO::getPointTime);
//        return pointDataJsonMapper.selectList(pointDataJsonDOMPJLambdaWrapperX);
        PointBO pointBO = pointService.getPointBO(reqVO.getPointId());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        PointDataTRespVO pointDataTRespVO = BeanUtils.toBean(reqVO, PointDataTRespVO.class);
        pointDataTRespVO.setInstrumentId(pointBO.getInstrumentId());
        if(pointDataTRespVO.getPointTime()!=null){
            pointDataTRespVO.setStartPointTime(reqVO.getPointTime()[0].format(formatter));
            pointDataTRespVO.setEndPointTime(reqVO.getPointTime()[1].format(formatter));
            pointDataTRespVO.setPointTime(null);
        }
        try {
            List<Map> list = damPointDataMapper.selectPointDateJson(pointDataTRespVO);
            return mapToJsonDO(reqVO.getProjectId(),pointBO.getInstrumentId(), reqVO.getPointId(),list);
        } catch (Exception e) {
            return null;
        }

    }

    public List<PointDataJsonDO>  mapToJsonDO(Long projectId,Long instrumentId,Long pointId,List<Map>  list){
        List<PointDataJsonDO> pointDataJsonDOList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
        // 兼容两种毫秒格式的灵活格式化器
        DateTimeFormatter flexibleFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-MM-dd HH:mm:ss")
                .appendFraction(ChronoField.MILLI_OF_SECOND, 1, 3, true)
                .toFormatter();
        List<InstrumentModelDO> instrumentModels = instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>()
                .eq(InstrumentModelDO::getInstrumentId, instrumentId)
                .eq(InstrumentModelDO::getProjectId, projectId)
        ) ;
        for(Map map:list){
            PointDataJsonDO pointDataJsonDO = new PointDataJsonDO();
            pointDataJsonDO.setProjectId(projectId);
            pointDataJsonDO.setPointId(pointId);
            pointDataJsonDO.setInstrumentId(instrumentId);
            String pointTime=map.get("ts").toString();
            String createTime=map.get("create_time").toString();
            LocalDateTime dateTime = LocalDateTime.parse(pointTime, formatter);
            LocalDateTime creat = LocalDateTime.parse(createTime, flexibleFormatter);
            pointDataJsonDO.setPointTime(dateTime);
            String pointDate="[";
            for(InstrumentModelDO instrumentModel:instrumentModels){
                pointDate=pointDate+"{";
                pointDate=pointDate+"\"thingName\":\""+instrumentModel.getThingName()+"\",\"thingValue\":"+map.get(instrumentModel.getThingIdentity().toLowerCase())+",";
                pointDate=pointDate+"\"thingIdentity\":\""+instrumentModel.getThingIdentity()+"\",\"instrumentModelId\":\""+instrumentModel.getId()+"\"";
                pointDate=pointDate+"},";
            }
            pointDate=pointDate.substring(0,pointDate.length()-1);
            pointDate=pointDate+"]";
            pointDataJsonDO.setPointData(pointDate);
            pointDataJsonDO.setDataType(map.get("data_type")!=null ? Integer.valueOf(String.valueOf(map.get("data_type"))):0);
            pointDataJsonDO.setDataStatus(map.get("data_status")!=null ? Integer.valueOf(String.valueOf(map.get("data_status"))):0);
            pointDataJsonDO.setReviewStatus(map.get("review_status")!=null ? Integer.valueOf(String.valueOf(map.get("review_status"))):0);
            pointDataJsonDO.setReviewer(map.get("reviewer")!=null ? String.valueOf(map.get("reviewer")):"");
            pointDataJsonDO.setReviewName(map.get("review_name")!=null ? String.valueOf(map.get("review_name")): "");
            pointDataJsonDO.setCreator(map.get("creator")!=null ? String.valueOf(map.get("creator")): "");
            pointDataJsonDO.setCreateTime(creat);
            pointDataJsonDOList.add(pointDataJsonDO);
        }
        return pointDataJsonDOList;
    }

    @Override
    public List<List<String>> getDataList(PointDataJsonPageReqVO pageReqVO) {
        List<List<String>> dataList = new ArrayList<>();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<PointDataJsonRespVO> pageResult = getPointDataJsonPage(pageReqVO);
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            List<PointParamTableRespVO> headList = instrumentModelService.getModelTable(pageReqVO.getPointId(), pageReqVO.getThingTypeList());
            pageResult.getList().forEach(item -> {
                PointDataJsonExportVO pointDataJsonExportVO = BeanUtils.toBean(item, PointDataJsonExportVO.class);
                Class<?> clazz = pointDataJsonExportVO.getClass();
                Field[] fields = clazz.getDeclaredFields();
                List<String> rowData = new ArrayList<>();
                for (Field field : fields) {
                    field.setAccessible(true);
                    // 获取字段名和值
                    String fieldName = field.getName();
                    Object fieldValue = null;
                    try {
                        fieldValue = field.get(pointDataJsonExportVO);
                        if (Objects.isNull(fieldValue)) {
                            rowData.add(null);
                            continue;
                        }
                        if ("pointData".equals(fieldName)) {
                            List<PointInstrumentModelJsonVO> modelList = JSONObject.parseArray(item.getPointData(), PointInstrumentModelJsonVO.class);
                            Map<String, BigDecimal> jsonMap = modelList.stream()
                                    .collect(Collectors.toMap(
                                            PointInstrumentModelJsonVO::getThingIdentity,
                                            model -> model.getThingValue() != null ? model.getThingValue() : BigDecimal.ZERO,
                                            (existingValue, newValue) -> {
                                                // 合并策略：保留现有值或新值
                                                return existingValue;
                                            }
                                    ));
                            headList.forEach(head -> {
                                if (jsonMap.containsKey(head.getFieldName())) {
                                    rowData.add(String.valueOf(jsonMap.get(head.getFieldName())));
                                } else {
                                    rowData.add(null);
                                }
                            });
                        }
                        if ("dataType".equals(fieldName)) {
                            fieldValue = DataTypeEnum.getNameByCode((Integer) fieldValue);
                            rowData.add(String.valueOf(fieldValue));
                        }
                        if ("dataStatus".equals(fieldName)) {
                            fieldValue = DataStatusEnum.getNameByCode((Integer) fieldValue);
                            rowData.add(String.valueOf(fieldValue));
                        }
                        if ("pointTime".equals(fieldName)) {
                            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
                            String format = dateTimeFormatter.format((LocalDateTime) fieldValue);
                            rowData.add(format);
                        }
                        if ("reviewStatus".equals(fieldName)) {
                            fieldValue = ReviewStatusEnum.getNameByCode((Integer) fieldValue);
                            rowData.add(String.valueOf(fieldValue));
                        }
                        if ("reviewName".equals(fieldName) || "pointCode".equals(fieldName)) {
                            rowData.add(String.valueOf(fieldValue));
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
                dataList.add(rowData);
            });
        }

        return dataList;
    }

    @Override
    @Transactional
    public void importPointDataJson(List<PointDataJsonSaveReqVO> createReqVOList) {
        List<PointDataDO> pointDataDOList = new ArrayList<>();
        List<PointDataJsonDO> jsonDOList = new ArrayList<>();
        Map<Long, InstrumentModelDO> instrumentModelDOMap = new HashMap<>();
        createReqVOList.forEach(createReqVO -> {
            //插入测点分量数据
            List<PointInstrumentModelJsonVO> modelBOList = JSONObject.parseArray(createReqVO.getPointData(), PointInstrumentModelJsonVO.class);
            modelBOList.forEach(item -> {
                if (!instrumentModelDOMap.containsKey(item.getInstrumentModelId())) {
                    InstrumentModelDO instrumentModel = instrumentModelService.getInstrumentModel(item.getInstrumentModelId());
                    if (Objects.isNull(instrumentModel)) {
                        throw exception(INSTRUMENT_MODEL_NOT_EXISTS_BY_ID);
                    }
                    instrumentModelDOMap.put(item.getInstrumentModelId(), instrumentModel);
                }
                InstrumentModelDO instrumentModel = instrumentModelDOMap.get(item.getInstrumentModelId());
                PointDataDO pointData = pointDataService.createPointData(instrumentModel, createReqVO.getPointId(),
                        createReqVO.getPointTime(), item.getThingValue(), DataTypeEnum.MANUAL.getType());
                pointDataDOList.add(pointData);
            });

            // 插入
            PointDataJsonDO pointDataJson = BeanUtils.toBean(createReqVO, PointDataJsonDO.class);
            pointDataJson.setDataType(DataTypeEnum.MANUAL.getType());
            //状态
            setPointJsonStatus(pointDataDOList, pointDataJson);
            pointDataJson.setCreator(createReqVO.getUserId());
            pointDataJson.setUpdater(createReqVO.getUserId());
            jsonDOList.add(pointDataJson);
        });
        pointDataService.insertBatch(pointDataDOList);
        pointDataJsonMapper.insertBatch(jsonDOList);
    }

    @Override
    public void updatePointDataJson(PointDataJsonSaveReqVO updateReqVO) {
        // 校验存在
//        PointDataJsonDO pointDataJsonDO = validatePointDataJsonExists(updateReqVO.getId());
//        LocalDateTime pointTime = pointDataJsonDO.getPointTime();
//        //删除旧数据
//        pointDataService.deleteByPointIdAndPointTime(updateReqVO.getPointId(), pointTime);
//
//        //新增分量数据
//        List<PointDataDO> pointDataDOList = batchPointData(updateReqVO);
//
//        // 更新
//        PointDataJsonDO updateObj = BeanUtils.toBean(updateReqVO, PointDataJsonDO.class);
//        //状态
//        setPointJsonStatus(pointDataDOList, updateObj);
//        pointDataJsonMapper.updateById(updateObj);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        delByPointTime(updateReqVO.getProjectId().toString(),updateReqVO.getInstrumentId().toString(), updateReqVO.getPointId().toString(), updateReqVO.getDataType(),updateReqVO.getPointTime().format(formatter) , updateReqVO.getPointTime().format(formatter));
        insertPointData(updateReqVO);
    }

    @Override
    public void reviewSave(PointDataJsonReviewVO updateReqVO) {
        PointDataJsonDO pointDataJsonDO = BeanUtils.toBean(updateReqVO, PointDataJsonDO.class);
        BeanUtil.copyProperties(updateReqVO, pointDataJsonDO);
        //确认保存后，更新审核人为最新的修改人
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
        pointDataJsonDO.setReviewer(String.valueOf(loginUserId));
        pointDataJsonDO.setReviewName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
        Long pointTime=pointDataJsonDO.getPointTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        if(updateReqVO.getDataType()==1){
            damPointDataMapper.insertReview1(pointTime,pointDataJsonDO);
        }else {
            damPointDataMapper.insertReview2(pointTime,pointDataJsonDO);
        }

    }

    @Override
    public void deletePointDataJson(PointDataJsonSaveReqVO updateReqVO) {
//        // 校验存在
//        PointDataJsonDO pointDataJsonDO = validatePointDataJsonExists(id);
//        LocalDateTime pointTime = pointDataJsonDO.getPointTime();
//        // 删除
//        pointDataJsonMapper.deleteById(id);
//        //删除分量数据
//        pointDataService.deleteByPointIdAndPointTime(pointDataJsonDO.getPointId(), pointTime);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        delByPointTime(updateReqVO.getProjectId().toString(),updateReqVO.getInstrumentId().toString(), updateReqVO.getPointId().toString(), updateReqVO.getDataType(),updateReqVO.getPointTime().format(formatter) , updateReqVO.getPointTime().format(formatter));
    }

    private PointDataJsonDO validatePointDataJsonExists(PointDataJsonSaveReqVO updateReqVO) {
        PointDataJsonDO pointDataJsonDO = BeanUtils.toBean(updateReqVO, PointDataJsonDO.class);
        if(pointDataJsonDO!=null){
            return pointDataJsonDO;
        }else {
            throw exception(POINT_DATA_JSON_NOT_EXISTS);
        }
    }

    @Override
    public PointDataJsonRespVO getPointDataJson(PointDataJsonSaveReqVO updateReqVO) {
        PointDataJsonDO pointDataJsonDO = validatePointDataJsonExists(updateReqVO);
        PointDataJsonRespVO pointDataJsonRespVO = new PointDataJsonRespVO();
        BeanUtil.copyProperties(pointDataJsonDO, pointDataJsonRespVO);
        //测点信息
        PointDO point = pointService.getPoint(pointDataJsonDO.getPointId());
        //仪器类型
        InstrumentDO instrument = instrumentService.getInstrument(point.getInstrumentId());
        //录入人
        Long loginUserId = Long.valueOf(pointDataJsonDO.getCreator());
        AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
        pointDataJsonRespVO.setPointCode(point.getPointCode());
        pointDataJsonRespVO.setInstrumentName(Objects.nonNull(instrument) ? instrument.getInstrumentName() : null);
        pointDataJsonRespVO.setCreatorName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
        return pointDataJsonRespVO;
    }

    @Override
    public PageResult<PointDataJsonRespVO> getPointDataJsonPage(PointDataJsonPageReqVO pageReqVO) {
//        //默认查询最新数据当前年份所有数据
//        if (Objects.isNull(pageReqVO.getPointTime())) {
//            PointDataJsonDO lastPointDataJson = getLastPointDataJson(pageReqVO.getPointId(), null);
//            if (lastPointDataJson != null) {
//                LocalDateTime[] newDates = new LocalDateTime[2];
//                newDates[0] = lastPointDataJson.getPointTime().withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
//                newDates[1] = lastPointDataJson.getPointTime();
//                pageReqVO.setPointTime(newDates);
//            }
//        }
            try {
                PointBO pointBO = pointService.getPointBO(pageReqVO.getPointId());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                // 修正分页参数计算
                Integer pageNo = pageReqVO.getPageNo();
                Integer pageSize = pageReqVO.getPageSize();
                Integer offset = (pageNo - 1) * pageSize;
                Integer limit = pageSize;
                pageReqVO.setPageNo( offset);
                pageReqVO.setPageSize( limit);

                PointDataTRespVO pointDataTRespVO = BeanUtils.toBean(pageReqVO, PointDataTRespVO.class);
                pointDataTRespVO.setInstrumentId(pointBO.getInstrumentId());
                if (pageReqVO.getPointTime() != null) {
                    pointDataTRespVO.setStartPointTime(pageReqVO.getPointTime()[0].format(formatter));
                    pointDataTRespVO.setEndPointTime(pageReqVO.getPointTime()[1].format(formatter));
                }
                pointDataTRespVO.setPointTime(null);
                pointDataTRespVO.setPageNo(null);
                pointDataTRespVO.setPageSize(null);

                List<Map> list = new ArrayList<>();
                int batchSize = 1000;
                int batchOffset = 0;
                List<Map> batchList;
                pointDataTRespVO.setOffset(batchOffset);
                pointDataTRespVO.setLimit(batchSize);
                batchList = damPointDataMapper.selectPointDateJsonV1(pointDataTRespVO);
                if (CollUtil.isNotEmpty(batchList)) {
                    list.addAll(batchList);
                    batchOffset += batchSize;
                }

                List<PointDataJsonDO> pointDataJsonDOList = mapToJsonDO(pageReqVO.getProjectId(),
                        pointDataTRespVO.getInstrumentId(), pointDataTRespVO.getPointId(), list);

                // 添加审核状态过滤
                if (CollectionUtil.isNotEmpty(pageReqVO.getReviewStatusList())) {
                    pointDataJsonDOList = pointDataJsonDOList.stream()
                            .filter(item -> pageReqVO.getReviewStatusList().contains(item.getReviewStatus()))
                            .collect(Collectors.toList());
                }
                // 添加数据状态过滤
                if (CollectionUtil.isNotEmpty(pageReqVO.getDataStatusList())) {
                    pointDataJsonDOList = pointDataJsonDOList.stream()
                            .filter(item -> pageReqVO.getDataStatusList().contains(item.getDataStatus()))
                            .collect(Collectors.toList());
                }

                // 分页处理
                Long total = (long) pointDataJsonDOList.size();
                List<PointDataJsonDO> pageData;
                if (pageSize == PageParam.PAGE_SIZE_NONE) {
                    pageData = pointDataJsonDOList;
                } else {
                    int fromIndex = Math.min(offset, pointDataJsonDOList.size());
                    int toIndex = Math.min(fromIndex + pageSize, pointDataJsonDOList.size());
                    pageData = pointDataJsonDOList.subList(fromIndex, toIndex);
                }

                PageResult<PointDataJsonRespVO> result = new PageResult<>();
                result.setList(BeanUtils.toBean(pageData, PointDataJsonRespVO.class));
                result.setTotal(total);

                Map<String, String> nameMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(result.getList()) && pointBO != null) {
                    result.getList().forEach(item -> {
                        String creator = item.getCreator();
                        if (creator != null && !nameMap.containsKey(creator) && !"null".equals(creator)) {
                            AdminUserRespDTO userRespDTO = adminUserApi.getUser(Long.valueOf(creator)).getData();
                            String creatorName = userRespDTO != null ? userRespDTO.getName() : null;
                            nameMap.put(creator, creatorName);
                        }

                        item.setPointCode(pointBO.getPointCode());
                        if (item.getPointData() != null) {
                            List<PointInstrumentModelJsonVO> modelList = JSONObject.parseArray(item.getPointData(), PointInstrumentModelJsonVO.class);
                            List<Map<String, Object>> resultMapList = modelList.stream()
                                    .map(vo -> {
                                        Map<String, Object> map = new HashMap<>();
                                        map.put(vo.getThingIdentity(), vo.getThingValue());
                                        map.put("dataStatus", vo.getDataStatus());
                                        return map;
                                    })
                                    .collect(Collectors.toList());

                            item.setPointDataValue(JSONObject.toJSONString(resultMapList));
                            item.setCreatorName(nameMap.get(creator));
                        }
                    });
                }
                return result;
            } catch (Exception e) {
                log.error("分页查询测点数据异常", e);
                return new PageResult<>(Collections.emptyList(), 0L);
            }
    }
    @Override
    public Long countPointDataJsonByProjectIdAndDataType(Long projectId, Integer dataType, Date startTime) {
//        根据项目id获得所有测点id
        long count = 0;
        LocalDateTime startOfDay = null;
        LocalDateTime endOfDay = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<PointDO> pointDOS = pointMapper.selectList(new LambdaQueryWrapper<PointDO>()
                .eq(PointDO::getProjectId, projectId));
        if (startTime != null) {
             startOfDay = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay();
             endOfDay = startOfDay.plusDays(1).minusNanos(1);
        }
        for (PointDO pointDO : pointDOS) {
            PointDataTRespVO reqVO = new PointDataTRespVO();
            reqVO.setProjectId(projectId);
            reqVO.setPointId(pointDO.getId());
            reqVO.setDataType(dataType);
            reqVO.setInstrumentId(pointDO.getInstrumentId());
            if (startTime != null) {
                reqVO.setStartPointTime(startOfDay.format(formatter));
                reqVO.setEndPointTime(endOfDay.format(formatter));
                reqVO.setPointTime(null);
            }
            Long jsonCount = null;
            try {
                jsonCount = damPointDataMapper.selectPointDateJsonCount(reqVO);
            }catch (Exception e){

            }
            if (jsonCount != null) {
                count += jsonCount;
            }
        }
//        Long count = pointDataJsonMapper.selectCount(new LambdaQueryWrapperX<PointDataJsonDO>()
//                .eq(PointDataJsonDO::getProjectId, projectId)
//                .eqIfPresent(PointDataJsonDO::getDataType, dataType)
//                .geIfPresent(PointDataJsonDO::getPointTime, startTime));
        return Objects.isNull(count) ? 0L : count;
    }

    @Override
    public List<PointDataInstrumentBO> findPointDataInstrumentBOList(Long projectId, List<Long> instrumentIdList) {
        QueryWrapper<PointDataJsonDO> wrapper = new QueryWrapper<>();
        wrapper.groupBy("instrument_id");
        wrapper.select("instrument_id as instrumentId", "count(1) as pointDataCount", "min(point_time) as pointTimeFirst", "max(point_time) as pointTimeRecent");
        wrapper.eq("project_id", projectId);
        List<Map<String, Object>> instrumentList = pointDataJsonMapper.selectMaps(wrapper);
        List<PointDataInstrumentBO> instrumentBOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(instrumentList)) {
            return instrumentBOList;
        }

        instrumentList.forEach(item -> {
            PointDataInstrumentBO instrumentBO = new PointDataInstrumentBO();
            instrumentBO.setInstrumentId((Long) item.get("instrumentId"));
            instrumentBO.setPointDataCount((Long) item.get("pointDataCount"));
            instrumentBO.setPointTimeFirst((LocalDateTime) item.get("pointTimeFirst"));
            instrumentBO.setPointTimeRecent((LocalDateTime) item.get("pointTimeRecent"));
            instrumentBOList.add(instrumentBO);
        });
        return instrumentBOList;
    }

    @Override
    public PointDataInstrumentBO findPointDataInstrumentBO(Long projectId, Long instrumentId) {
        PointDataTRespVO pointDataTRespVO = new PointDataTRespVO();
        pointDataTRespVO.setProjectId(projectId);
        pointDataTRespVO.setInstrumentId(instrumentId);
        List<Map> list;
        try {
            list = damPointDataMapper.selectInstrument(pointDataTRespVO);
        } catch (Exception e) {
            return null;
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
            // 取第一条和最后一条的 pointTime
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
        String lastPointTimeStr = list.get(0).get("ts").toString();
        String firstPointTimeStr = list.get(list.size() - 1).get("ts").toString();
        LocalDateTime pointTimeFirst = LocalDateTime.parse(firstPointTimeStr, formatter);
        LocalDateTime pointTimeRecent = LocalDateTime.parse(lastPointTimeStr, formatter);
        PointDataInstrumentBO instrumentBO = new PointDataInstrumentBO();
        instrumentBO.setInstrumentId(instrumentId);
        instrumentBO.setPointDataCount(Long.valueOf(list.size()));
        instrumentBO.setPointTimeFirst(pointTimeFirst);
        instrumentBO.setPointTimeRecent(pointTimeRecent);
        return instrumentBO;
    }

    @Override
    public void recalculate(PointDataJsonSaveReqVO createReqVO) {
        PointDataJsonDO pointDataJsonDO = validatePointDataJsonExists(createReqVO);
        pointDataService.recalculate(pointDataJsonDO);
    }

    @Override
    public String batchCalculate(PointDataJsonPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PointDataJsonRespVO> list = getPointDataJsonPage(pageReqVO).getList();
        StringBuilder errorMsg = new StringBuilder();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                try {
                    pointDataService.recalculate(BeanUtils.toBean(item, PointDataJsonDO.class));
                } catch (ServiceException e) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
                    if (errorMsg.length() > 0) {
                        errorMsg.append("、");
                    }
                    errorMsg.append(item.getPointTime().format(formatter));
                }
            });
        }
        if (errorMsg.length() > 1) {
            errorMsg.append("监测数据未匹配到有效计算公式，请检查计算公式后重试");
        }
        return errorMsg.toString();
    }

    @Override
    public PointInformationVO getInformationByPointId(Long pointId) {
        //测量分量列表
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(pointId);
        //测点信息
        PointDO point = pointService.getPoint(pointId);
        //仪器类型
        InstrumentDO instrument = instrumentService.getInstrument(point.getInstrumentId());
        //录入人
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();

        PointInformationVO pointInformationVO = new PointInformationVO();
        pointInformationVO.setPointId(pointId);
        pointInformationVO.setPointCode(point.getPointCode());
        pointInformationVO.setInstrumentId(point.getInstrumentId());
        pointInformationVO.setInstrumentName(Objects.nonNull(instrument) ? instrument.getInstrumentName() : null);
        pointInformationVO.setCreator(String.valueOf(loginUserId));
        pointInformationVO.setCreatorName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
        pointInformationVO.setInstrumentModelDOList(instrumentModelList);
        return pointInformationVO;
    }

    @Override
    @Transactional
    public List<PointInstrumentModelJsonVO> calculatePointFormula(PointDataJsonSaveReqVO createReqVO) {
        List<PointInstrumentModelJsonVO> modelBOList = JSONObject.parseArray(createReqVO.getPointData(), PointInstrumentModelJsonVO.class);
        Map<String, Object> modelMap = modelBOList.stream().collect(Collectors.toMap(PointInstrumentModelJsonVO::getThingIdentity, PointInstrumentModelJsonVO::getThingValue));
        List<PointDataDO> pointDataDOList = pointDataService.calculatePointFormula(createReqVO.getPointId(), createReqVO.getPointTime(), modelMap, DataTypeEnum.MANUAL.getType());
        Map<Long, BigDecimal> resultValueMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(pointDataDOList)) {
            pointDataDOList.forEach(item -> {
                resultValueMap.put(item.getInstrumentModelId(), item.getThingValue());
            });
        }

//        阿斯顿
        //所有中间值和成果值都要显示计算结果
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(createReqVO.getPointId());
        List<InstrumentModelDO> modelDOList = instrumentModelList.stream().filter(item -> !item.getThingType().equals(InstrumentThingTypeEnum.ORIGIN.getType())).toList();
        for (InstrumentModelDO model : modelDOList) {
            if (!resultValueMap.containsKey(model.getId())) {
                throw exception(POINT_DATA_MODEL_NO_FORMULA, model.getThingName());
            }
            if (Objects.isNull(resultValueMap.get(model.getId()))) {
                throw exception(POINT_DATA_MODEL_FORMULA_ERROR, model.getThingName());
            }
        }
        return BeanUtils.toBean(pointDataDOList, PointInstrumentModelJsonVO.class);
    }

    @Override
    public List<PointDataDO> importCalculatePointFormula(PointDataJsonSaveReqVO createReqVO) {
        List<PointInstrumentModelJsonVO> modelBOList = JSONObject.parseArray(createReqVO.getPointData(), PointInstrumentModelJsonVO.class);
        Map<String, Object> modelMap = modelBOList.stream()
                .filter(vo -> Objects.nonNull(vo.getThingValue())).collect(Collectors.toMap(PointInstrumentModelJsonVO::getThingIdentity, PointInstrumentModelJsonVO::getThingValue));
        List<PointDataDO> pointDataDOList = pointDataService.calculatePointFormula(createReqVO.getPointId(), createReqVO.getPointTime(), modelMap, DataTypeEnum.MANUAL.getType());
        return pointDataDOList;
    }

    @Override
    public void updateByPointIdAndTime(Long pointId, LocalDateTime startTime, LocalDateTime endTime, Integer dataType, boolean isDelete, Long importId) {
        if (isDelete) {
            pointDataJsonMapper.batchUpdateDelete(pointId, startTime, endTime, dataType, importId);
            pointDataMapper.batchUpdateDelete(pointId, startTime, endTime, dataType, importId);
        } else {
            //回滚删除的数据
            pointDataJsonMapper.batchUpdateRecover(pointId, startTime, endTime, dataType, importId);
            pointDataMapper.batchUpdateRecover(pointId, startTime, endTime, dataType, importId);
        }
    }
    @Resource
    private InstrumentModelMapper instrumentModelMapper;
    @Override
    public PointDataJsonDO getLastPointDataJson(Long projectId, Long instrumentId, Long pointId, Integer dataType) {
        try {
            Map map=damPointDataMapper.getLastPointDataJson(String.valueOf(instrumentId), String.valueOf(pointId), String.valueOf(dataType));
            PointDataJsonDO pointDataJsonDO = new PointDataJsonDO();
            pointDataJsonDO.setPointId(pointId);
            pointDataJsonDO.setInstrumentId(instrumentId);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
            pointDataJsonDO.setPointTime(map.get("ts")==null?null:LocalDateTime.parse(map.get("ts").toString(),formatter));
            List<InstrumentModelDO> instrumentModels = instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>()
                    .eq(InstrumentModelDO::getInstrumentId, instrumentId)
                    .eq(InstrumentModelDO::getProjectId, projectId)
            ) ;
            String pointDate="[";
            for(InstrumentModelDO instrumentModel:instrumentModels){
                pointDate=pointDate+"{";
                pointDate=pointDate+"\"thingName\":\""+instrumentModel.getThingName()+"\",\"thingValue\":"+map.get(instrumentModel.getThingIdentity())+",";
                pointDate=pointDate+"\"thingIdentity\":\""+instrumentModel.getThingIdentity()+"\",\"instrumentModelId\":\""+instrumentModel.getId()+"\"";
                pointDate=pointDate+"},";
            }
            pointDate=pointDate.substring(0,pointDate.length()-1);
            pointDate=pointDate+"]";
            pointDataJsonDO.setPointData(pointDate);
            pointDataJsonDO.setDataType(dataType);
            pointDataJsonDO.setDataStatus(map.get("data_status")== null ? null:Integer.valueOf(String.valueOf(map.get("data_status"))));
            pointDataJsonDO.setReviewStatus(map.get("review_status")== null ? null :Integer.valueOf(String.valueOf(map.get("review_status"))));
            pointDataJsonDO.setReviewer(map.get("reviewer")==null ? null :String.valueOf(map.get("reviewer")));
            pointDataJsonDO.setReviewName(map.get("review_name")==null ? null :String.valueOf(map.get("review_name")));
            return pointDataJsonDO;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public PointDataJsonReviewVO getReviewPointDataJson( PointDataJsonSaveReqVO createReqVO) {
        PointDataJsonDO pointDataJsonDO = validatePointDataJsonExists(createReqVO);
        PointDataJsonReviewVO pointDataJsonReviewVO = new PointDataJsonReviewVO();
        BeanUtil.copyProperties(pointDataJsonDO, pointDataJsonReviewVO);
        if (Objects.isNull(pointDataJsonDO.getReviewer())||pointDataJsonDO.getReviewer().equals("")) {
            Long loginUserId = WebFrameworkUtils.getLoginUserId();
            AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
            pointDataJsonReviewVO.setReviewer(String.valueOf(loginUserId));
            pointDataJsonReviewVO.setReviewName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
        }
        return pointDataJsonReviewVO;
    }

    @NotNull
    private List<PointDataDO> batchPointData(PointDataJsonSaveReqVO createReqVO) {
        //插入测点分量数据
        List<PointDataDO> pointDataDOList = new ArrayList<>();
        List<PointInstrumentModelJsonVO> modelBOList = JSONObject.parseArray(createReqVO.getPointData(), PointInstrumentModelJsonVO.class);
        modelBOList.forEach(item -> {
            InstrumentModelDO instrumentModel = instrumentModelService.getInstrumentModel(item.getInstrumentModelId());
            if (Objects.isNull(instrumentModel)) {
                throw exception(INSTRUMENT_MODEL_NOT_EXISTS_BY_ID);
            }
            PointDataDO pointData = pointDataService.createPointData(instrumentModel, createReqVO.getPointId(),
                    createReqVO.getPointTime(), item.getThingValue(), DataTypeEnum.MANUAL.getType());
            pointDataDOList.add(pointData);
        });
        pointDataService.insertBatch(pointDataDOList);
        return pointDataDOList;
    }

    @Override
    public void insertPointData(PointDataJsonSaveReqVO createReqVO) {
        Integer status = DataStatusEnum.UNDETERMINED.getType();
        pointDataService.defineDevicePropertyData(createReqVO.getProjectId(), createReqVO.getInstrumentId());
        List<PointInstrumentModelJsonVO> modelBOList = JSONObject.parseArray(createReqVO.getPointData(), PointInstrumentModelJsonVO.class);
        Map<String, BigDecimal> pointDataMap = modelBOList.stream().collect(
                Collectors.toMap(PointInstrumentModelJsonVO::getThingIdentity, PointInstrumentModelJsonVO::getThingValue));
        Map<String, String> pointDataMap1=pointDataMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().toString()));
        PointDataTDDO pointData = BeanUtils.toBean(createReqVO, PointDataTDDO.class);
        pointData.setPointTime(createReqVO.getPointTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        pointData.setCreator(createReqVO.getUserId());
        if(pointData.getCreator()==null){
            Long loginUserId = WebFrameworkUtils.getLoginUserId();
            pointData.setCreator(String.valueOf(loginUserId));
        }
        pointData.setCreateTime(LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        PointEvaluateReqVO pointEvaluateReqVO = new PointEvaluateReqVO();
        pointEvaluateReqVO.setPointIdList(new ArrayList<>(Collections.singletonList(createReqVO.getPointId())));
        pointEvaluateReqVO.setProjectId(createReqVO.getProjectId());
        List<PointEvaluatePageRespVO> evaluatePageRespVOList = pointEvaluateMapper.selectList1(pointEvaluateReqVO);
//        比较判定状态
        if (evaluatePageRespVOList.size() > 0) {
            // 初始化数据状态为正常
            int finalStatus = DataStatusEnum.NORMAL.getType();

            List<Map> componentDataList = JSON.parseArray(createReqVO.getPointData(), Map.class);
            for (Map<String, Object> componentData : componentDataList) {
                BigDecimal value = new BigDecimal(componentData.get("thingValue").toString());
                Long instrumentModelId = Long.valueOf(componentData.get("instrumentModelId").toString());

                // 查找对应的评价指标
                PointEvaluatePageRespVO evaluate = evaluatePageRespVOList.stream()
                        .filter(e -> e.getInstrumentModelId().equals(instrumentModelId))
                        .findFirst()
                        .orElse(null);

                if (evaluate != null) {
                    // 单个分量数据状态判定
                    int componentStatus = DataStatusEnum.NORMAL.getType();

                    if (value.compareTo(new BigDecimal(evaluate.getAbnormalUp())) > 0 ||
                            value.compareTo(new BigDecimal(evaluate.getAbnormalDown())) < 0) {
                        componentStatus = DataStatusEnum.ERROR_DATA.getType();
                    } else if (value.compareTo(new BigDecimal(evaluate.getWaringUp())) > 0 ||
                            value.compareTo(new BigDecimal(evaluate.getWaringDown())) < 0) {
                        componentStatus = DataStatusEnum.ANOMALOUS.getType();
                    }

                    // 综合判定一组数据状态
                    if (componentStatus == DataStatusEnum.ERROR_DATA.getType()) {
                        finalStatus = DataStatusEnum.ERROR_DATA.getType();
                    } else if (componentStatus == DataStatusEnum.ANOMALOUS.getType() &&
                            finalStatus != DataStatusEnum.ERROR_DATA.getType()) {
                        finalStatus = DataStatusEnum.ANOMALOUS.getType();
                    }
                }
            }
            status = finalStatus;
        }
        pointData.setDataStatus(status);
        if(pointData.getDataType()==1){
             damPointDataMapper.insertType1(pointData, pointDataMap1);
        }else{
            damPointDataMapper.insertType2(pointData, pointDataMap1);
        }
    }

    @Override
    public void insertPointDataUpload(PointDataJsonSaveReqVO createReqVO) {

        List<PointInstrumentModelJsonVO> modelBOList = JSONObject.parseArray(createReqVO.getPointData(), PointInstrumentModelJsonVO.class);
        Map<String, BigDecimal> pointDataMap = modelBOList.stream().collect(
                Collectors.toMap(PointInstrumentModelJsonVO::getThingIdentity, PointInstrumentModelJsonVO::getThingValue));
        Map<String, String> pointDataMap1=pointDataMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().toString()));
        PointDataTDDO pointData = BeanUtils.toBean(createReqVO, PointDataTDDO.class);
        pointData.setPointTime(createReqVO.getPointTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        pointData.setCreator(createReqVO.getUserId());
        if(pointData.getCreator()==null){
            Long loginUserId = WebFrameworkUtils.getLoginUserId();
            pointData.setCreator(String.valueOf(loginUserId));
        }
        pointData.setCreateTime(LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        PointEvaluateReqVO pointEvaluateReqVO = new PointEvaluateReqVO();
        pointEvaluateReqVO.setPointIdList(new ArrayList<>(Collections.singletonList(createReqVO.getPointId())));
        pointEvaluateReqVO.setProjectId(createReqVO.getProjectId());
        if(pointData.getDataType()==1){
            damPointDataMapper.insertType1(pointData, pointDataMap1);
        }else{
            damPointDataMapper.insertType2(pointData, pointDataMap1);
        }
    }


    @Override
    public void delByPointTime( String projectId, String instrumentId, String pointId, Integer dataType, String startPointTime, String endPointTime) {
        pointDataService.defineDevicePropertyData(Long.valueOf(projectId), Long.valueOf(instrumentId));
        if(dataType==1){
            damPointDataMapper.delByPointTime1(instrumentId, pointId,startPointTime,endPointTime);
        }else{
            damPointDataMapper.delByPointTime2(instrumentId, pointId,startPointTime,endPointTime);
        }
    }

}