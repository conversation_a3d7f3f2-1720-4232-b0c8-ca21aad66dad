package cn.powerchina.bjy.link.dam.controller.admin.projectinfo;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.bo.ProjectInfoBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoSaveReqVO;
import cn.powerchina.bjy.link.dam.service.projectinfo.ProjectInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 项目工程信息")
@RestController
@RequestMapping("/dam/project/info")
@Validated
public class ProjectInfoController {

    @Resource
    private ProjectInfoService projectInfoService;

    @PostMapping("/save")
    @Operation(summary = "保存项目工程信息")
//    @PreAuthorize("@ss.hasPermission('dam:project-info:create')")
    public CommonResult<Long> saveProjectInfo(@Valid @RequestBody ProjectInfoSaveReqVO createReqVO) {
        return success(projectInfoService.saveProjectInfo(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目工程信息")
    @Parameter(name = "projectId", description = "项目id", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:project-info:query')")
    public CommonResult<ProjectInfoRespVO> getProjectInfo(@RequestParam("projectId") Long projectId) {
        ProjectInfoBO projectInfo = projectInfoService.getProjectInfoBO(projectId);
        return success(BeanUtils.toBean(projectInfo, ProjectInfoRespVO.class));
    }

}