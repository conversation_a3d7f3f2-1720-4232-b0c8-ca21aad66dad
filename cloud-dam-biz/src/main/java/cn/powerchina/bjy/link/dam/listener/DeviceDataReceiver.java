package cn.powerchina.bjy.link.dam.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.service.pointdata.PointDataService;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.GROUP_DEVICE_COLLECT_DATA;
import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.TOPIC_DEVICE_COLLECT_DATA;

/**
 * @Description: 接收设备上报的监测数据
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/9
 */
@Slf4j
@Component
@RocketMQMessageListener( topic = TOPIC_DEVICE_COLLECT_DATA, consumerGroup = GROUP_DEVICE_COLLECT_DATA, requestTimeout = 10, consumptionThreadCount = 10)
public class DeviceDataReceiver implements RocketMQListener {

    @Resource
    private PointDataService pointDataService;


    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            DeviceDataTransportModel deviceDataTransportModel = parseMessageBody(messageView);
            if (deviceDataTransportModel == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            log.info("receiver message {}", JSONObject.toJSON(deviceDataTransportModel));
            pointDataService.saveBatchPointData(deviceDataTransportModel);
        } catch (Exception e) {
            log.error("指令下发属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;

    }
    /**
     * 解析消息体为实体类
     */
    private DeviceDataTransportModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceDataTransportModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
