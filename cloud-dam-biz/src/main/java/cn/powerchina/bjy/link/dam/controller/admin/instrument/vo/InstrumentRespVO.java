package cn.powerchina.bjy.link.dam.controller.admin.instrument.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28673")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "4758")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "仪器类型名称", example = "张三")
    @ExcelProperty("仪器类型名称")
    private String instrumentName;

    @Schema(description = "测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它")
    @ExcelProperty("测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它")
    private Integer measurePrinciple;

    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    @ExcelProperty("监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @Schema(description = "计量单位，1：个，2：支，3：套")
    @ExcelProperty("计量单位，1：个，2：支，3：套")
    private Integer measureUnit;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "关联iot产品编码")
    @ExcelProperty("关联iot产品编码")
    private String productCode;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 仪器类型模板id
     */
    @Schema(description = "仪器类型模板id")
    private Long templateId;

}