package cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 物联网平台同步的产品 DO
 *
 * <AUTHOR>
 */
@TableName("dam_iot_product")
@KeySequence("dam_iot_product_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotProductDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 物联网平台库的产品id
     */
    private Long iotId;

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 厂商
     */
    private String firmName;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 节点类型
     * 0:直连; 1:网关; 2:网关子设备
     */
    private Integer nodeType;

    /**
     * 协议编号
     */
    private String protocolCode;

    /**
     * 联网方式
     */
    private String networkMethod;

    /**
     * 数据格式
     */
    private String dataFormat;

    /**
     * 产品启用状态
     * 0:未启用; 1:启用;
     * 默认为1
     */
    private Integer productState;

    /**
     * 秘钥
     */
    private String productSecret;

    /**
     * 物联网平台库的产品创建时间
     */
    private LocalDateTime iotCreateTime;
}
