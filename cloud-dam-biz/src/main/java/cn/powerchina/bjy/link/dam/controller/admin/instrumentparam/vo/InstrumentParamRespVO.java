package cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型-计算参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentParamRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1864")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "28666")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "仪器类型id", example = "9306")
    @ExcelProperty("仪器类型id")
    private Long instrumentId;

    @Schema(description = "参数名称", example = "王五")
    @ExcelProperty("参数名称")
    private String thingName;

    @Schema(description = "参数标识符")
    @ExcelProperty("参数标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String thingUnit;

    @Schema(description = "小数位")
    @ExcelProperty("小数位")
    private Integer decimalLimit;

    @Schema(description = "权重，数字越小越靠前")
    @ExcelProperty("权重，数字越小越靠前")
    private Integer thingWeight;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}