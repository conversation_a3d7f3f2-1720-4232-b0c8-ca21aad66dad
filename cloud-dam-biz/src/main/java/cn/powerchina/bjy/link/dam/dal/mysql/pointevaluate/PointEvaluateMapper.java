package cn.powerchina.bjy.link.dam.dal.mysql.pointevaluate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluatePageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测点评价指标 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointEvaluateMapper extends BaseMapperX<PointEvaluateDO> {

    default PageResult<PointEvaluatePageRespVO> selectPage(PointEvaluatePageReqVO reqVO) {
        MPJLambdaWrapperX<PointEvaluateDO> wrapper = (MPJLambdaWrapperX<PointEvaluateDO>) new MPJLambdaWrapperX<PointEvaluateDO>()
                .selectAll(PointEvaluateDO.class)
                .selectAs(PointDO::getPointCode, PointEvaluatePageRespVO::getPointCode)
                .selectAs(PointDO::getPointState, PointEvaluatePageRespVO::getPointState)
                .leftJoin(PointDO.class, "p", on -> on.eq(PointDO::getId, PointEvaluateDO::getPointId))
                .eqIfExists(PointEvaluateDO::getProjectId, reqVO.getProjectId())
                .in(PointEvaluateDO::getPointId, reqVO.getPointIdList())
                .eqIfExists(PointEvaluateDO::getApplyType, reqVO.getApplyType())
                .eqIfExists(PointDO::getPointState, reqVO.getPointState())
                .likeIfExists(PointDO::getPointCode, reqVO.getPointCode())
                .orderByDesc(PointEvaluateDO::getId);
        return selectJoinPage(reqVO, PointEvaluatePageRespVO.class, wrapper);
    }

    List<PointEvaluatePageRespVO> selectList1(PointEvaluateReqVO reqVO);

    /**
     * 查询分量默认的评价指标（数据库不存在，只页面显示）
     *
     * @param reqVO
     * @return
     */
    List<PointEvaluatePageRespVO> selectModelList(PointEvaluateReqVO reqVO);

    List<PointEvaluatePageRespVO> selectSelectedList(@Param("extremeId") Long extremeId);
}