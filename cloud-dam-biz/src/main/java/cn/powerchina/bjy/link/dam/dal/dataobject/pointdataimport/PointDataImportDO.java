package cn.powerchina.bjy.link.dam.dal.dataobject.pointdataimport;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测点数据导入 DO
 *
 * <AUTHOR>
 */
@TableName("dam_point_data_import")
@KeySequence("dam_point_data_import_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointDataImportDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 数据类型，1：原始值，2：中间值，3：成果值
     */
    private Integer dataType;
    /**
     * 导入类型，1：追加导入，2：覆盖导入
     */
    private Integer importType;
    /**
     * 覆盖开始时间
     */
    private LocalDateTime startTime;
    /**
     * 覆盖结束时间
     */
    private LocalDateTime endTime;
    /**
     * excel文件地址
     */
    private String filePath;

    private String message;
    /**
     * 导入状态
     */
    private Integer importStatus;

}