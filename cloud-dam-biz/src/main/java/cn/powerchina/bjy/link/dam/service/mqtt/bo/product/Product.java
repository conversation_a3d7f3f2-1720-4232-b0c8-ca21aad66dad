package cn.powerchina.bjy.link.dam.service.mqtt.bo.product;

import lombok.Data;

import java.util.List;

/**
 * @Description: 产品
 * @Author: AI Assistant
 * @CreateDate: 2025/7/23
 */
@Data
public class Product {

    /**
     * iot主键
     */
    private Long id;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 厂商
     */
    private String firmName;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 节点类型
     * 0:直连; 1:网关; 2:网关子设备
     */
    private Integer nodeType;

    /**
     * 协议编号
     */
    private String protocolCode;

    /**
     * 联网方式
     */
    private String networkMethod;

    /**
     * 数据格式
     */
    private String dataFormat;

    /**
     * 产品启用状态
     * 0:未启用; 1:启用
     * 默认: 1
     */
    private Integer productState;

    /**
     * 密钥
     */
    private String productSecret;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 产品物模型
     */
    private List<ProductModel> productModelList;

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;
}
