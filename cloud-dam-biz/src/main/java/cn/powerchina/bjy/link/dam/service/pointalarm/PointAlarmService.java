package cn.powerchina.bjy.link.dam.service.pointalarm;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmUpdateReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointalarm.PointAlarmDO;
import jakarta.validation.Valid;

/**
 * 测点报警信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PointAlarmService {

    /**
     * 创建测点报警信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointAlarm(@Valid PointAlarmSaveReqVO createReqVO);

    /**
     * 更新测点报警信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePointAlarm(@Valid PointAlarmUpdateReqVO updateReqVO);

    /**
     * 删除测点报警信息
     *
     * @param id 编号
     */
    void deletePointAlarm(Long id);

    /**
     * 获得测点报警信息
     *
     * @param id 编号
     * @return 测点报警信息
     */
    PointAlarmDO getPointAlarm(Long id);

    /**
     * 获得测点报警信息分页
     *
     * @param pageReqVO 分页查询
     * @return 测点报警信息分页
     */
    PageResult<PointAlarmPageRespVO> getPointAlarmPage(PointAlarmPageReqVO pageReqVO);

}