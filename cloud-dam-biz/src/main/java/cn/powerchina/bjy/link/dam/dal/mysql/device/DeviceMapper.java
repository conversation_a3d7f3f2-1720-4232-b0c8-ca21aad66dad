package cn.powerchina.bjy.link.dam.dal.mysql.device;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.device.bo.DeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointInstrumentModelBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointInstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmPageRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointalarm.PointAlarmDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 大坝设备 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceMapper extends BaseMapperX<DeviceDO> {

    default PageResult<DeviceDO> selectPage(DevicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceDO>()
                .eqIfPresent(DeviceDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(DeviceDO::getParentProductCode, reqVO.getParentProductCode())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .likeIfPresent(DeviceDO::getParentName, reqVO.getParentName())
                .eqIfPresent(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                .eqIfPresent(DeviceDO::getParentSerial, reqVO.getParentSerial())
                .eqIfPresent(DeviceDO::getStationId, reqVO.getStationId())
                .eqIfPresent(DeviceDO::getPointId, reqVO.getPointId())
                .eqIfPresent(DeviceDO::getStrategyId, reqVO.getStrategyId())
                .eqIfPresent(DeviceDO::getNodeType, reqVO.getNodeType())
                .eqIfPresent(DeviceDO::getLinkState, reqVO.getLinkState())
                .eqIfPresent(DeviceDO::getBindType, reqVO.getBindType())
                .eqIfPresent(DeviceDO::getMcuChannel, reqVO.getMcuChannel())
                .orderByDesc(DeviceDO::getId));
    }

    default PageResult<DeviceBO> selectDevicePage(DevicePageReqVO reqVO) {
        MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                .selectAll(DeviceDO.class)
                .leftJoin(PointDO.class, "p", on -> on.eq(DeviceDO::getPointId, PointDO::getId))
                .eqIfExists(DeviceDO::getProjectId, reqVO.getProjectId())
                .likeIfExists(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfExists(DeviceDO::getProductCode, reqVO.getProductCode())
                .eqIfExists(DeviceDO::getParentProductCode, reqVO.getParentProductCode())
                .likeIfExists(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .likeIfExists(DeviceDO::getParentName, reqVO.getParentName())
                .likeIfExists(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                .likeIfExists(DeviceDO::getParentSerial, reqVO.getParentSerial())
                .eqIfExists(DeviceDO::getStationId, reqVO.getStationId())
                .eqIfExists(DeviceDO::getPointId, reqVO.getPointId())
                .eqIfExists(DeviceDO::getStrategyId, reqVO.getStrategyId())
                .eqIfExists(DeviceDO::getNodeType, reqVO.getNodeType())
                .eqIfExists(DeviceDO::getLinkState, reqVO.getLinkState())
                .eqIfExists(DeviceDO::getBindType, reqVO.getBindType())
                .eqIfExists(DeviceDO::getMcuChannel, reqVO.getMcuChannel())
                .eqIfExists(PointDO::getPointCode, reqVO.getPointCode())
                .orderByDesc(DeviceDO::getId);
        return selectJoinPage(reqVO, DeviceBO.class, wrapper);
    }

    default List<String> bindDeviceCodes() {
       return selectList(new LambdaQueryWrapperX<DeviceDO>().select(DeviceDO::getDeviceCode)).stream()
               .map(DeviceDO::getDeviceCode)
               .collect(Collectors.toList());
    }
}