package cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 项目角色信息新增/修改 Request VO")
@Data
public class ProjectRoleSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "角色名称")
    @NotEmpty(message = "角色名称不能为空")
    @Length(max = 32)
    private String roleName;

    @Schema(description = "角色描述")
    @Length(max = 128)
    private String remark;

}