package cn.powerchina.bjy.link.dam.service.mqtt.bo.device;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 分量
 * @Author: AI Assistant
 * @CreateDate: 2025/8/4
 */
@Data
public class InstrumentModel {

    /**
     * 分量id
     */
    private Long instrumentModelId;

    /**
     * 分量名称
     */
    private String thingName;

    /**
     * 分量标识符
     */
    private String thingIdentity;

    /**
     * 分量值
     */
    private BigDecimal thingValue;
}
