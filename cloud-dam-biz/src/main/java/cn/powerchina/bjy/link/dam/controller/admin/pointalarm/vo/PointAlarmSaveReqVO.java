package cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点报警信息新增/修改 Request VO")
@Data
public class PointAlarmSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id",example = "1833761821325131776")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "测点id",example = "1834062943801704448")
    @NotNull(message = "请输入测点id")
    private Long pointId;

    @Schema(description = "监测时间")
    @NotNull(message = "请输入监测时间")
    private LocalDateTime pointTime;

    @Schema(description = "分量id",example = "11")
    @NotNull(message = "请输入测量分量")
    private Long instrumentModelId;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "测值")
    @NotBlank(message = "请输入测值")
    private String pointData;

    @Schema(description = "报警内容")
    @NotBlank(message = "请输入报警内容")
    private String alarmContent;

    @Schema(description = "报警时间")
    @NotNull(message = "请输入报警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "处理状态，0：未处理，1：已处理")
    private Integer solutionStatus;

    @Schema(description = "处理情况")
    private String solutionContent;

    @Schema(description = "处理人id")
    private String solutionUserId;

    @Schema(description = "处理人姓名")
    private String solutionUserName;

    @Schema(description = "处理时间")
    private LocalDateTime solutionTime;

    private Integer alarmType;

}