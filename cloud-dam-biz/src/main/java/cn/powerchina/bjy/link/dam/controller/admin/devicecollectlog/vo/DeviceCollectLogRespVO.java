package cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 网关设备采集日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceCollectLogRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4977")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "10551")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "设备编码")
    @ExcelProperty("设备编码")
    private String deviceCode;

    @Schema(description = "消息id", example = "21541")
    @ExcelProperty("消息id")
    private String msgId;

    @Schema(description = "采集时间id", example = "19503")
    @ExcelProperty("采集时间id")
    private Long collectTimeId;

    @Schema(description = "采集时间")
    @ExcelProperty("采集时间")
    private LocalDateTime collectTime;

    @Schema(description = "采集指令下发时间")
    @ExcelProperty("采集指令下发时间")
    private LocalDateTime sendTime;

    @Schema(description = "采集指令返回时间")
    @ExcelProperty("采集指令返回时间")
    private LocalDateTime backTime;

    @Schema(description = "采集状态，0：采集日志已创建，1：采集指令已下发，2：采集指令已返回", example = "1")
    @ExcelProperty("采集状态，0：采集日志已创建，1：采集指令已下发，2：采集指令已返回")
    private Integer collectStatus;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}