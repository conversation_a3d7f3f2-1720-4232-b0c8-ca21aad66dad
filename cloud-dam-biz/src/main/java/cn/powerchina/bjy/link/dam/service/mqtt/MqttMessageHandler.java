package cn.powerchina.bjy.link.dam.service.mqtt;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.factory.MsgProcessorFactory;
import cn.powerchina.bjy.link.dam.service.mqtt.processor.MsgProcessor;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Description: MQTT消息处理器
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Component
@Slf4j
public class MqttMessageHandler {

    @Autowired
    private MsgProcessorFactory msgProcessorFactory;


    /**
     * 处理接收到的MQTT消息
     *
     * @param message 接收到的消息
     */
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public void handleMessage(Message<?> message) {
        try {
            String topic = (String) message.getHeaders().get("mqtt_receivedTopic");
            String payload = message.getPayload().toString();
            
            log.info("收到MQTT消息 - Topic: {}, Payload: {}", topic, payload);
            
            // 根据不同的topic处理不同的消息
            processMessageByTopic(topic, payload);
            
        } catch (Exception e) {
            log.error("处理MQTT消息时发生错误", e);
        }
    }

    /**
     * 根据主题处理消息
     *
     * @param topic   主题
     * @param payload 消息内容
     */
    private void processMessageByTopic(String topic, String payload) {
        if (topic == null) {
            log.warn("接收到空主题的消息: {}", payload);
            return;
        }

        // 反序列化是为了获取dataType的值
        MqttReceiveData mqttReceiveData = JsonUtils.parseObject(payload, new TypeReference<MqttReceiveData>() {});

        // 根据topic和dataType获取消息处理器
        MsgProcessor msgProcessor = msgProcessorFactory.getProcessor(topic, mqttReceiveData.getDataType());

        // 成功获取到消息处理器后调用处理消息的方法
        if (Objects.nonNull(msgProcessor)) {
            msgProcessor.process(payload);
        }
    }


}
