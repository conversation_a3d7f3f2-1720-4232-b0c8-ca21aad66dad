package cn.powerchina.bjy.link.dam.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 首页信息头部
 * @Author: yhx
 * @CreateDate: 2024/11/22
 */
@Schema(description = "管理后台 - 首页信息头部 Response VO")
@Data
public class IndexHeadRespVO {

    @Schema(description = "监测仪器数量")
    private Long pointDeviceTotal;

    @Schema(description = "监测仪器今日新增数量")
    private Long pointDeviceToday;

    @Schema(description = "监测数据数量")
    private Long pointDataTotal;

    @Schema(description = "监测数据今日新增数量")
    private Long pointDataToday;

    @Schema(description = "自动化设备数量")
    private Long deviceMcuTotal;

    @Schema(description = "自动化设备今日新增数量")
    private Long deviceMcuToday;

    @Schema(description = "在测自动化仪器数量")
    private Long deviceMcuRunTotal;

    @Schema(description = "在测自动化仪器今日新增数量")
    private Long deviceMcuRunToday;

    @Schema(description = "自动化监测数据数量")
    private Long pointDataMcuTotal;

    @Schema(description = "自动化监测数据今日新增数量")
    private Long pointDataMcuToday;
}
