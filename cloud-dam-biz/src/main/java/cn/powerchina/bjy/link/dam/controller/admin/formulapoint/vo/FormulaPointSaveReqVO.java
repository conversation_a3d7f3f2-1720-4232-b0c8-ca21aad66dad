package cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 公式关联测点新增/修改 Request VO")
@Data
public class FormulaPointSaveReqVO {

    @Schema(description = "主键id", example = "15140")
    private Long id;

    @Schema(description = "测点公式id", example = "11278")
    @NotNull(message = "测点公式id不能为空")
    private Long pointFormulaId;

    @Schema(description = "关联测点id", example = "23886")
    @NotNull(message = "关联测点id不能为空")
    private Long pointId;

    @Schema(description = "分量id", example = "26093")
    @NotNull(message = "测量分量id不能为空")
    private Long instrumentModelId;

    @Schema(description = "数据范围，1：全部，2：自动化，3：人工", example = "2")
    @NotNull(message = "数据范围不能为空")
    private Integer applyType;

    @Schema(description = "取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值")
    @NotNull(message = "取值条件不能为空")
    private Integer dataCondition;

    @Schema(description = "数值或1：日统计值，2：月统计值")
    private Integer dataValue;

    @Schema(description = "单位，1：分钟，2：小时，3：天或1：平均值，2：最大值，3：最小值，4：累计值")
    private Integer dataUnit;

}