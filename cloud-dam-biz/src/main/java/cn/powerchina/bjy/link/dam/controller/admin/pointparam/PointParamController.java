package cn.powerchina.bjy.link.dam.controller.admin.pointparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointparam.PointParamDO;
import cn.powerchina.bjy.link.dam.service.pointparam.PointParamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点计算参数")
@RestController
@RequestMapping("/dam/point/param")
@Validated
public class PointParamController {

    @Resource
    private PointParamService pointParamService;

    @PostMapping("/create")
    @Operation(summary = "创建测点计算参数")
    //@PreAuthorize("@ss.hasPermission('dam:point-param:create')")
    public CommonResult<Long> createPointParam(@Valid @RequestBody PointParamSaveReqVO createReqVO) {
        return success(pointParamService.createPointParam(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新测点计算参数")
    //@PreAuthorize("@ss.hasPermission('dam:point-param:update')")
    public CommonResult<Boolean> updatePointParam(@Valid @RequestBody PointParamSaveReqVO updateReqVO) {
        pointParamService.updatePointParam(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测点计算参数")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('dam:point-param:delete')")
    public CommonResult<Boolean> deletePointParam(@RequestParam("id") Long id) {
        pointParamService.deletePointParam(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测点计算参数")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('dam:point-param:query')")
    public CommonResult<PointParamRespVO> getPointParam(@RequestParam("id") Long id) {
        PointParamDO pointParam = pointParamService.getPointParam(id);
        return success(BeanUtils.toBean(pointParam, PointParamRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点计算参数分页")
    //@PreAuthorize("@ss.hasPermission('dam:point-param:query')")
    public CommonResult<PageResult<PointParamPageVO>> getPointParamPage(@Valid PointParamPageReqVO pageReqVO) {
        PageResult<PointParamPageVO> pageResult = pointParamService.getPointParamPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointParamPageVO.class));
    }

    @GetMapping("/table")
    @Operation(summary = "获得测点计算参数表头")
    //@PreAuthorize("@ss.hasPermission('dam:point-param:query')")
    @Parameter(name = "categoryId", description = "工程分类id(仪器类型、分组名称、测点名称)", required = true)
    public CommonResult<List<PointParamTableRespVO>> getPointParamTable(Long categoryId) {
        List<PointParamTableRespVO> pointParamTable = pointParamService.getPointParamTable(categoryId);
        return success(BeanUtils.toBean(pointParamTable, PointParamTableRespVO.class));
    }

    @GetMapping("/instrument")
    @Operation(summary = "根据仪器id获取仪器设备定义的参数列表")
    @Parameter(name = "categoryId", description = "工程分类id(测点id)", required = true)
    //@PreAuthorize("@ss.hasPermission('dam:point-param:query')")
    public CommonResult<List<InstrumentVO>> getPointInstrumentParams(Long categoryId) {
        List<InstrumentParamDO> instrumentParamDOList = pointParamService.getPointInstrumentParams(categoryId);
        return success(BeanUtils.toBean(instrumentParamDOList, InstrumentVO.class));
    }

    @PutMapping("/update-list")
    @Operation(summary = "更新测点计算参数")
    //@PreAuthorize("@ss.hasPermission('dam:point-param:update')")
    public CommonResult<Boolean> updateListPointParam(@Valid @RequestBody List<PointParamSaveReqVO> updateReqVO) {
        pointParamService.updateListPointParam(updateReqVO);
        return success(true);
    }

}