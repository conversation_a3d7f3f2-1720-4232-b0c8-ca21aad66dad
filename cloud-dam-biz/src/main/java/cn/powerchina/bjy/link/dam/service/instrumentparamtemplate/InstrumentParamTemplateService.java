package cn.powerchina.bjy.link.dam.service.instrumentparamtemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparamtemplate.InstrumentParamTemplateDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 仪器类型模板-计算参数 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentParamTemplateService {

    /**
     * 创建仪器类型模板-计算参数
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrumentParamTemplate(@Valid InstrumentParamTemplateSaveReqVO createReqVO);

    /**
     * 更新仪器类型模板-计算参数
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrumentParamTemplate(@Valid InstrumentParamTemplateSaveReqVO updateReqVO);

    /**
     * 删除仪器类型模板-计算参数
     *
     * @param id 编号
     */
    void deleteInstrumentParamTemplate(Long id);

    /**
     * 获得仪器类型模板-计算参数
     *
     * @param id 编号
     * @return 仪器类型模板-计算参数
     */
    InstrumentParamTemplateDO getInstrumentParamTemplate(Long id);

    /**
     * 获得仪器类型模板-计算参数分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器类型模板-计算参数分页
     */
    PageResult<InstrumentParamTemplateDO> getInstrumentParamTemplatePage(InstrumentParamTemplatePageReqVO pageReqVO);

    List<InstrumentParamTemplateDO> getListByInstrumentId(long templateId);
}