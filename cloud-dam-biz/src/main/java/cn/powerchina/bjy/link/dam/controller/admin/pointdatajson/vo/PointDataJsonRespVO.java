package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据json Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataJsonRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "406")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "19338")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id", example = "24279")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "测点编号", example = "24279")
    @ExcelProperty("测点编号")
    private String pointCode;

    @Schema(description = "监测时间")
    @ExcelProperty("监测时间")
    private LocalDateTime pointTime;

    @Schema(description = "仪器类型id", example = "3871")
    @ExcelProperty("仪器类型id")
    private Long instrumentId;

    @Schema(description = "分量值json，包括分量id、分量标识符、分量名称、分量原始值、分量值")
    @ExcelProperty("分量值json，包括分量id、分量标识符、分量名称、分量原始值、分量值")
    private String pointData;

    @Schema(description = "分量值json，分量标识符：分量值；动态分量数据展示")
    private String pointDataValue;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    @ExcelProperty("采集类型(1：自动化采集，2：人工录入）")
    private Integer dataType;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    @ExcelProperty("数据状态(1：正常数据，0：异常数据）")
    private Integer dataStatus;

    @Schema(description = "仪器类型名称")
    private String instrumentName;

    @Schema(description = "录入人id")
    private String creator;

    @Schema(description = "录入人")
    private String creatorName;

    @Schema(description = "录入时间")
    private LocalDateTime createTime;

    @Schema(description = "审核状态")
    private Integer reviewStatus;

    private String reviewer;

    @Schema(description = "审核人")
    private String reviewName;
}