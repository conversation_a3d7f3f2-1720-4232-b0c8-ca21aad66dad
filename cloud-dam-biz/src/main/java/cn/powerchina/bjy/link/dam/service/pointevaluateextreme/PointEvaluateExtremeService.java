package cn.powerchina.bjy.link.dam.service.pointevaluateextreme;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeResultVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremeSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluateextreme.PointEvaluateExtremeDO;
import jakarta.validation.Valid;

/**
 * 测点评价指标极值 Service 接口
 *
 * <AUTHOR>
 */
public interface PointEvaluateExtremeService {

    /**
     * 创建测点评价指标极值
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPointEvaluateExtreme(@Valid PointEvaluateExtremeSaveReqVO createReqVO);

    /**
     * 计算极值
     *
     * @param pointEvaluateExtremeReqVO
     * @return
     */
    PointEvaluateExtremeResultVO calculating(@Valid PointEvaluateExtremeReqVO pointEvaluateExtremeReqVO);

    /**
     * 更新测点评价指标极值
     *
     * @param updateReqVO 更新信息
     */
    void updatePointEvaluateExtreme(@Valid PointEvaluateExtremeSaveReqVO updateReqVO);

    /**
     * 删除测点评价指标极值
     *
     * @param id 编号
     */
    void deletePointEvaluateExtreme(Long id);

    /**
     * 获得测点评价指标极值
     *
     * @param id 编号
     * @return 测点评价指标极值
     */
    PointEvaluateExtremeDO getPointEvaluateExtreme(Long id);

    /**
     * 获得测点评价指标极值分页
     *
     * @param pageReqVO 分页查询
     * @return 测点评价指标极值分页
     */
    PageResult<PointEvaluateExtremeDO> getPointEvaluateExtremePage(PointEvaluateExtremePageReqVO pageReqVO);

}