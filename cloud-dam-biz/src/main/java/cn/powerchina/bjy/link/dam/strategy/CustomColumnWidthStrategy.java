package cn.powerchina.bjy.link.dam.strategy;

import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;

import java.util.Map;

/**
 * easyExcel自定义列宽的策略
 */
public class CustomColumnWidthStrategy extends AbstractColumnWidthStyleStrategy {

    private final Map<Integer, Integer> columnWidthMap;

    public CustomColumnWidthStrategy(Map<Integer, Integer> columnWidthMap) {
        this.columnWidthMap = columnWidthMap;
    }

    @Override
    protected void setColumnWidth(CellWriteHandlerContext context) {
        Integer columnIndex = context.getHeadData().getColumnIndex();
        if (columnWidthMap.containsKey(columnIndex)) {
            int width = columnWidthMap.get(columnIndex);
            context.getCell().getSheet().setColumnWidth(columnIndex, width*256);
        }
    }
}
