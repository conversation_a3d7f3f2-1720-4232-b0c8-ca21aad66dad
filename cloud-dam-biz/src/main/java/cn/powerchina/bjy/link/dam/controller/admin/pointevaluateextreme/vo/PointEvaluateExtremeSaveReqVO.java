package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo;

import cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo.PointEvaluateSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 测点评价指标极值新增/修改 Request VO")
@Data
public class PointEvaluateExtremeSaveReqVO{

//    @Schema(description = "正常最大值")
//    @NotNull(message = "请输入正常最大值")
//    private String waringUp;
//
//    @Schema(description = "正常最小值")
//    @NotNull(message = "请输入正常最小值")
//    private String waringDown;
//
//    @Schema(description = "异常最大值")
//    @NotNull(message = "请输入异常最大值")
//    private String abnormalUp;
//
//    @Schema(description = "异常最小值")
//    @NotNull(message = "请输入异常最小值")
//    private String abnormalDown;
//
//    @Schema(description = "测点评价指标id")
//    @NotNull(message = "测点评价指标id")
//    private Long pointEvaluateId;

    private PointEvaluateExtremeReqVO pointEvaluateExtremeReqVO;

    private PointEvaluateSaveReqVO pointEvaluateSaveReqVO;

    List<PointEvaluateExtremeBatchAppReqVO> batchAppReqVOList;

}