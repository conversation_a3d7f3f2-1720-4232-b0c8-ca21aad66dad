package cn.powerchina.bjy.link.dam.controller.admin.instrument.vo;

import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 仪器类型模板新增/修改 Request VO")
@Data
public class InstrumentSaveListReqVO {
    @Schema(description = "仪器类型模板新增参数")
    private InstrumentSaveReqVO instrumentSaveReqVO;

    @Valid
    @Schema(description = "计算参数List")
    private List<InstrumentParamSaveReqVO> instrumentParamSaveReqVOS;

    @Valid
    @Schema(description = "测量分量List")
    private List<InstrumentModelSaveReqVO> instrumentModelSaveReqVOS;
}