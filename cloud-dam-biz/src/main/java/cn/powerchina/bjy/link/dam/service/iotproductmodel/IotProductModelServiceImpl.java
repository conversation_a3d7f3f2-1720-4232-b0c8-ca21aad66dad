package cn.powerchina.bjy.link.dam.service.iotproductmodel;

import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproductmodel.IotProductModelDO;
import cn.powerchina.bjy.link.dam.dal.mysql.iotproductmodel.IotProductModelMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 物联网平台同步的产品物模型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IotProductModelServiceImpl implements IotProductModelService {

    @Autowired
    private IotProductModelMapper iotProductModelMapper;

    /**
     * 新增
     * @param iotProductModelDO iot产品物模型
     */
    @Override
    public void createIotProductModel(IotProductModelDO iotProductModelDO) {
        iotProductModelMapper.insert(iotProductModelDO);
    }

    /**
     * 根据物联网平台库的产品id删除
     * @param iotProductIotId 物联网平台库的产品id
     */
    @Override
    public void deleteByIotProductIotId(Long iotProductIotId) {
        iotProductModelMapper.delete(new LambdaQueryWrapperX<IotProductModelDO>().eq(IotProductModelDO::getIotProductIotId, iotProductIotId));
    }

    /**
     * 根据物联网平台库的产品物模型id删除
     * @param iotId 物联网平台库的产品物模型id
     */
    @Override
    public void deleteByIotId(Long iotId) {
        iotProductModelMapper.delete(new LambdaQueryWrapperX<IotProductModelDO>().eq(IotProductModelDO::getIotId, iotId));
    }

    /**
     * 根据物联网平台库的产品物模型id更新
     * @param iotProductModelDO 物联网平台库的产品物模型
     */
    @Override
    public void updateByIotId(IotProductModelDO iotProductModelDO) {
        iotProductModelMapper.update(new LambdaUpdateWrapper<IotProductModelDO>()
                .set(IotProductModelDO::getIotProductIotId, iotProductModelDO.getIotProductIotId())
                .set(IotProductModelDO::getProductCode, iotProductModelDO.getProductCode())
                .set(IotProductModelDO::getThingIdentity, iotProductModelDO.getThingIdentity())
                .set(IotProductModelDO::getThingName, iotProductModelDO.getThingName())
                .set(IotProductModelDO::getThingType, iotProductModelDO.getThingType())
                .set(IotProductModelDO::getDatatype, iotProductModelDO.getDatatype())
                .set(IotProductModelDO::getReadWriteType, iotProductModelDO.getReadWriteType())
                .set(IotProductModelDO::getEventType, iotProductModelDO.getEventType())
                .set(IotProductModelDO::getInputParams, iotProductModelDO.getInputParams())
                .set(IotProductModelDO::getOutputParams, iotProductModelDO.getOutputParams())
                .set(IotProductModelDO::getExtra, iotProductModelDO.getExtra())
                .set(IotProductModelDO::getRemark, iotProductModelDO.getRemark())
                .set(IotProductModelDO::getIotCreateTime, iotProductModelDO.getIotCreateTime())
                .eq(IotProductModelDO::getIotId, iotProductModelDO.getIotId())
        );
    }

    /**
     * 根据物联网平台库的产品物模型id获取
     * @param iotId 物联网平台库的产品物模型id
     * @return 物联网平台库同步的产品物模型
     */
    @Override
    public IotProductModelDO getByIotId(Long iotId) {
        List<IotProductModelDO> iotProductModelDOList = iotProductModelMapper.selectList(new LambdaQueryWrapperX<IotProductModelDO>()
                .eq(IotProductModelDO::getIotId, iotId));
        return iotProductModelDOList.stream().findFirst().orElse(null);
    }

    /**
     * 根据产品编码和物模型类型获取
     * @param productCode 产品编码
     * @param thingType 物模型类型
     * @return 物联网平台同步的产品物模型
     */
    @Override
    public List<IotProductModelDO> listByProductCodeThingType(String productCode, Integer thingType) {
        List<IotProductModelDO> iotProductModelDOList = iotProductModelMapper.selectList(new LambdaQueryWrapperX<IotProductModelDO>()
                .eq(IotProductModelDO::getProductCode, productCode)
                .eq(IotProductModelDO::getThingType, thingType));
        return iotProductModelDOList;
    }
}
