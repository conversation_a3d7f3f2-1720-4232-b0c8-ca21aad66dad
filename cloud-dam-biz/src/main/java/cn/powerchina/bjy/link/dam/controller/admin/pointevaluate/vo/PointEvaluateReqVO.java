package cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 测点评价指标分页 Request VO")
@Data
@ToString(callSuper = true)
public class PointEvaluateReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "报警上限")
    private Integer waringUp;

    @Schema(description = "工程分类id")
    @NotNull(message = "请选择仪器类型或分组或测点")
    private Long categoryId;

    @Schema(description = "测点id", hidden = true)
    private List<Long> pointIdList;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他")
    private Integer pointState;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "1：原始值，2：中间值，3：成果值", defaultValue = "3")
    List<Integer> thingTypeList;

}