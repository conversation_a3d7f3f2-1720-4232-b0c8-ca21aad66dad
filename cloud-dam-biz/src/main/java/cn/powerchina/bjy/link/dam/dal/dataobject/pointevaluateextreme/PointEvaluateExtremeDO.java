package cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluateextreme;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测点评价指标极值 DO
 *
 * <AUTHOR>
 */
@TableName("dam_point_evaluate_extreme")
@KeySequence("dam_point_evaluate_extreme_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointEvaluateExtremeDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间（小于等于当前时间）
     */
    private LocalDateTime endTime;
    /**
     * 采集类型(0：全部，1：自动化采集，2：人工录入）
     */
    private Integer dataType;
    /**
     * 最值法(1-极值百分比浮动，2-极值固定值浮动，3-固定数值)
     */
    private Integer extremeType;
    /**
     * 异常最小值(1-极小值，2-极大值)
     */
    private Integer abnormalDownType;
    /**
     * 异常最小值(1-加，2-减)
     */
    private Integer abnormalDownSymbol;
    /**
     * 异常最小值
     */
    private String abnormalDown;
    /**
     * 正常最小值(1-极小值，2-极大值)
     */
    private Integer waringDownType;
    /**
     * 正常最小值(1-加，2-减)
     */
    private Integer waringDownSymbol;
    /**
     * 正常最小值
     */
    private String waringDown;
    /**
     * 异常最大值(1-极小值，2-极大值)
     */
    private Integer abnormalUpType;
    /**
     * 异常最大值(1-加，2-减)
     */
    private Integer abnormalUpSymbol;
    /**
     * 异常最大值
     */
    private String abnormalUp;
    /**
     * 正常最大值(1-极小值，2-极大值)
     */
    private Integer waringUpType;
    /**
     * 正常最大值(1-加，2-减)
     */
    private Integer waringUpSymbol;
    /**
     * 异常最小值
     */
    private String waringUp;

}