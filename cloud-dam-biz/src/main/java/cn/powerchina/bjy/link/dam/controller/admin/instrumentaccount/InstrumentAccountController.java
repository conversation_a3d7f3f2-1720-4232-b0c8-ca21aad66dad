package cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountPageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentaccount.InstrumentAccountDO;
import cn.powerchina.bjy.link.dam.service.instrumentaccount.InstrumentAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 仪器台账")
@RestController
@RequestMapping("/dam/instrument-account")
@Validated
public class InstrumentAccountController {

    @Resource
    private InstrumentAccountService instrumentAccountService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器台账")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-account:create')")
    public CommonResult<Long> createInstrumentAccount(@Valid @RequestBody InstrumentAccountSaveReqVO createReqVO) {
        return success(instrumentAccountService.createInstrumentAccount(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器台账")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-account:update')")
    public CommonResult<Boolean> updateInstrumentAccount(@Valid @RequestBody InstrumentAccountSaveReqVO updateReqVO) {
        instrumentAccountService.updateInstrumentAccount(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器台账")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('dam:instrument-account:delete')")
    public CommonResult<Boolean> deleteInstrumentAccount(@RequestParam("id") Long id) {
        instrumentAccountService.deleteInstrumentAccount(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器台账")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('dam:instrument-account:query')")
    public CommonResult<InstrumentAccountRespVO> getInstrumentAccount(@RequestParam("id") Long id) {
        InstrumentAccountDO instrumentAccount = instrumentAccountService.getInstrumentAccount(id);
        return success(BeanUtils.toBean(instrumentAccount, InstrumentAccountRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器台账分页")
    //@PreAuthorize("@ss.hasPermission('dam:instrument-account:query')")
    public CommonResult<PageResult<InstrumentAccountPageRespVO>> getInstrumentAccountPage(@Valid InstrumentAccountPageReqVO pageReqVO) {
        PageResult<InstrumentAccountPageRespVO> pageResult = instrumentAccountService.getInstrumentAccountPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentAccountPageRespVO.class));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出仪器台账 Excel")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-account:export')")
//    @OperateLog(type = EXPORT)
//    public void exportInstrumentAccountExcel(@Valid InstrumentAccountPageReqVO pageReqVO,
//                                             HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<InstrumentAccountDO> list = instrumentAccountService.getInstrumentAccountPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "仪器台账.xls", "数据", InstrumentAccountRespVO.class,
//                BeanUtils.toBean(list, InstrumentAccountRespVO.class));
//    }

}