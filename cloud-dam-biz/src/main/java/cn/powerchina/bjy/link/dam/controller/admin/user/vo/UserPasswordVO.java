package cn.powerchina.bjy.link.dam.controller.admin.user.vo;

import cn.powerchina.bjy.cloud.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * @Description: 描述
 * @Author: zhaoqi<PERSON>
 * @CreateDate: 2024/8/30
 */
@Schema(description = "管理后台 - 用户修改密码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UserPasswordVO {
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "旧密码", example = "123456")
    private String oldPassword;

    @Schema(description = "新密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "654321")
    @NotEmpty(message = "新密码不能为空")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String newPassword;

    @Schema(description = "手机号码", example = "")
    @Mobile
    private String mobile;
}
