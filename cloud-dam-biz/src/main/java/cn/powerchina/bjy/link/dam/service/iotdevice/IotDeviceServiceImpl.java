package cn.powerchina.bjy.link.dam.service.iotdevice;

import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice.IotDeviceDO;
import cn.powerchina.bjy.link.dam.dal.mysql.iotdevice.IotDeviceMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 物联网平台同步的设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IotDeviceServiceImpl implements IotDeviceService {

    @Autowired
    private IotDeviceMapper iotDeviceMapper;

    /**
     * 新增
     * @param iotDeviceDO 物联网平台库的设备
     */
    @Override
    public void createIotDevice(IotDeviceDO iotDeviceDO) {
        iotDeviceMapper.insert(iotDeviceDO);
    }

    /**
     * 根据物联网平台库的设备id删除
     * @param iotId 物联网平台库的设备id
     */
    @Override
    public void deleteByIotId(Long iotId) {
        iotDeviceMapper.delete(new LambdaUpdateWrapper<IotDeviceDO>().eq(IotDeviceDO::getIotId, iotId));
    }

    /**
     * 根据物联网平台库的设备id更新
     * @param iotDeviceDO 物联网平台库的设备
     */
    @Override
    public void updateByIotId(IotDeviceDO iotDeviceDO) {
        iotDeviceMapper.update(new LambdaUpdateWrapper<IotDeviceDO>()
                .set(IotDeviceDO::getProductCode, iotDeviceDO.getProductCode())
                .set(IotDeviceDO::getProjectCode, iotDeviceDO.getProjectCode())
                .set(IotDeviceDO::getDeviceName, iotDeviceDO.getDeviceName())
                .set(IotDeviceDO::getDeviceCode, iotDeviceDO.getDeviceCode())
                .set(IotDeviceDO::getParentCode, iotDeviceDO.getParentCode())
                .set(IotDeviceDO::getParentName, iotDeviceDO.getParentName())
                .set(IotDeviceDO::getDeviceSerial, iotDeviceDO.getDeviceSerial())
                .set(IotDeviceDO::getShadow, iotDeviceDO.getShadow())
                .set(IotDeviceDO::getChannelCode, iotDeviceDO.getChannelCode())
                .set(IotDeviceDO::getMcuChannel, iotDeviceDO.getMcuChannel())
                .set(IotDeviceDO::getSlaveId, iotDeviceDO.getSlaveId())
                .set(IotDeviceDO::getDistributeState, iotDeviceDO.getDistributeState())
                .set(IotDeviceDO::getLinkState, iotDeviceDO.getLinkState())
                .set(IotDeviceDO::getRegisterState, iotDeviceDO.getRegisterState())
                .set(IotDeviceDO::getNodeType, iotDeviceDO.getNodeType())
                .set(IotDeviceDO::getExtra, iotDeviceDO.getExtra())
                .set(IotDeviceDO::getLastUpTime, iotDeviceDO.getLastUpTime())
                .set(IotDeviceDO::getEdgeCode, iotDeviceDO.getEdgeCode())
                .set(IotDeviceDO::getDriverCode, iotDeviceDO.getDriverCode())
                .set(IotDeviceDO::getProductName, iotDeviceDO.getProductName())
                .set(IotDeviceDO::getRemark, iotDeviceDO.getRemark())
                .set(IotDeviceDO::getFirmName, iotDeviceDO.getFirmName())
                .set(IotDeviceDO::getProductModel, iotDeviceDO.getProductModel())
                .set(IotDeviceDO::getResourceSpaceId, iotDeviceDO.getResourceSpaceId())
                .set(IotDeviceDO::getSpaceName, iotDeviceDO.getSpaceName())
                .set(IotDeviceDO::getLongitude, iotDeviceDO.getLongitude())
                .set(IotDeviceDO::getLatitude, iotDeviceDO.getLatitude())
                .set(IotDeviceDO::getIotCreateTime, iotDeviceDO.getIotCreateTime())
                .set(IotDeviceDO::getRegisterTime, iotDeviceDO.getRegisterTime())
                .eq(IotDeviceDO::getIotId, iotDeviceDO.getIotId())
        );
    }

    /**
     * 根据设备编码，产品编码更新设备连接状态
     * @param iotDeviceDO 物联网平台库的设备（包含“设备编码”，“产品编码”，“连接状态”以及“最后上线时间”）
     */
    @Override
    public void updateLinkState(IotDeviceDO iotDeviceDO) {
        iotDeviceMapper.update(new LambdaUpdateWrapper<IotDeviceDO>()
                .set(IotDeviceDO::getLinkState, iotDeviceDO.getLinkState())
                .set(IotDeviceDO::getLastUpTime, iotDeviceDO.getLastUpTime())
                .eq(IotDeviceDO::getDeviceCode, iotDeviceDO.getDeviceCode())
                .eq(IotDeviceDO::getProductCode, iotDeviceDO.getProductCode())
        );
    }

    /**
     * 根据物联网平台库的设备id获取
     * @param iotId 物联网平台库的设备id
     * @return 物联网平台同步的设备
     */
    @Override
    public IotDeviceDO getByIotId(Long iotId) {
        List<IotDeviceDO> iotDeviceDOList = iotDeviceMapper.selectList(new LambdaQueryWrapperX<IotDeviceDO>().eq(IotDeviceDO::getIotId, iotId));
        return iotDeviceDOList.stream().findFirst().orElse(null);
    }

    /**
     * 获取所有物联网平台同步的设备
     * @return 物联网平台同步的设备列表
     */
    @Override
    public List<IotDeviceDO> list() {
        return iotDeviceMapper.selectList();
    }

    /**
     * 根据物联网平台的设备id获取
     * @param iotIdList 物联网平台库的设备id列表
     * @return 物联网平台同步的设备列表
     */
    @Override
    public List<IotDeviceDO> listByIotId(List<Long> iotIdList) {
        if (CollectionUtils.isNotEmpty(iotIdList)) {
            List<IotDeviceDO> iotDeviceDOList = iotDeviceMapper.selectList(new LambdaQueryWrapperX<IotDeviceDO>().in(IotDeviceDO::getIotId, iotIdList));
            return iotDeviceDOList;
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据父设备编码获取
     * @param parentCodeList 父设备编码列表
     * @return 物联网平台同步的设备列表
     */
    @Override
    public List<IotDeviceDO> listByParentCode(List<String> parentCodeList) {
        if (CollectionUtils.isNotEmpty(parentCodeList)) {
            List<IotDeviceDO> iotDeviceDOList = iotDeviceMapper.selectList(new LambdaQueryWrapperX<IotDeviceDO>().in(IotDeviceDO::getParentCode, parentCodeList));
            return iotDeviceDOList;
        } else {
            return new ArrayList<>();
        }
    }
}
