package cn.powerchina.bjy.link.dam.util;

import java.text.NumberFormat;
import java.util.Objects;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
public class MyCalculateUtils {

    /**
     * 计算百分比
     *
     * @param count
     * @param total
     * @param section
     * @return
     */
    public static String calculate(Long count, Long total, int section) {
        if (Objects.isNull(count) || Objects.isNull(total) || count <= 0 || total <= 0) {
            return "0";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(section);
        return numberFormat.format((float) count / (float) total * 100) + "";
    }
}
