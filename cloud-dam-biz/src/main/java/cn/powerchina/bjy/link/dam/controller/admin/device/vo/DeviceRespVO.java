package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 大坝设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "项目名称")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "父设备编码")
    @ExcelProperty("父设备编码")
    private String parentCode;

    @Schema(description = "父设备名称")
    @ExcelProperty("父设备名称")
    private String parentName;

    @Schema(description = "父设备产品编码")
    @ExcelProperty("父设备产品编码")
    private String parentProductCode;

    @Schema(description = "父设备产品名称")
    @ExcelProperty("父设备产品名称")
    private String parentProductName;

    @Schema(description = "父设备唯一标识")
    @ExcelProperty("父设备唯一标识")
    private String parentSerial;

    @Schema(description = "设备编码")
    @ExcelProperty("设备编码")
    private String deviceCode;

    @Schema(description = "产品编码")
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "设备名称")
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "设备唯一标识")
    @ExcelProperty("设备唯一标识")
    private String deviceSerial;

    @Schema(description = "最后上线时间")
    @ExcelProperty("最后上线时间")
    private LocalDateTime lastUpTime;

    @Schema(description = "测站id")
    @ExcelProperty("测站id")
    private Long stationId;

    @Schema(description = "所属测站")
    @ExcelProperty("所属测站")
    private String stationName;

    @Schema(description = "测点id")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "测点编号")
    @ExcelProperty("测点编号")
    private String pointCode;

    @Schema(description = "测点名称")
    @ExcelProperty("测点名称")
    private String pointName;

    @Schema(description = "策略id")
    @ExcelProperty("策略id")
    private Long strategyId;

    @Schema(description = "策略名称")
    private String strategyName;

    @Schema(description = "采集方式，1：定点采集，2：间隔采集，3：跨天采集")
    private Integer strategyType;

    @Schema(description = "时间间隔")
    private String timeInterval;

    @Schema(description = "时间点（英文逗号分隔）")
    private String timePoint;

    @Schema(description = "设备电压")
    @ExcelProperty("设备电压")
    private String deviceVoltage;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    @ExcelProperty("节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "连接状态（0-离线；1-在线；）")
    @ExcelProperty("连接状态（0-离线；1-在线；）")
    private Integer linkState;

    @Schema(description = "是否绑定测点，0：未绑定，1：已绑定")
    @ExcelProperty("是否绑定测点，0：未绑定，1：已绑定")
    private Integer bindType;

    @Schema(description = "通道号")
    @ExcelProperty("通道号")
    private String mcuChannel;

    @Schema(description = "设备温度")
    private String deviceTemperature;

    @Schema(description = "设备时钟")
    private LocalDateTime deviceClock;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastReceiveTime;
}