package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplateRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodeltemplate.InstrumentModelTemplateDO;
import cn.powerchina.bjy.link.dam.service.instrumentmodeltemplate.InstrumentModelTemplateService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 仪器类型模板-测量分量")
@RestController
@RequestMapping("/dam/instrument-model-template")
@Validated
public class InstrumentModelTemplateController {

    @Resource
    private InstrumentModelTemplateService instrumentModelTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建仪器类型模板-测量分量")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-model-template:create')")
    public CommonResult<Long> createInstrumentModelTemplate(@Valid @RequestBody InstrumentModelTemplateSaveReqVO createReqVO) {
        return success(instrumentModelTemplateService.createInstrumentModelTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新仪器类型模板-测量分量")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-model-template:update')")
    public CommonResult<Boolean> updateInstrumentModelTemplate(@Valid @RequestBody InstrumentModelTemplateSaveReqVO updateReqVO) {
        instrumentModelTemplateService.updateInstrumentModelTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除仪器类型模板-测量分量")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:instrument-model-template:delete')")
    public CommonResult<Boolean> deleteInstrumentModelTemplate(@RequestParam("id") Long id) {
        instrumentModelTemplateService.deleteInstrumentModelTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得仪器类型模板-测量分量")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-model-template:query')")
    public CommonResult<InstrumentModelTemplateRespVO> getInstrumentModelTemplate(@RequestParam("id") Long id) {
        InstrumentModelTemplateDO instrumentModelTemplate = instrumentModelTemplateService.getInstrumentModelTemplate(id);
        return success(BeanUtils.toBean(instrumentModelTemplate, InstrumentModelTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得仪器类型模板-测量分量分页")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-model-template:query')")
    public CommonResult<PageResult<InstrumentModelTemplateRespVO>> getInstrumentModelTemplatePage(@Valid InstrumentModelTemplatePageReqVO pageReqVO) {
        PageResult<InstrumentModelTemplateDO> pageResult = instrumentModelTemplateService.getInstrumentModelTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InstrumentModelTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出仪器类型模板-测量分量 Excel")
//    @PreAuthorize("@ss.hasPermission('dam:instrument-model-template:export')")
    public void exportInstrumentModelTemplateExcel(@Valid InstrumentModelTemplatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InstrumentModelTemplateDO> list = instrumentModelTemplateService.getInstrumentModelTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "仪器类型模板-测量分量.xls", "数据", InstrumentModelTemplateRespVO.class,
                        BeanUtils.toBean(list, InstrumentModelTemplateRespVO.class));
    }

}