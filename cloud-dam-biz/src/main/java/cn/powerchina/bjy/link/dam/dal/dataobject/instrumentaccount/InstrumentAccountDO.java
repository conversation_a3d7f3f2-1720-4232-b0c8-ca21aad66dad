package cn.powerchina.bjy.link.dam.dal.dataobject.instrumentaccount;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 仪器台账 DO
 *
 * <AUTHOR>
 */
@TableName("dam_instrument_account")
@KeySequence("dam_instrument_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstrumentAccountDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 生产编号
     */
    private String productionNumber;
    /**
     * 仪器型号
     */
    private String instrumentModel;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 生产厂商
     */
    private String instrumentManufacturer;
    /**
     * 备注
     */
    private String remarks;

}