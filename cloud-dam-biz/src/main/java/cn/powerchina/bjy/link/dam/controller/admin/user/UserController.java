package cn.powerchina.bjy.link.dam.controller.admin.user;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.tenant.core.context.TenantContextHolder;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import cn.powerchina.bjy.link.dam.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户信息")
@RestController
@RequestMapping("/dam/user")
@Validated
public class UserController {

    @Resource
    private UserService userService;

    @PostMapping("/create")
    @Operation(summary = "创建用户信息")
    // @PreAuthorize("@ss.hasPermission('dam:user:create')")
    public CommonResult<Long> createUser(@Valid @RequestBody UserSaveReqVO createReqVO) {
        return success(userService.createUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户信息")
    //  @PreAuthorize("@ss.hasPermission('dam:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserSaveReqVO updateReqVO) {
        userService.updateUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户信息")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('dam:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        userService.deleteUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('dam:user:query')")
    public CommonResult<UserRespVO> getUser(@RequestParam("id") Long id) {
        UserDO user = userService.getUser(id);
        return success(BeanUtils.toBean(user, UserRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统管理用户信息分页")
    //@PreAuthorize("@ss.hasPermission('dam:user:query')")
    public CommonResult<PageResult<UserRespVO>> getSystemUserPage(@Valid UserPageReqVO pageReqVO) {
        PageResult<UserBO> pageResult = userService.getUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserRespVO.class));
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新用户状态")
    //@PreAuthorize("@ss.hasPermission('dam:user:update')")
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody UserStatusReqVO userStatusReqVO) {
        userService.updateUserStatus(userStatusReqVO.getId(), userStatusReqVO.getStatus());
        return success(true);
    }

    @PutMapping("/update-password")
    @Operation(summary = "更新密码")
    //@PreAuthorize("@ss.hasPermission('dam:user:update')")
    public CommonResult<Boolean> updatePassword(@Valid @RequestBody UserPasswordVO userPasswordVO) {
        userService.updatePassword(userPasswordVO);
        return success(true);
    }

    @PutMapping("/update/info")
    @Operation(summary = "更新个人信息")
    //@PreAuthorize("@ss.hasPermission('dam:user:update')")
    public CommonResult<Boolean> updateUserInfo(@Valid @RequestBody UserUpdateInfoReqVO userUpdateInfoReqVO) {
        UserSaveReqVO userSaveReqVO = new UserSaveReqVO();
        BeanUtil.copyProperties(userUpdateInfoReqVO, userSaveReqVO);
        userSaveReqVO.setId(WebFrameworkUtils.getLoginUserId());
        userService.updateUser(userSaveReqVO);
        return success(true);
    }

    @GetMapping("/current")
    @Operation(summary = "获得当前登录的用户信息")
    //@PreAuthorize("@ss.hasPermission('dam:user:query')")
    public CommonResult<UserRespVO> getLoginUser() {
        UserDO user = userService.getUser(WebFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(user, UserRespVO.class));
    }

    @PostMapping("/getByUsername")
    @Operation(summary = "根据用户名联动获取主数据")
    //@PreAuthorize("@ss.hasPermission('dam:user:query')")
    public CommonResult<UserDO> getUserByUsername(@RequestBody UserSaveReqVO createReqVO) {
        UserDO user = userService.getUserByUsername(createReqVO);
        return success(user);
    }

}