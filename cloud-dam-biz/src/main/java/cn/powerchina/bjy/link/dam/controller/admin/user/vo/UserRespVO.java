package cn.powerchina.bjy.link.dam.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28308")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("用户账号")
    private String username;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "手机号码")
    @ExcelProperty("手机号码")
    private String mobile;

    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("部门名称")
    private String deptName;

    @Schema(description = "职务", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("职务")
    private String postName;

    @Schema(description = "用户类型:(1系统管理员，2项目管理员，3普通用户)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("用户类型:(1系统管理员，2项目管理员，3普通用户)")
    private Integer userType;

    @Schema(description = "启用状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("启用状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "所属项目")
    @ExcelProperty("所属项目")
    private String projectNames;

    @Schema(description = "角色")
    @ExcelProperty("角色")
    private String roleNames;
}