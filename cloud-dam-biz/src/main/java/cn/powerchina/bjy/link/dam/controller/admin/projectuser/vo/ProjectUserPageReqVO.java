package cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 项目用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectUserPageReqVO extends PageParam {

    @NotNull(message = "请选择一个项目")
    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "用户账号")
    private String username;

    @Schema(description = "手机号码")
    private String mobile;

}