package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点计算参数表头 Response VO")
@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
public class PointParamTableRespVO {

    @Schema(description = "显示名称")
    @ExcelProperty("显示名称")
    private String labelName;

    @Schema(description = "字段名称")
    @ExcelProperty("字段名称")
    private String fieldName;

}