package cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 项目菜单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectMenuRespVO {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目已分配的菜单ID")
    private List<Long> menuIds;

    @Schema(description = "菜单列表")
    private List<MenuVO> menuList;

    @Data
    public static class MenuVO {

        @Schema(description = "菜单编号")
        private Long id;

        @Schema(description = "菜单名称")
        private String name;

        @Schema(description = "父菜单 ID")
        private Long parentId;

        @Schema(description = "类型，参见 MenuTypeEnum 枚举类")
        private Integer type;

    }

}