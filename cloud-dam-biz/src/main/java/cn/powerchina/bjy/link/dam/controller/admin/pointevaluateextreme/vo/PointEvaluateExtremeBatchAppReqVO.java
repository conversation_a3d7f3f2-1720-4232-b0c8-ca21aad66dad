package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点评价指标极值新增/修改 Request VO")
@Data
public class PointEvaluateExtremeBatchAppReqVO {

    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27258")
    private Long projectId;

    @Schema(description = "测点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31317")
    private Long pointId;

    @Schema(description = "测点编号")
    private String pointCode;


}