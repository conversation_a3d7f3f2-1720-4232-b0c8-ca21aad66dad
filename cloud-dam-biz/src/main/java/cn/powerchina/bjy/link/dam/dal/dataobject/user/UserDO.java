package cn.powerchina.bjy.link.dam.dal.dataobject.user;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 用户信息 DO
 *
 * <AUTHOR>
 */
@TableName("dam_user")
@KeySequence("dam_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 职务
     */
    private String postName;
    /**
     * 用户类型:(1系统管理员，2项目管理员，3普通用户)
     */
    private Integer userType;
    /**
     * 启用状态（0正常 1停用）
     */
    private Integer status;

}