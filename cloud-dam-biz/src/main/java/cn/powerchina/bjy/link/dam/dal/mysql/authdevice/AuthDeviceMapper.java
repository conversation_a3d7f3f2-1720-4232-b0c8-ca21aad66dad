package cn.powerchina.bjy.link.dam.dal.mysql.authdevice;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthDevicePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authdevice.AuthDeviceDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备授权 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AuthDeviceMapper extends BaseMapperX<AuthDeviceDO> {

    default PageResult<AuthDeviceDO> selectPage(AuthDevicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AuthDeviceDO>()
                .eqIfPresent(AuthDeviceDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                .eqIfPresent(AuthDeviceDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(AuthDeviceDO::getDeviceGroupId, reqVO.getDeviceGroupId())
                .betweenIfPresent(AuthDeviceDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AuthDeviceDO::getId));
    }

}