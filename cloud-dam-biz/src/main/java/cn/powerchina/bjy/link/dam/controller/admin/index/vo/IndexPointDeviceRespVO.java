package cn.powerchina.bjy.link.dam.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 首页仪器类型信息
 * @Author: yhx
 * @CreateDate: 2024/11/22
 */
@Schema(description = "管理后台 - 首页仪器类型信息 Response VO")
@Data
public class IndexPointDeviceRespVO {

    @Schema(description = "仪器类型名称")
    private String instrumentName;

    @Schema(description = "仪器数量")
    private Long instrumentCount;

    @Schema(description = "仪器占比")
    private String instrumentRate;

    @Schema(description = "观测记录数量")
    private Long pointDataCount;

    @Schema(description = "最近观测时间")
    private LocalDateTime pointTimeRecent;

    @Schema(description = "最早观测时间")
    private LocalDateTime pointTimeFirst;
}
