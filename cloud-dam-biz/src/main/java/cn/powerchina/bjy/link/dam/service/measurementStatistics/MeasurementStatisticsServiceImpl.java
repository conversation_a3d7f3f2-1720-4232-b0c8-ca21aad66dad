package cn.powerchina.bjy.link.dam.service.measurementStatistics;

import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.monitorchart.bo.PointDataJsonBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.ExcelSetupmap;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataStatisticsVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.tdengine.DamPointDataJsonMapper;
import cn.powerchina.bjy.link.dam.dal.tdengine.DamPointDataMapper;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import cn.powerchina.bjy.link.dam.strategy.CustomColumnWidthStrategy;
import cn.powerchina.bjy.link.dam.util.BigDecimalUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 测值统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MeasurementStatisticsServiceImpl  implements MeasurementStatisticsService{
    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private DamPointDataMapper damPointDataMapper;

    @Resource
    private DamPointDataJsonMapper damPointDataJsonMapper;

    @Resource
    private InstrumentModelMapper instrumentModelMapper;
    @Resource
    private PointDataJsonService pointDataJsonService;
    @Resource
    private PointService pointService;

    @Override
    public List<PointDataBO> pointStatistics(PointDataStatisticsVO statisticsVO) {
        // 根据仪器类型分量id查询详情
        List<InstrumentModelDO> instrumentModelDOList = Optional.ofNullable(
                instrumentModelService.listByIdList(statisticsVO.getInstrumentModelIds())
        ).orElse(new ArrayList<>());

        // 分量转成map，方便下面取值
        Map<Long, InstrumentModelDO> instrumentModelDOMap = instrumentModelDOList.stream().collect(Collectors.toMap(
                temp->temp.getId(),
                temp->temp,
                (k1, k2)->k1));

        // 最终返回的数据
        List<PointDataBO> result = new ArrayList<>();

        // 前端传的测点
        for (Long pointId : statisticsVO.getPointIds()) {
            //获取测点信息
            PointDO point = pointService.getPoint(pointId);

            // 如果时序数据库表不存在，跳过该点位，处理下一个点位数据
            try {
                damPointDataMapper.getPointDataSTableFieldList(String.valueOf(statisticsVO.getProjectId()), String.valueOf(point.getInstrumentId()));
            } catch (Exception e) {
                if (e.getMessage().contains("does not exist")) {
                    continue;
                } else {
                    throw e;
                }
            }

            // 根据部分查询条件查询出要展示的数据
            List<Map> mapList = damPointDataJsonMapper.list4MeasurementStatisticsTdengine(
                    statisticsVO.getProjectId(), point.getInstrumentId(), pointId,
                    statisticsVO.getPointTime()[0].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    statisticsVO.getPointTime()[1].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    statisticsVO.getDataTypeList(), statisticsVO.getDataStatusList());
            List<PointDataJsonBO> pointDataJsonBOList = new ArrayList<>();
            mapList.forEach(temp->{
                for (InstrumentModelDO instrumentModelDO : instrumentModelDOList) {
                    Object thingValue = temp.get(instrumentModelDO.getThingIdentity());
                    if (Objects.nonNull(thingValue)) {
                        PointDataJsonBO pointDataJsonBO = new PointDataJsonBO();
                        pointDataJsonBO.setProjectId(statisticsVO.getProjectId());
                        pointDataJsonBO.setPointId(Long.valueOf((String) temp.get("point_id")));
                        pointDataJsonBO.setPointTime(((Date) temp.get("ts")).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                        pointDataJsonBO.setInstrumentId(instrumentModelDO.getInstrumentId());
                        pointDataJsonBO.setInstrumentModelId(instrumentModelDO.getId());
                        pointDataJsonBO.setThingIdentity(instrumentModelDO.getThingIdentity());
                        if (statisticsVO.getUseValueAbsolute()==1) {
                            pointDataJsonBO.setThingValue(new BigDecimal(thingValue.toString()).abs());
                        } else {
                            pointDataJsonBO.setThingValue(new BigDecimal(thingValue.toString()));
                        }
                        pointDataJsonBO.setDataType((Integer) temp.get("data_type"));
                        pointDataJsonBO.setDataStatus((Integer) temp.get("data_status"));
                        pointDataJsonBO.setReviewStatus((Integer) temp.get("review_status"));
                        pointDataJsonBOList.add(pointDataJsonBO);
                    }
                }
            });
            // 前端传过来的分量id
            Set<Long> instrumentModelIdSet = new HashSet<>(statisticsVO.getInstrumentModelIds());
            // 再按前端传过来的分量id过滤数据
            List<PointDataJsonBO> showPointDataJsonBOList = new ArrayList<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(instrumentModelIdSet)) {
                showPointDataJsonBOList = pointDataJsonBOList.stream().filter(item -> {
                    return instrumentModelIdSet.contains(item.getInstrumentModelId());
                }).collect(Collectors.toList());
            } else {
                showPointDataJsonBOList = pointDataJsonBOList;
            }
            // 要展示的数据按分量分组
            Map<Long, List<PointDataJsonBO>> showPointDataJsonBOMap = showPointDataJsonBOList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getInstrumentModelId));

            // 根据部分查询条件查询出历史数据
            List<Map> mapHistoryList = damPointDataJsonMapper.list4MeasurementStatisticsTdengine(
                    statisticsVO.getProjectId(), point.getInstrumentId(), pointId,
                    null,
                    statisticsVO.getPointTime()[1].format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    statisticsVO.getDataTypeList(), statisticsVO.getDataStatusList());
            List<PointDataJsonBO> pointDataJsonBOHistoryList = new ArrayList<>();
            mapHistoryList.forEach(temp->{
                for (InstrumentModelDO instrumentModelDO : instrumentModelDOList) {
                    Object thingValue = temp.get(instrumentModelDO.getThingIdentity());
                    if (Objects.nonNull(thingValue)) {
                        PointDataJsonBO pointDataJsonBO = new PointDataJsonBO();
                        pointDataJsonBO.setProjectId(statisticsVO.getProjectId());
                        pointDataJsonBO.setPointId(Long.valueOf((String) temp.get("point_id")));
                        pointDataJsonBO.setPointTime(((Date) temp.get("ts")).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                        pointDataJsonBO.setInstrumentId(instrumentModelDO.getInstrumentId());
                        pointDataJsonBO.setInstrumentModelId(instrumentModelDO.getId());
                        pointDataJsonBO.setThingIdentity(instrumentModelDO.getThingIdentity());
                        if (statisticsVO.getUseValueAbsolute()==1) {
                            pointDataJsonBO.setThingValue(new BigDecimal(thingValue.toString()).abs());
                        } else {
                            pointDataJsonBO.setThingValue(new BigDecimal(thingValue.toString()));
                        }
                        pointDataJsonBO.setDataType((Integer) temp.get("data_type"));
                        pointDataJsonBO.setDataStatus((Integer) temp.get("data_status"));
                        pointDataJsonBO.setReviewStatus((Integer) temp.get("review_status"));
                        pointDataJsonBOHistoryList.add(pointDataJsonBO);
                    }
                }
            });
            // 再按前端传过来的分量id过滤数据
            List<PointDataJsonBO> showPointDataJsonBOHistoryList = new ArrayList<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(instrumentModelIdSet)) {
                showPointDataJsonBOHistoryList = pointDataJsonBOHistoryList.stream().filter(item -> {
                    return instrumentModelIdSet.contains(item.getInstrumentModelId());
                }).collect(Collectors.toList());
            } else {
                showPointDataJsonBOHistoryList = pointDataJsonBOList;
            }
            // 历史数据按分量分组
            Map<Long, List<PointDataJsonBO>> showPointDataJsonBOHistoryMap = showPointDataJsonBOHistoryList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getInstrumentModelId));

            // "时间类型(1：月统计，2：年统计，3：自定义时间段
            if (statisticsVO.getTimeType() == 1) {
                for (Long instrumentModelId : statisticsVO.getInstrumentModelIds()) {
                    // 分量信息
                    InstrumentModelDO instrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(instrumentModelId)).orElse(new InstrumentModelDO());

                    // 当前点位当前分量要展示的数据
                    List<PointDataJsonBO> pointModelList = Optional.ofNullable(showPointDataJsonBOMap.get(instrumentModelId)).orElse(new ArrayList<>());
                    // 当前点位当前分量历史数据
                    List<PointDataJsonBO> pointModelHistoryList = Optional.ofNullable(showPointDataJsonBOHistoryMap.get(instrumentModelId)).orElse(new ArrayList<>());

                    // 当前点位当前分量要展示的数据按月分组
                    Map<String, List<PointDataJsonBO>> pointModelMonthDataMap = pointModelList.stream()
                            .collect(Collectors.groupingBy(data ->
                                    String.format("%d-%02d",
                                            data.getPointTime().getYear(),
                                            data.getPointTime().getMonthValue())));

                    // 组装要返回的数据
                    pointModelMonthDataMap.forEach((yearMonth, yearMonthDataList)->{
                        // 最小值
                        PointDataJsonBO minBO = yearMonthDataList.stream().min(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());
                        // 最大值
                        PointDataJsonBO maxBO = yearMonthDataList.stream().max(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime, Comparator.reverseOrder())).orElse(new PointDataJsonBO());
                        // 平均值
                        BigDecimal avgValue = yearMonthDataList.stream().map(PointDataJsonBO::getThingValue).filter(Objects::nonNull).reduce(BigDecimal::add)
                                .map(sum -> sum.divide(new BigDecimal(yearMonthDataList.size()), 2, BigDecimal.ROUND_HALF_UP)).orElse(null);
                        // 变幅
                        BigDecimal valueRange = maxBO.getThingValue().subtract(minBO.getThingValue());

                        // 历史数据
                        List<PointDataJsonBO> yearMonthHistoryDataList = pointModelHistoryList.stream().filter(temp->{
                            LocalDateTime endTime = YearMonth.parse(yearMonth).atEndOfMonth().atTime(23, 59, 59);
                            return temp.getPointTime().isBefore(endTime);
                        }).collect(Collectors.toList());

                        // 历史最大值
                        PointDataJsonBO maxHistoryBO = yearMonthHistoryDataList.stream().max(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime, Comparator.reverseOrder())).orElse(new PointDataJsonBO());
                        // 历史最小值
                        PointDataJsonBO minHistoryBO = yearMonthHistoryDataList.stream().min(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());

                        PointDataBO pointDataBO = new PointDataBO();
                        pointDataBO.setProjectId(statisticsVO.getProjectId());
                        pointDataBO.setPointId(point.getId());
                        pointDataBO.setPointCode(point.getPointCode());
                        pointDataBO.setInstrumentModelId(instrumentModelDO.getId());
                        pointDataBO.setThingIdentity(instrumentModelDO.getThingIdentity());
                        pointDataBO.setThingName(instrumentModelDO.getThingName());
                        pointDataBO.setThingUnit(instrumentModelDO.getThingUnit());
                        pointDataBO.setValueMin(minBO.getThingValue());
                        pointDataBO.setValueMinPointTime(minBO.getPointTime());
                        pointDataBO.setValueMax(maxBO.getThingValue());
                        pointDataBO.setValueMaxPointTime(maxBO.getPointTime());
                        pointDataBO.setValueAverage(avgValue);
                        pointDataBO.setValueRange(valueRange);
                        pointDataBO.setValueMaxHistory(maxHistoryBO.getThingValue());
                        pointDataBO.setValueMinHistory(minHistoryBO.getThingValue());
                        pointDataBO.setCountNum((long) yearMonthDataList.size());
                        pointDataBO.setYearAndMonth(yearMonth);
                        pointDataBO.setValueMinHistoryPointTime(minHistoryBO.getPointTime());
                        pointDataBO.setValueMaxHistoryPointTime(maxHistoryBO.getPointTime());
                        result.add(pointDataBO);
                    });
                }

            } else if (statisticsVO.getTimeType() == 2) {
                for (Long instrumentModelId : statisticsVO.getInstrumentModelIds()) {
                    // 分量信息
                    InstrumentModelDO instrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(instrumentModelId)).orElse(new InstrumentModelDO());

                    // 当前点位当前分量要展示的数据
                    List<PointDataJsonBO> pointModelList = Optional.ofNullable(showPointDataJsonBOMap.get(instrumentModelId)).orElse(new ArrayList<>());
                    // 当前点位当前分量历史数据
                    List<PointDataJsonBO> pointModelHistoryList = Optional.ofNullable(showPointDataJsonBOHistoryMap.get(instrumentModelId)).orElse(new ArrayList<>());

                    // 当前点位当前分量要展示的数据按年分组
                    Map<String, List<PointDataJsonBO>> pointModelYearDataMap = pointModelList.stream()
                            .collect(Collectors.groupingBy(data->String.valueOf(data.getPointTime().getYear())));

                    // 组装要返回的数据
                    pointModelYearDataMap.forEach((year, yearDataList)->{
                        // 最小值
                        PointDataJsonBO minBO = yearDataList.stream().min(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());
                        // 最大值
                        PointDataJsonBO maxBO = yearDataList.stream().max(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime, Comparator.reverseOrder())).orElse(new PointDataJsonBO());
                        // 平均值
                        BigDecimal avgValue = yearDataList.stream().map(PointDataJsonBO::getThingValue).filter(Objects::nonNull).reduce(BigDecimal::add)
                                .map(sum -> sum.divide(new BigDecimal(yearDataList.size()), 2, BigDecimal.ROUND_HALF_UP)).orElse(null);
                        // 变幅
                        BigDecimal valueRange = maxBO.getThingValue().subtract(minBO.getThingValue());

                        // 历史数据
                        List<PointDataJsonBO> yearHistoryDataList = pointModelHistoryList.stream().filter(temp->{
                            LocalDateTime endTime = LocalDateTime.now().withYear(Integer.valueOf(year)).withMonth(12).withDayOfMonth(31).withHour(23).withMinute(59).withSecond(59);
                            return temp.getPointTime().isBefore(endTime);
                        }).collect(Collectors.toList());

                        // 历史最大值
                        PointDataJsonBO maxHistoryBO = yearHistoryDataList.stream().max(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime, Comparator.reverseOrder())).orElse(new PointDataJsonBO());
                        // 历史最小值
                        PointDataJsonBO minHistoryBO = yearHistoryDataList.stream().min(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());

                        PointDataBO pointDataBO = new PointDataBO();
                        pointDataBO.setProjectId(statisticsVO.getProjectId());
                        pointDataBO.setPointId(point.getId());
                        pointDataBO.setPointCode(point.getPointCode());
                        pointDataBO.setInstrumentModelId(instrumentModelDO.getId());
                        pointDataBO.setThingIdentity(instrumentModelDO.getThingIdentity());
                        pointDataBO.setThingName(instrumentModelDO.getThingName());
                        pointDataBO.setThingUnit(instrumentModelDO.getThingUnit());
                        pointDataBO.setValueMin(minBO.getThingValue());
                        pointDataBO.setValueMinPointTime(minBO.getPointTime());
                        pointDataBO.setValueMax(maxBO.getThingValue());
                        pointDataBO.setValueMaxPointTime(maxBO.getPointTime());
                        pointDataBO.setValueAverage(avgValue);
                        pointDataBO.setValueRange(valueRange);
                        pointDataBO.setValueMaxHistory(maxHistoryBO.getThingValue());
                        pointDataBO.setValueMinHistory(minHistoryBO.getThingValue());
                        pointDataBO.setCountNum((long) yearDataList.size());
                        pointDataBO.setYearAndMonth(year);
                        pointDataBO.setValueMinHistoryPointTime(minHistoryBO.getPointTime());
                        pointDataBO.setValueMaxHistoryPointTime(maxHistoryBO.getPointTime());
                        result.add(pointDataBO);
                    });
                }

            } else {
                for (Long instrumentModelId : statisticsVO.getInstrumentModelIds()) {
                    // 分量信息
                    InstrumentModelDO instrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(instrumentModelId)).orElse(new InstrumentModelDO());

                    // 当前点位当前分量要展示的数据
                    List<PointDataJsonBO> pointModelList = Optional.ofNullable(showPointDataJsonBOMap.get(instrumentModelId)).orElse(new ArrayList<>());
                    // 当前点位当前分量历史数据
                    List<PointDataJsonBO> pointModelHistoryList = Optional.ofNullable(showPointDataJsonBOHistoryMap.get(instrumentModelId)).orElse(new ArrayList<>());

                    // 组装要返回的数据
                    // 最小值
                    PointDataJsonBO minBO = pointModelList.stream().min(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());
                    // 最大值
                    PointDataJsonBO maxBO = pointModelList.stream().max(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime, Comparator.reverseOrder())).orElse(new PointDataJsonBO());
                    // 首值
                    PointDataJsonBO firstBO = pointModelList.stream().min(Comparator.comparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());
                    // 尾值
                    PointDataJsonBO lastBO = pointModelList.stream().max(Comparator.comparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());
                    // 平均值
                    BigDecimal avgValue = pointModelList.stream().map(PointDataJsonBO::getThingValue).filter(Objects::nonNull).reduce(BigDecimal::add)
                            .map(sum -> sum.divide(new BigDecimal(pointModelList.size()), 2, BigDecimal.ROUND_HALF_UP)).orElse(null);
                    // 变幅
                    BigDecimal tempMax = Optional.ofNullable(maxBO).map(PointDataJsonBO::getThingValue).orElse(null);
                    BigDecimal tempMin = Optional.ofNullable(minBO).map(PointDataJsonBO::getThingValue).orElse(null);
                    BigDecimal valueRange = BigDecimalUtils.calculateValueSub(tempMax, tempMin, 2);

                    // 历史最大值
                    PointDataJsonBO maxHistoryBO = pointModelHistoryList.stream().max(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime, Comparator.reverseOrder())).orElse(new PointDataJsonBO());
                    // 历史最小值
                    PointDataJsonBO minHistoryBO = pointModelHistoryList.stream().min(Comparator.comparing(PointDataJsonBO::getThingValue).thenComparing(PointDataJsonBO::getPointTime)).orElse(new PointDataJsonBO());

                    PointDataBO pointDataBO = new PointDataBO();
                    pointDataBO.setProjectId(statisticsVO.getProjectId());
                    pointDataBO.setPointId(point.getId());
                    pointDataBO.setPointCode(point.getPointCode());
                    pointDataBO.setInstrumentModelId(instrumentModelDO.getId());
                    pointDataBO.setThingIdentity(instrumentModelDO.getThingIdentity());
                    pointDataBO.setThingName(instrumentModelDO.getThingName());
                    pointDataBO.setThingUnit(instrumentModelDO.getThingUnit());
                    pointDataBO.setValueMin(minBO.getThingValue());
                    pointDataBO.setValueMinPointTime(minBO.getPointTime());
                    pointDataBO.setValueMax(maxBO.getThingValue());
                    pointDataBO.setValueMaxPointTime(maxBO.getPointTime());
                    pointDataBO.setValueFirst(firstBO.getThingValue());
                    pointDataBO.setValueFirstPointTime(firstBO.getPointTime());
                    pointDataBO.setValueLast(lastBO.getThingValue());
                    pointDataBO.setValueLastPointTime(lastBO.getPointTime());
                    pointDataBO.setValueAverage(avgValue);
                    pointDataBO.setValueRange(valueRange);
                    pointDataBO.setValueMaxHistory(maxHistoryBO.getThingValue());
                    pointDataBO.setValueMinHistory(minHistoryBO.getThingValue());
                    pointDataBO.setCountNum((long) pointModelList.size());
                    pointDataBO.setValueMinHistoryPointTime(minHistoryBO.getPointTime());
                    pointDataBO.setValueMaxHistoryPointTime(maxHistoryBO.getPointTime());
                    result.add(pointDataBO);
                }
            }
        }
        return result;
//        // 根据仪器类型分量id查询详情
//        List<InstrumentModelDO> instrumentModelDOList = Optional.ofNullable(
//                instrumentModelService.listByIdList(statisticsVO.getInstrumentModelIds())
//        ).orElse(new ArrayList<>());
//        Long tempInstrumentId = instrumentModelDOList.stream().map(InstrumentModelDO::getInstrumentId)
//                .filter(temp-> Objects.nonNull(temp)).findFirst().orElse(null);

//        // 如果时序数据库表不存在，则直接返回空列表
//        try {
//            damPointDataMapper.getPointDataSTableFieldList(String.valueOf(statisticsVO.getProjectId()), String.valueOf(tempInstrumentId));
//        } catch (Exception e) {
//            if (e.getMessage().contains("Table does not exist")) {
//                return new ArrayList<>();
//            } else {
//                throw e;
//            }
//        }

    }
    private BigDecimal parseValueFromJson(String jsonData, Long instrumentModelId) {
        try {
            // 将JSON字符串解析为对象数组
            if (jsonData == null || jsonData.isEmpty()) {
                return null;
            }
            JSONArray jsonArray = JSONArray.parseArray(jsonData);

            // 遍历数组查找匹配的分量ID
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                Long currentId = item.getLong("instrumentModelId");

                if (currentId != null && currentId.equals(instrumentModelId)) {
                    // 获取分量值并转换为BigDecimal
                    Object value = item.get("thingValue");
                    if (value != null) {
                        try {
                            return new BigDecimal(value.toString());
                        } catch (NumberFormatException e) {
                            log.error("数值格式转换失败: {}", value, e);
                            return null;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 处理JSON解析异常
            log.error("解析测点数据JSON失败: {}", jsonData, e);
        }
        return null;
    }

    @Override
    public byte[] exportExcel(PointDataStatisticsVO statisticsVO) {
        try {
            // 要导出的数据
            List<PointDataBO> pointDataBOList = pointStatistics(statisticsVO);

            // 数据按分量分组
            Map<Long, List<PointDataBO>> pointDataBOMap = pointDataBOList.stream().collect(Collectors.groupingBy(PointDataBO::getInstrumentModelId));

            // 指定分量顺序
            List<Long> keyList = pointDataBOList.stream().map(PointDataBO::getInstrumentModelId).distinct().sorted((o1, o2)->o1.compareTo(o2)).collect(Collectors.toList());

            // 前端设置的要展示的列
            List<String> excelSetups = new ArrayList<>();
            excelSetups.addAll(
                    Optional.ofNullable(statisticsVO.getExcelSetup())
                            .filter(s -> !s.isEmpty())
                            .map(s -> Arrays.stream(s.split(",")).collect(Collectors.toList()))
                            .orElse(Collections.emptyList())
            );
            if (CollUtil.isEmpty(excelSetups)) {
                if (statisticsVO.getTimeType() == 1) {
                    excelSetups.addAll(ExcelSetupmap.getMonthCol());
                } else if (statisticsVO.getTimeType() == 2) {
                    excelSetups.addAll(ExcelSetupmap.getYearCol());
                } else {
                    excelSetups.addAll(ExcelSetupmap.getCustomCol());
                }
            }

            // 表头中展示的统计类型以及统计时间
            StringBuilder partOfTitle = new StringBuilder("");
            if (Objects.equals(statisticsVO.getTimeType(), 1)) { // 月统计
                LocalDateTime beginTime = statisticsVO.getPointTime()[0];
                String beginTimeStr = beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                LocalDateTime endTime = statisticsVO.getPointTime()[1];
                String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));

                partOfTitle.append("月份特征值统计").append(" ").append(beginTimeStr).append("-").append(endTimeStr);
            } else if (Objects.equals(statisticsVO.getTimeType(), 2)) { // 年统计
                LocalDateTime beginTime = statisticsVO.getPointTime()[0];
                String beginTimeStr = beginTime.format(DateTimeFormatter.ofPattern("yyyy"));
                LocalDateTime endTime = statisticsVO.getPointTime()[1];
                String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy"));

                partOfTitle.append("年度特征值统计").append(" ").append(beginTimeStr).append("-").append(endTimeStr);
            } else if (Objects.equals(statisticsVO.getTimeType(), 3)) { // 自定义时间段
                LocalDateTime beginTime = statisticsVO.getPointTime()[0];
                String beginTimeStr = beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                LocalDateTime endTime = statisticsVO.getPointTime()[1];
                String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                partOfTitle.append("时段特征值统计").append(" ").append(beginTimeStr).append("-").append(endTimeStr);
            }

            // 表头
            Map<Long, List<List<String>>> headListMap = new HashMap<>();
            keyList.forEach(key -> {
                PointDataBO pointDataBO = Optional.ofNullable(pointDataBOMap.get(key)).orElse(new ArrayList<>()).stream().findFirst().orElse(new PointDataBO());
                StringBuilder title = new StringBuilder("");
                title.append(pointDataBO.getThingName());

                String thingUnit = pointDataBO.getThingUnit();
                if (StringUtils.isNotBlank(thingUnit)) {
                    title.append("(").append(thingUnit).append(")");
                }
                if (StringUtils.isNotBlank(partOfTitle.toString())) {
                    title.append(" ").append(partOfTitle.toString());
                }

                List<List<String>> headList = new ArrayList<>();
                excelSetups.forEach(setup -> {
                    List<String> head = new ArrayList<>();
                    head.add(title.toString());
                    // 根据英文名映射中文名
                    head.add(ExcelSetupmap.getChineseName(statisticsVO.getTimeType(), setup));
                    headList.add(head);
                });

                headListMap.put(key, headList);
            });

            // 列宽
            Map<String, Integer> widthMap = ExcelSetupmap.getColumnWidthConfig();
            Map<Integer, Integer> columnWidthMap = new HashMap<>();
            for (int i=0; i<excelSetups.size(); i++) {
                columnWidthMap.put(i, widthMap.get(excelSetups.get(i)));
            }

            // 1. 创建表头样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            // 设置背景色为灰色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);
            // 设置表头所有边框
            headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            headWriteCellStyle.setBorderTop(BorderStyle.THIN);
            headWriteCellStyle.setBorderRight(BorderStyle.THIN);
            headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            // 设置水平居中
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

            // 2. 创建内容样式
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 主体部分 HorizontalAlignment 和 VerticalAlignment 均默认居左居中，可以不 set
            // contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            // contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置内容所有边框
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

            // 3. 创建样式策略
            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "测值统计")
                    .registerWriteHandler(styleStrategy).registerWriteHandler(new CustomColumnWidthStrategy(columnWidthMap)).build();

            for (int i=0; i<keyList.size(); i++) {
                Long key = keyList.get(i);

                List<List<Object>> dataList = new ArrayList<>();
                pointDataBOMap.get(key).forEach(bo -> {
                    List<Object> row = new ArrayList<>();
                    excelSetups.forEach(setup -> {
                        try {
                            // 通过反射获取对应的字段值
                            Field field = PointDataBO.class.getDeclaredField(setup);
                            field.setAccessible(true);
                            Object value = field.get(bo);
                            row.add(value);
                        } catch (Exception e) {
                            row.add(null);
                            log.warn("导出字段获取失败: {}", setup, e);
                        }
                    });
                    dataList.add(row);
                });

                WriteTable writeTable = EasyExcel.writerTable(i)
                        .head(headListMap.get(key))
                        .build();
                if (i!=0) {
                    writeTable.setRelativeHeadRowIndex(3);
                }
                excelWriter.write(dataList, writeSheet, writeTable);

            }
            excelWriter.finish();
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("exportExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }
}
