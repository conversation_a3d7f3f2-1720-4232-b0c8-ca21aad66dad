package cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo;

import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 测点计算公式 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointFormulaRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7726")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "1849")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id", example = "23216")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "分量id", example = "22493")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "分量名称", example = "赵六")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "计算公式")
    @ExcelProperty("计算公式")
    private String calcFormula;

    @Schema(description = "有效开始时间")
    @ExcelProperty("有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    @ExcelProperty("有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    @ExcelProperty("适用类型，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}