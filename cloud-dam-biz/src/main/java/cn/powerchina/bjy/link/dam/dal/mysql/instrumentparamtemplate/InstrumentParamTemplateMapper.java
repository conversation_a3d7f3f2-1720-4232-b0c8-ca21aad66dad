package cn.powerchina.bjy.link.dam.dal.mysql.instrumentparamtemplate;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparamtemplate.vo.InstrumentParamTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparamtemplate.InstrumentParamTemplateDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仪器类型模板-计算参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentParamTemplateMapper extends BaseMapperX<InstrumentParamTemplateDO> {

    default PageResult<InstrumentParamTemplateDO> selectPage(InstrumentParamTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstrumentParamTemplateDO>()
                .eqIfPresent(InstrumentParamTemplateDO::getInstrumentId, reqVO.getInstrumentId())
                .likeIfPresent(InstrumentParamTemplateDO::getThingName, reqVO.getThingName())
                .eqIfPresent(InstrumentParamTemplateDO::getThingIdentity, reqVO.getThingIdentity())
                .eqIfPresent(InstrumentParamTemplateDO::getThingUnit, reqVO.getThingUnit())
                .eqIfPresent(InstrumentParamTemplateDO::getDecimalLimit, reqVO.getDecimalLimit())
                .eqIfPresent(InstrumentParamTemplateDO::getThingWeight, reqVO.getThingWeight())
                .betweenIfPresent(InstrumentParamTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InstrumentParamTemplateDO::getId));
    }

}