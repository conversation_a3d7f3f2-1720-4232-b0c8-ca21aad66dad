package cn.powerchina.bjy.link.dam.service.iotproduct;

import cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct.IotProductDO;

import java.util.List;

/**
 * 物联网平台同步的产品 Service 接口
 *
 * <AUTHOR>
 */
public interface IotProductService {

    /**
     * 新增
     * @param iotProductDO 物联网平台库的产品
     */
    void createIotProduct(IotProductDO iotProductDO);

    /**
     * 根据物联网平台库的产品id删除
     * @param iotId 物联网平台库的产品id
     */
    void deleteByIotId(Long iotId);

    /**
     * 根据物联网平台库的产品id更新
     * @param iotProductDO 物联网平台库的产品id
     */
    void updateByIotId(IotProductDO iotProductDO);

    /**
     * 根据物联网平台库的产品id获取物联网平台库同步的产品
     * @param iotId 物联网平台库的产品id
     * @return 物联网平台库同步的产品
     */
    IotProductDO getByIotId(Long iotId);

    /**
     * 根据产品编码列表获取物联网平台同步的产品列表
     * @param productCodeList 产品编码列表
     * @return 物联网平台同步的产品列表
     */
    List<IotProductDO> listByProductCode(List<String> productCodeList);
}
