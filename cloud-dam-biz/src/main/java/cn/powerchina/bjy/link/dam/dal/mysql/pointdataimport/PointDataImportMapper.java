package cn.powerchina.bjy.link.dam.dal.mysql.pointdataimport;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointdataimport.vo.PointDataImportPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdataimport.PointDataImportDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测点数据导入 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointDataImportMapper extends BaseMapperX<PointDataImportDO> {

    default PageResult<PointDataImportDO> selectPage(PointDataImportPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PointDataImportDO>()
                .eqIfPresent(PointDataImportDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(PointDataImportDO::getPointId, reqVO.getPointId())
                .eqIfPresent(PointDataImportDO::getDataType, reqVO.getDataType())
                .eqIfPresent(PointDataImportDO::getImportType, reqVO.getImportType())
                .betweenIfPresent(PointDataImportDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(PointDataImportDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PointDataImportDO::getFilePath, reqVO.getFilePath())
                .betweenIfPresent(PointDataImportDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PointDataImportDO::getId));
    }

}