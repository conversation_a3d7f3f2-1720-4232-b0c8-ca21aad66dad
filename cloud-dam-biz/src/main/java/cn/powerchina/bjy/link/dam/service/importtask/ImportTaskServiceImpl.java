package cn.powerchina.bjy.link.dam.service.importtask;

import cn.hutool.core.collection.CollUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.ImportFileRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importfile.ImportFileDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importnode.ImportNodeDO;
import cn.powerchina.bjy.link.dam.dal.mysql.importnode.ImportNodeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.powerchina.bjy.link.dam.controller.admin.importtask.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importtask.ImportTaskDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;

import cn.powerchina.bjy.link.dam.dal.mysql.importtask.ImportTaskMapper;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.diffList;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 导入任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImportTaskServiceImpl implements ImportTaskService {

    @Resource
    private ImportTaskMapper importTaskMapper;
    @Resource
    private ImportNodeMapper importNodeMapper;
    @Override
    public Long createImportTask(ImportTaskSaveReqVO createReqVO) {
        // 插入
        ImportTaskDO importTask = BeanUtils.toBean(createReqVO, ImportTaskDO.class);
        importTaskMapper.insert(importTask);

        // 返回
        return importTask.getId();
    }

    @Override
    public void updateImportTask(ImportTaskSaveReqVO updateReqVO) {
        // 校验存在
        validateImportTaskExists(updateReqVO.getId());
        // 更新
        ImportTaskDO updateObj = BeanUtils.toBean(updateReqVO, ImportTaskDO.class);
        importTaskMapper.updateById(updateObj);
    }

    @Override
    public void deleteImportTask(Long id) {
        // 校验存在
        validateImportTaskExists(id);
        // 删除
        importTaskMapper.deleteById(id);
    }

    @Override
        public void deleteImportTaskListByIds(List<Long> ids) {
        // 删除
        importTaskMapper.deleteBatchIds(ids);
        }


    private void validateImportTaskExists(Long id) {
        if (importTaskMapper.selectById(id) == null) {
//            throw exception(IMPORT_TASK_NOT_EXISTS);
        }
    }

    @Override
    public ImportTaskDO getImportTask(Long id) {
        return importTaskMapper.selectById(id);
    }

    @Override
    public PageResult<ImportTaskDO> getImportTaskPage(ImportTaskPageReqVO pageReqVO) {
        if (!Objects.nonNull(pageReqVO.getNodeId())) {
            throw exception(new ErrorCode(500, "节点不能为空"));
        }
        if(0==pageReqVO.getNodeId()) {
            pageReqVO.setNodeId(null);
            PageResult<ImportTaskDO> importTaskDOPageResult = importTaskMapper.selectPage(pageReqVO);
            return importTaskDOPageResult;
        }
        //根据测点id和项目id获得所有子节点数据
        // 查询直接子节点
        ImportNodeDO importNodeDO = importNodeMapper.selectOne(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getId, pageReqVO.getNodeId()));
        List<ImportNodeDO> allChildNodes = getAllChildNodes(pageReqVO.getNodeId());
        allChildNodes.add(importNodeDO);
        long total = 0;
        List<ImportTaskDO> allTasks = new ArrayList<>();
        // 遍历每个节点查询任务
        for (ImportNodeDO node : allChildNodes) {
            ImportTaskPageReqVO importTaskPageReqVO = BeanUtils.toBean(pageReqVO, ImportTaskPageReqVO.class);
            importTaskPageReqVO.setNodeId(node.getId());
//            importTaskPageReqVO.setExecuteTime(pageReqVO.getExecuteTime());
            LambdaQueryWrapper<ImportTaskDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ImportTaskDO::getNodeId, node.getId());
            queryWrapper.eq(ImportTaskDO::getProjectId, pageReqVO.getProjectId());
            if (pageReqVO.getTaskStatus() != null) {
                queryWrapper.eq(ImportTaskDO::getTaskStatus, pageReqVO.getTaskStatus());
            }
            if (pageReqVO.getExecuteTime() != null && pageReqVO.getExecuteTime().length == 2) {
                queryWrapper.between(ImportTaskDO::getExecuteTime,
                        pageReqVO.getExecuteTime()[0],
                        pageReqVO.getExecuteTime()[1]);
            }
            if (pageReqVO.getFileName()!= null) {
                queryWrapper.like(ImportTaskDO::getFileName, pageReqVO.getFileName());
            }
            List<ImportTaskDO> importTaskDOS = importTaskMapper.selectList(queryWrapper);
            if (importTaskDOS.size()>0) {
                allTasks.addAll(importTaskDOS);
                total += importTaskDOS.size();
            }
        }
        // 按执行时间倒序排序
        allTasks.sort(Comparator.comparing(ImportTaskDO::getExecuteTime).reversed());
        // 处理分页
        int pageSize = pageReqVO.getPageSize();
        int pageNo = pageReqVO.getPageNo();
        int fromIndex = (pageNo - 1) * pageSize;
        if (fromIndex >= allTasks.size()) {
            return new PageResult<>(Collections.emptyList(), total);
        }
        int toIndex = Math.min(fromIndex + pageSize, allTasks.size());
        List<ImportTaskDO> pagedTasks = allTasks.subList(fromIndex, toIndex);

        return new PageResult<>(pagedTasks, total);
    }
    /**
     * 获取指定节点下的所有子节点（递归）
     *
     * @param parentId  父节点ID
     * @return 子节点列表
     */
    private List<ImportNodeDO> getAllChildNodes(Long parentId) {
        List<ImportNodeDO> result = new ArrayList<>();
        // 查询直接子节点
        List<ImportNodeDO> directChildren = importNodeMapper.selectList(new LambdaQueryWrapper<ImportNodeDO>()
                .eq(ImportNodeDO::getParentId, parentId));
        if (directChildren != null && !directChildren.isEmpty()) {
            result.addAll(directChildren);
            for (ImportNodeDO child : directChildren) {
                // 递归查询子节点的子节点
                result.addAll(getAllChildNodes( child.getId()));
            }
        }
        return result;
    }
}