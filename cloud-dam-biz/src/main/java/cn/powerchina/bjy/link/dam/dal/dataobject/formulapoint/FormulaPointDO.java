package cn.powerchina.bjy.link.dam.dal.dataobject.formulapoint;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 公式关联测点 DO
 *
 * <AUTHOR>
 */
@TableName("dam_formula_point")
@KeySequence("dam_formula_point_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormulaPointDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 测点公式id
     */
    private Long pointFormulaId;
    /**
     * 关联测点id
     */
    private Long pointId;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 数据范围，1：全部，2：自动化，3：人工
     */
    private Integer applyType;
    /**
     * 取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值
     */
    private Integer dataCondition;
    /**
     * 数值或1：日统计值，2：月统计值
     */
    private Integer dataValue;
    /**
     * 单位，1：分钟，2：小时，3：天或1：平均值，2：最大值，3：最小值，4：累计值
     */
    private Integer dataUnit;

}