package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct.IotProductDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproductmodel.IotProductModelDO;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttModelDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import cn.powerchina.bjy.link.dam.service.iotproduct.IotProductService;
import cn.powerchina.bjy.link.dam.service.iotproductmodel.IotProductModelService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.product.Product;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.product.ProductModel;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;

/**
 * 产品更新消息处理器
 */
@Component
@Slf4j
public class ProductUpdateMsgProcessor implements MsgProcessor {

    @Autowired
    private IotProductService iotProductService;

    @Autowired
    private IotProductModelService iotProductModelService;

    @Autowired
    private AuthProductService authProductService;

    /**
     * 使用一个 Map 来存储所有可能处理产品物模型的操作策略
     * key: mqtt发过来的消息中的modelDataType
     * value: 处理该操作的策略
     */
    private final Map<String, Consumer<Product>> operations = new HashMap<>();

    /**
     * 注册所有处理产品物模型的策略
     */
    public ProductUpdateMsgProcessor() {
        // 注册创建产品物模型的策略
        operations.put(MqttModelDataTypeEnum.PRODUCT_MODEL_CREATE.getModelDataType(), this::handleModelCreate);

        // 注册更新产品物模型的策略
        operations.put(MqttModelDataTypeEnum.PRODUCT_MODEL_UPDATE.getModelDataType(), this::handleModelUpdate);

        // 注册删除产品物模型的策略
        operations.put(MqttModelDataTypeEnum.PRODUCT_MODEL_DELETE.getModelDataType(), this::handleModelDelete);
    }

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    @Transactional
    public void process(String payload) {
        MqttReceiveData<Product> productData = JsonUtils.parseObject(payload, new TypeReference<>() {
        });
        Product product = productData.getMessage();

        // 更新物联网平台同步的产品
        IotProductDO iotProductDO = iotProductService.getByIotId(product.getId());
        if (Objects.nonNull(iotProductDO)) {
            if (Objects.nonNull(product.getId())) {
                iotProductDO.setIotId(product.getId());
            }
            if (Objects.nonNull(product.getResourceSpaceId())) {
                iotProductDO.setResourceSpaceId(product.getResourceSpaceId());
            }
            if (StringUtils.isNotBlank(product.getProductName())) {
                iotProductDO.setProductName(product.getProductName());
            }
            if (StringUtils.isNotBlank(product.getProductCode())) {
                iotProductDO.setProductCode(product.getProductCode());
            }
            if (StringUtils.isNotBlank(product.getProductModel())) {
                iotProductDO.setProductModel(product.getProductModel());
            }
            if (StringUtils.isNotBlank(product.getFirmName())) {
                iotProductDO.setFirmName(product.getFirmName());
            }
            if (StringUtils.isNotBlank(product.getDescription())) {
                iotProductDO.setDescription(product.getDescription());
            }
            if (Objects.nonNull(product.getNodeType())) {
                iotProductDO.setNodeType(product.getNodeType());
            }
            if (StringUtils.isNotBlank(product.getProtocolCode())) {
                iotProductDO.setProtocolCode(product.getProtocolCode());
            }
            if (StringUtils.isNotBlank(product.getNetworkMethod())) {
                iotProductDO.setNetworkMethod(product.getNetworkMethod());
            }
            if (StringUtils.isNotBlank(product.getDataFormat())) {
                iotProductDO.setDataFormat(product.getDataFormat());
            }
            if (Objects.nonNull(product.getProductState())) {
                iotProductDO.setProductState(product.getProductState());
            }
            if (StringUtils.isNotBlank(product.getProductSecret())) {
                iotProductDO.setProductSecret(product.getProductSecret());
            }
            if (StringUtils.isNotBlank(product.getCreateTime())) {
                LocalDateTime iotCreateTime = Optional.ofNullable(product.getCreateTime()).map(LocalDateTime::parse)
                        .orElse(null);
                iotProductDO.setIotCreateTime(iotCreateTime);
            }
            iotProductService.updateByIotId(iotProductDO);

            // 更新物联网平台同步的产品物模型。虽然productModeList是个集合，但里面只有一条数据，所以在循环里面操作的数据库
            List<ProductModel> productModelList = product.getProductModelList();
            productModelList.forEach(productModel -> {
                Consumer<Product> operation = operations.getOrDefault(productModel.getModelDataType(), this::handleUnknown);
                operation.accept(product);
            });

            // 更新产品授权
            String productCode = StringUtils.isNotBlank(product.getProductCode())?product.getProductCode():iotProductDO.getProductCode();
            AuthProductDO authProductDO = authProductService.getByProductCode(productCode);
            if (Objects.nonNull(authProductDO)) {
                if (StringUtils.isNotBlank(product.getProductName())) {
                    authProductDO.setProductName(product.getProductName());
                }
                if (StringUtils.isNotBlank(product.getFirmName())) {
                    authProductDO.setFirmName(product.getFirmName());
                }
                if (StringUtils.isNotBlank(product.getProductModel())) {
                    authProductDO.setProductModel(product.getProductModel());
                }
                if (Objects.nonNull(product.getNodeType())) {
                    authProductDO.setNodeType(product.getNodeType());
                }
                if (StringUtils.isNotBlank(product.getProductCode())) {
                    authProductDO.setProductCode(product.getProductCode());
                }
                authProductService.updateByProductCode(authProductDO);
            }
        }
    }

    /**
     * 获取当前消息处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.PRODUCT_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.PRODUCT_UPDATE.getDataType();
    }

    /**
     * 产品物模型新增策略
     * @param product 产品
     */
    private void handleModelCreate(Product product) {
        ProductModel productModel = product.getProductModelList().get(0);
        Long iotProductIotId = product.getId();

        IotProductModelDO iotProductModelDO = new IotProductModelDO();
        iotProductModelDO.setIotId(productModel.getId());
        iotProductModelDO.setIotProductIotId(iotProductIotId);
        iotProductModelDO.setProductCode(productModel.getProductCode());
        iotProductModelDO.setThingIdentity(productModel.getThingIdentity());
        iotProductModelDO.setThingName(productModel.getThingName());
        iotProductModelDO.setThingType(productModel.getThingType());
        iotProductModelDO.setDatatype(productModel.getDatatype());
        iotProductModelDO.setReadWriteType(productModel.getReadWriteType());
        iotProductModelDO.setEventType(productModel.getEventType());
        iotProductModelDO.setInputParams(productModel.getInputParams());
        iotProductModelDO.setOutputParams(productModel.getOutputParams());
        iotProductModelDO.setExtra(productModel.getExtra());
        iotProductModelDO.setRemark(productModel.getRemark());
        iotProductModelDO.setIotCreateTime(LocalDateTime.parse(productModel.getCreateTime()));

        iotProductModelService.createIotProductModel(iotProductModelDO);
    }

    /**
     * 产品物模型修改策略
     * @param product 产品
     */
    private void handleModelUpdate(Product product) {
        ProductModel productModel = product.getProductModelList().get(0);

        IotProductModelDO iotProductModelDO = iotProductModelService.getByIotId(productModel.getId());
        if (Objects.nonNull(iotProductModelDO)) {
            if (Objects.nonNull(productModel.getId())) {
                iotProductModelDO.setIotId(productModel.getId());
            }
            //iotProductModelDO.setIotProductIotId(product.getId());
            if (StringUtils.isNotBlank(productModel.getProductCode())) {
                iotProductModelDO.setProductCode(productModel.getProductCode());
            }
            if (StringUtils.isNotBlank(productModel.getThingIdentity())) {
                iotProductModelDO.setThingIdentity(productModel.getThingIdentity());
            }
            if (StringUtils.isNotBlank(productModel.getThingName())) {
                iotProductModelDO.setThingName(productModel.getThingName());
            }
            if (Objects.nonNull(productModel.getThingType())) {
                iotProductModelDO.setThingType(productModel.getThingType());
            }
            if (StringUtils.isNotBlank(productModel.getDatatype())) {
                iotProductModelDO.setDatatype(productModel.getDatatype());
            }
            if (Objects.nonNull(productModel.getReadWriteType())) {
                iotProductModelDO.setReadWriteType(productModel.getReadWriteType());
            }
            //iotProductModelDO.setEventType(productModel.getEventType());
            if (StringUtils.isNotBlank(productModel.getInputParams())) {
                iotProductModelDO.setInputParams(productModel.getInputParams());
            }
            if (StringUtils.isNotBlank(productModel.getOutputParams())) {
                iotProductModelDO.setOutputParams(productModel.getOutputParams());
            }
            if (StringUtils.isNotBlank(productModel.getExtra())) {
                iotProductModelDO.setExtra(productModel.getExtra());
            }
            if (StringUtils.isNotBlank(productModel.getRemark())) {
                iotProductModelDO.setRemark(productModel.getRemark());
            }
            if (StringUtils.isNotBlank(productModel.getCreateTime())) {
                iotProductModelDO.setIotCreateTime(LocalDateTime.parse(productModel.getCreateTime()));
            }

            iotProductModelService.updateByIotId(iotProductModelDO);
        }
    }

    /**
     * 产品物模型删除策略
     * @param product 产品物模型
     */
    private void handleModelDelete(Product product) {
        ProductModel productModel = product.getProductModelList().get(0);

        iotProductModelService.deleteByIotId(productModel.getId());
    }

    /**
     * 产品物模型未知操作策略
     * @param product 产品
     */
    private void handleUnknown(Product product) {
        ProductModel productModel = product.getProductModelList().get(0);

        log.error("未知的产品物模型操作类型: {}", productModel.getModelDataType());
    }
}
