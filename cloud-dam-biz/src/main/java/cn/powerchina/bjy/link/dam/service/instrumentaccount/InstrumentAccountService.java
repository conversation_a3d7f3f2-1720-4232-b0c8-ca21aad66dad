package cn.powerchina.bjy.link.dam.service.instrumentaccount;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountPageRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo.InstrumentAccountSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentaccount.InstrumentAccountDO;
import jakarta.validation.Valid;

/**
 * 仪器台账 Service 接口
 *
 * <AUTHOR>
 */
public interface InstrumentAccountService {

    /**
     * 创建仪器台账
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstrumentAccount(@Valid InstrumentAccountSaveReqVO createReqVO);

    /**
     * 更新仪器台账
     *
     * @param updateReqVO 更新信息
     */
    void updateInstrumentAccount(@Valid InstrumentAccountSaveReqVO updateReqVO);

    /**
     * 删除仪器台账
     *
     * @param id 编号
     */
    void deleteInstrumentAccount(Long id);

    /**
     * 获得仪器台账
     *
     * @param id 编号
     * @return 仪器台账
     */
    InstrumentAccountDO getInstrumentAccount(Long id);

    /**
     * 获得仪器台账分页
     *
     * @param pageReqVO 分页查询
     * @return 仪器台账分页
     */
    PageResult<InstrumentAccountPageRespVO> getInstrumentAccountPage(InstrumentAccountPageReqVO pageReqVO);

}