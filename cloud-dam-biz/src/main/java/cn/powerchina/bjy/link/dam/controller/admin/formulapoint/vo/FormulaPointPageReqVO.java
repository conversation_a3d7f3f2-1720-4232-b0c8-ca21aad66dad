package cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 公式关联测点分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FormulaPointPageReqVO extends PageParam {

    @Schema(description = "测点公式id", example = "11278")
    private Long pointFormulaId;

    @Schema(description = "关联测点id", example = "23886")
    private Long pointId;

    @Schema(description = "分量id", example = "26093")
    private Long instrumentModelId;

    @Schema(description = "数据范围，1：全部，2：自动化，3：人工", example = "2")
    private Integer applyType;

    @Schema(description = "取值条件，1：时间范围内测值，2：当前时间的测值，3：之前最近的测值，4：前几个小时的累计值，5：统计值")
    private Integer dataCondition;

    @Schema(description = "数值或1：日统计值，2：月统计值")
    private Integer dataValue;

    @Schema(description = "单位，1：分钟，2：小时，3：天或1：平均值，2：最大值，3：最小值，4：累计值")
    private Integer dataUnit;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}