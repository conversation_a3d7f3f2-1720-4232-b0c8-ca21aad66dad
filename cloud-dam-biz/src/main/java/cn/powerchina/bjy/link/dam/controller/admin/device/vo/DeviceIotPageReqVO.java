package cn.powerchina.bjy.link.dam.controller.admin.device.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 查询未绑定设备")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceIotPageReqVO extends PageParam {

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备唯一标识id")
    private String deviceSerial;

}