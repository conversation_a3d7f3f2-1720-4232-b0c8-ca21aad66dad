package cn.powerchina.bjy.link.dam.dal.mysql.indexhead;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexhead.IndexHeadDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 大坝首页头部信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IndexHeadMapper extends BaseMapperX<IndexHeadDO> {

    default PageResult<IndexHeadDO> selectPage(IndexHeadPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<IndexHeadDO>()
                .eqIfPresent(IndexHeadDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(IndexHeadDO::getPointDeviceTotal, reqVO.getPointDeviceTotal())
                .eqIfPresent(IndexHeadDO::getPointDeviceToday, reqVO.getPointDeviceToday())
                .eqIfPresent(IndexHeadDO::getPointDataTotal, reqVO.getPointDataTotal())
                .eqIfPresent(IndexHeadDO::getPointDataToday, reqVO.getPointDataToday())
                .eqIfPresent(IndexHeadDO::getDeviceMcuTotal, reqVO.getDeviceMcuTotal())
                .eqIfPresent(IndexHeadDO::getDeviceMcuToday, reqVO.getDeviceMcuToday())
                .eqIfPresent(IndexHeadDO::getDeviceMcuRunTotal, reqVO.getDeviceMcuRunTotal())
                .eqIfPresent(IndexHeadDO::getDeviceMcuRunToday, reqVO.getDeviceMcuRunToday())
                .eqIfPresent(IndexHeadDO::getPointDataMcuTotal, reqVO.getPointDataMcuTotal())
                .eqIfPresent(IndexHeadDO::getPointDataMcuToday, reqVO.getPointDataMcuToday())
                .eqIfPresent(IndexHeadDO::getGenerateTime,reqVO.getGenerateTime())
                .betweenIfPresent(IndexHeadDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(IndexHeadDO::getId));
    }

}
