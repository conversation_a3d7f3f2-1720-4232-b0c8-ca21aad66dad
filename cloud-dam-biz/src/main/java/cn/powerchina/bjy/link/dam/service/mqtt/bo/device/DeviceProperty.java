package cn.powerchina.bjy.link.dam.service.mqtt.bo.device;

import lombok.Data;

import java.util.List;

/**
 * @Description: 设备属性上报
 * @Author: AI Assistant
 * @CreateDate: 2025/8/1
 */
@Data
public class DeviceProperty {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 属性值集合
     */
    private List<DevicePropertyService> services;

    private String thingIdentity;

}
