package cn.powerchina.bjy.link.dam.service.project;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.project.bo.ProjectBO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 项目管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectService {

    /**
     * 创建项目管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProject(@Valid ProjectSaveReqVO createReqVO);

    /**
     * 更新项目管理
     *
     * @param updateReqVO 更新信息
     */
    void updateProject(@Valid ProjectSaveReqVO updateReqVO);

    /**
     * 删除项目管理
     *
     * @param id 编号
     */
    void deleteProject(Long id);

    /**
     * 获得项目管理
     *
     * @param id 编号
     * @return 项目管理
     */
    ProjectDO getProject(Long id);

    /**
     * 获得项目管理
     *
     * @param id 编号
     * @return 项目管理
     */
    ProjectBO getProjectBO(Long id);

    /**
     * 获得项目管理分页
     *
     * @param pageReqVO 分页查询
     * @return 项目管理分页
     */
    PageResult<ProjectDO> getProjectPage(ProjectPageReqVO pageReqVO);

    /**
     * 获得项目管理分页BO
     *
     * @param pageReqVO 分页查询
     * @return 项目管理分页
     */
    PageResult<ProjectBO> getProjectBOPage(ProjectPageReqVO pageReqVO);

    /**
     * 校验项目管理是否存在
     *
     * @param id
     */
    ProjectDO validateProjectExists(Long id);

    /**
     * 获取树列表
     *
     * @return
     */
    List<ProjectDO> getProjectList();

    List<ProjectDO> getAllProjectList();


    List<ProjectDO> getProjectListByUserId(Long userId);
}