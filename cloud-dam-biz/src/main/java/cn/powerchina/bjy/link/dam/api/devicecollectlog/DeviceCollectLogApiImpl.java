package cn.powerchina.bjy.link.dam.api.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.service.devicecollectlog.DeviceCollectLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 采集仪执行计划
 * @Author: yhx
 * @CreateDate: 2024/9/27
 */
@RestController
public class DeviceCollectLogApiImpl implements DeviceCollectLogApi {

    @Autowired
    private DeviceCollectLogService deviceCollectLogService;

    @Override
    public CommonResult<Boolean> runDeviceCollectLog() {
        deviceCollectLogService.runDeviceCollectLog();
        return CommonResult.success(true);
    }
}
