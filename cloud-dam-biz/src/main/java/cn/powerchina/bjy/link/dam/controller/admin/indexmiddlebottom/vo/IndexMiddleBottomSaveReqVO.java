package cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 首页中间和底部数据新增/修改 Request VO")
@Data
public class IndexMiddleBottomSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28350")
    private Long id;

    @Schema(description = "项目id", example = "15150")
    private Long projectId;

    @Schema(description = "仪器类型名称", example = "赵六")
    private String instrumentName;

    @Schema(description = "仪器数量", example = "6337")
    private Long instrumentCount;

    @Schema(description = "仪器占比%")
    private String instrumentRate;

    @Schema(description = "观测记录数量", example = "20780")
    private Long pointDataCount;

    @Schema(description = "最近观测时间")
    private LocalDateTime pointTimeRecent;

    @Schema(description = "最早观测时间")
    private LocalDateTime pointTimeFirst;

    @Schema(description = "生成时间")
    private String generateTime;

}
