package cn.powerchina.bjy.link.dam.service.formulamodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 公式关联分量 Service 接口
 *
 * <AUTHOR>
 */
public interface FormulaModelService {

    /**
     * 创建公式关联分量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFormulaModel(@Valid FormulaModelSaveReqVO createReqVO);

    /**
     * 更新公式关联分量
     *
     * @param updateReqVO 更新信息
     */
    void updateFormulaModel(@Valid FormulaModelSaveReqVO updateReqVO);

    /**
     * 删除公式关联分量
     *
     * @param id 编号
     */
    void deleteFormulaModel(Long id);

    /**
     * 获得公式关联分量
     *
     * @param id 编号
     * @return 公式关联分量
     */
    FormulaModelRespVO getFormulaModel(Long id);

    /**
     * 获得公式关联分量分页
     *
     * @param pageReqVO 分页查询
     * @return 公式关联分量分页
     */
    PageResult<FormulaModelDO> getFormulaModelPage(FormulaModelPageReqVO pageReqVO);

    /**
     * 根据测点公式id查询关联分量列表
     *
     * @param pointFormulaId
     * @return
     */
    List<FormulaModelDO> getFormulaModelByPointFormulaId(Long pointFormulaId);

}