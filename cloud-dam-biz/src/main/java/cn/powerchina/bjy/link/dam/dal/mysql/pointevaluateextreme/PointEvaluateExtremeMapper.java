package cn.powerchina.bjy.link.dam.dal.mysql.pointevaluateextreme;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo.PointEvaluateExtremePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluateextreme.PointEvaluateExtremeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测点评价指标极值 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointEvaluateExtremeMapper extends BaseMapperX<PointEvaluateExtremeDO> {

    default PageResult<PointEvaluateExtremeDO> selectPage(PointEvaluateExtremePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PointEvaluateExtremeDO>()
                .eqIfPresent(PointEvaluateExtremeDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(PointEvaluateExtremeDO::getPointId, reqVO.getPointId())
                .eqIfPresent(PointEvaluateExtremeDO::getInstrumentModelId, reqVO.getInstrumentModelId())
                .betweenIfPresent(PointEvaluateExtremeDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(PointEvaluateExtremeDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PointEvaluateExtremeDO::getDataType, reqVO.getDataType())
                .eqIfPresent(PointEvaluateExtremeDO::getExtremeType, reqVO.getExtremeType())
                .eqIfPresent(PointEvaluateExtremeDO::getAbnormalDownType, reqVO.getAbnormalDownType())
                .eqIfPresent(PointEvaluateExtremeDO::getAbnormalDownSymbol, reqVO.getAbnormalDownSymbol())
                .eqIfPresent(PointEvaluateExtremeDO::getAbnormalDown, reqVO.getAbnormalDown())
                .eqIfPresent(PointEvaluateExtremeDO::getWaringDownType, reqVO.getWaringDownType())
                .eqIfPresent(PointEvaluateExtremeDO::getWaringDownSymbol, reqVO.getWaringDownSymbol())
                .eqIfPresent(PointEvaluateExtremeDO::getWaringDown, reqVO.getWaringDown())
                .eqIfPresent(PointEvaluateExtremeDO::getAbnormalUpType, reqVO.getAbnormalUpType())
                .eqIfPresent(PointEvaluateExtremeDO::getAbnormalUpSymbol, reqVO.getAbnormalUpSymbol())
                .eqIfPresent(PointEvaluateExtremeDO::getAbnormalUp, reqVO.getAbnormalUp())
                .eqIfPresent(PointEvaluateExtremeDO::getWaringUpType, reqVO.getWaringUpType())
                .eqIfPresent(PointEvaluateExtremeDO::getWaringUpSymbol, reqVO.getWaringUpSymbol())
                .eqIfPresent(PointEvaluateExtremeDO::getWaringUp, reqVO.getWaringUp())
                .betweenIfPresent(PointEvaluateExtremeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PointEvaluateExtremeDO::getId));
    }

}