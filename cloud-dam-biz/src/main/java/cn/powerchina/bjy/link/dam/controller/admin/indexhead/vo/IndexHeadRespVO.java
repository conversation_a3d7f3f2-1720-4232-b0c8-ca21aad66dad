package cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 大坝首页头部信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class IndexHeadRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "340")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目id", example = "4887")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "监测仪器数量")
    @ExcelProperty("监测仪器数量")
    private Long pointDeviceTotal;

    @Schema(description = "监测仪器今日新增数量")
    @ExcelProperty("监测仪器今日新增数量")
    private Long pointDeviceToday;

    @Schema(description = "监测数据数量")
    @ExcelProperty("监测数据数量")
    private Long pointDataTotal;

    @Schema(description = "监测数据今日新增数量")
    @ExcelProperty("监测数据今日新增数量")
    private Long pointDataToday;

    @Schema(description = "自动化设备数量")
    @ExcelProperty("自动化设备数量")
    private Long deviceMcuTotal;

    @Schema(description = "自动化设备今日新增数量")
    @ExcelProperty("自动化设备今日新增数量")
    private Long deviceMcuToday;

    @Schema(description = "在测自动化仪器数量")
    @ExcelProperty("在测自动化仪器数量")
    private Long deviceMcuRunTotal;

    @Schema(description = "在测自动化仪器今日新增数量")
    @ExcelProperty("在测自动化仪器今日新增数量")
    private Long deviceMcuRunToday;

    @Schema(description = "自动化监测数据数量")
    @ExcelProperty("自动化监测数据数量")
    private Long pointDataMcuTotal;

    @Schema(description = "自动化监测数据今日新增数量")
    @ExcelProperty("自动化监测数据今日新增数量")
    private Long pointDataMcuToday;

    @Schema(description = "生成时间")
    private String generateTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
