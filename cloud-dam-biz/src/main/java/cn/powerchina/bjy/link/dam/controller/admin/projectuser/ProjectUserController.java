package cn.powerchina.bjy.link.dam.controller.admin.projectuser;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.LoginReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectuser.vo.ProjectUserSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserPasswordVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectuser.ProjectUserDO;
import cn.powerchina.bjy.link.dam.service.projectuser.ProjectUserService;
import cn.powerchina.bjy.link.dam.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 项目用户")
@RestController
@RequestMapping("/dam/project/user")
@Validated
public class ProjectUserController {

    @Resource
    private ProjectUserService projectUserService;

    @Resource
    private UserService userService;

    @PostMapping("/create")
    @Operation(summary = "创建项目用户")
    //@PreAuthorize("@ss.hasPermission('dam:project-user:create')")
    public CommonResult<Long> createProjectUser(@Valid @RequestBody ProjectUserSaveReqVO createReqVO) {
        return success(projectUserService.createProjectUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新项目用户")
    // @PreAuthorize("@ss.hasPermission('dam:project-user:update')")
    public CommonResult<Boolean> updateProjectUser(@Valid @RequestBody ProjectUserSaveReqVO updateReqVO) {
        projectUserService.updateProjectUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除项目用户")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('dam:project-user:delete')")
    public CommonResult<Boolean> deleteProjectUser(@RequestParam("id") Long id) {
        projectUserService.deleteProjectUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "projectId", description = "当前项目id", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('dam:project-user:query')")
    public CommonResult<ProjectUserRespVO> getProjectUser(@RequestParam("id") Long id, @RequestParam("projectId") Long projectId) {
        UserBO userBO = projectUserService.getProjectUser(id, projectId);
        return success(BeanUtils.toBean(userBO, ProjectUserRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得项目用户分页")
    //@PreAuthorize("@ss.hasPermission('dam:project-user:query')")
    public CommonResult<PageResult<UserRespVO>> getProjectUserPage(@Valid ProjectUserPageReqVO pageReqVO) {
        PageResult<UserBO> pageResult = projectUserService.getProjectUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserRespVO.class));
    }

    @PutMapping("/update-password")
    @Operation(summary = "更新密码")
    //@PreAuthorize("@ss.hasPermission('dam:project-user:update')")
    public CommonResult<Boolean> updatePassword(@Valid @RequestBody UserPasswordVO userPasswordVO) {
        userService.updatePassword(userPasswordVO);
        return success(true);
    }

    @PostMapping("/login")
    @Operation(summary = "使用账号密码登录，检查登录用户是否绑定项目")
    public CommonResult<Boolean> login(@RequestBody @Valid LoginReqVO reqVO) {
        return success(projectUserService.login(reqVO));
    }
}