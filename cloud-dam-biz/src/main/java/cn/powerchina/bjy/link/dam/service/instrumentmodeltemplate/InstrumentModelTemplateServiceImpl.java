package cn.powerchina.bjy.link.dam.service.instrumentmodeltemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodeltemplate.vo.InstrumentModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodeltemplate.InstrumentModelTemplateDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodeltemplate.InstrumentModelTemplateMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumenttemplate.InstrumentTemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INSTRUMENT_MODEL_TEMPLATE_NOT_EXISTS;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.INSTRUMENT_NOT_EXISTS;


/**
 * 仪器类型模板-测量分量 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstrumentModelTemplateServiceImpl implements InstrumentModelTemplateService {

    @Resource
    private InstrumentModelTemplateMapper instrumentModelTemplateMapper;

    @Resource
    private InstrumentTemplateMapper instrumentTemplateMapper;

    @Override
    public Long createInstrumentModelTemplate(InstrumentModelTemplateSaveReqVO createReqVO) {
        // 插入
        InstrumentModelTemplateDO instrumentModelTemplate = BeanUtils.toBean(createReqVO, InstrumentModelTemplateDO.class);
        instrumentModelTemplateMapper.insert(instrumentModelTemplate);
        // 返回
        return instrumentModelTemplate.getId();
    }

    @Override
    public void updateInstrumentModelTemplate(InstrumentModelTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateInstrumentModelTemplateExists(updateReqVO.getId());
        // 更新
        InstrumentModelTemplateDO updateObj = BeanUtils.toBean(updateReqVO, InstrumentModelTemplateDO.class);
        instrumentModelTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteInstrumentModelTemplate(Long id) {
        // 校验存在
        validateInstrumentModelTemplateExists(id);
        // 删除
        instrumentModelTemplateMapper.deleteById(id);
    }

    private void validateInstrumentModelTemplateExists(Long id) {
        if (instrumentModelTemplateMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_MODEL_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public InstrumentModelTemplateDO getInstrumentModelTemplate(Long id) {
        return instrumentModelTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<InstrumentModelTemplateDO> getInstrumentModelTemplatePage(InstrumentModelTemplatePageReqVO pageReqVO) {
        return instrumentModelTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InstrumentModelTemplateDO> getModelByInstrumentId(long templateId) {
        validateInstrumentExists(templateId);
        return instrumentModelTemplateMapper.selectList(new LambdaQueryWrapperX<InstrumentModelTemplateDO>().
                eq(InstrumentModelTemplateDO::getInstrumentId, templateId).orderByAsc(InstrumentModelTemplateDO::getThingWeight));
    }
    private void validateInstrumentExists(Long id) {
        if (instrumentTemplateMapper.selectById(id) == null) {
            throw exception(INSTRUMENT_NOT_EXISTS);
        }
    }
}