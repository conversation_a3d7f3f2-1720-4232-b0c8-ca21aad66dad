package cn.powerchina.bjy.link.dam.service.authproduct;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.bo.AuthProductBO;
import cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo.AuthProductPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;

import java.util.List;

/**
 * 产品授权 Service 接口
 *
 * <AUTHOR>
 */
public interface AuthProductService {

    /**
     * 获得产品授权
     *
     * @param id 编号
     * @return 产品授权
     */
    AuthProductDO getAuthProduct(Long id);

    /**
     * 获得产品授权分页
     *
     * @param pageReqVO 分页查询
     * @return 产品授权分页
     */
    PageResult<AuthProductDO> getAuthProductPage(AuthProductPageReqVO pageReqVO);

    /**
     * 根据产品编码查找
     *
     * @param projectId
     * @param productCode
     * @return
     */
    AuthProductDO findProductByProjectIdProductCode(Long projectId, String productCode);

    /**
     * 保存产品授权
     *
     * @param projectId
     * @param productCodeList
     */
    void addAuthProductByProductCode(Long projectId, List<String> productCodeList);

    /**
     * 查询产品授权下拉
     *
     * @return
     */
    List<AuthProductBO> getAuthProductSelectList(Long projectId);

    /**
     * 根据项目id查询产品授权列表
     *
     * @return
     */
    List<AuthProductDO> getAuthProductListByProjectId(Long projectId);

    void deleteAuthProductByProductCode(Long projectId, List<String> productCodeList);

    /**
     * 根据产品编码删除产品授权
     *
     * @param productCode 产品编码
     */
    void deleteByProductCode(String productCode);

    /**
     * 根据产品编码获取产品授权
     *
     * @param productCode 产品编码
     * @return 产品授权
     */
    AuthProductDO getByProductCode(String productCode);

    /**
     * 根据产品编码更新产品授权
     *
     * @param authProductDO 产品授权信息
     */
    void updateByProductCode(AuthProductDO authProductDO);
}