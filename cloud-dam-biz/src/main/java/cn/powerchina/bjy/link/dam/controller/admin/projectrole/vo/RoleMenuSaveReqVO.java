package cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 角色菜单保存 Request VO")
@Data
public class RoleMenuSaveReqVO {

    @Schema(description = "角色Id")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "菜单ID")
    @NotEmpty(message = "请至少选择一个菜单")
    private Set<Long> menuIds;

}