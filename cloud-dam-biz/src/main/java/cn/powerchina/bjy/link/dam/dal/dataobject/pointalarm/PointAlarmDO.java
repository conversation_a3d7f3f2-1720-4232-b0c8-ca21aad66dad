package cn.powerchina.bjy.link.dam.dal.dataobject.pointalarm;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测点报警信息 DO
 *
 * <AUTHOR>
 */
@TableName("dam_point_alarm")
@KeySequence("dam_point_alarm_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointAlarmDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 监测时间
     */
    private LocalDateTime pointTime;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 分量名称
     */
    private String thingName;
    /**
     * 测值
     */
    private String pointData;
    /**
     * 报警内容
     */
    private String alarmContent;
    /**
     * 报警时间
     */
    private LocalDateTime alarmTime;
    /**
     * 处理状态，0：未处理，1：已处理
     */
    private Integer solutionStatus;
    /**
     * 处理情况
     */
    private String solutionContent;
    /**
     * 处理人id
     */
    private String solutionUserId;
    /**
     * 处理人姓名
     */
    private String solutionUserName;
    /**
     * 处理时间
     */
    private LocalDateTime solutionTime;

    /**
     * 告警类型
     */
    private Integer alarmType;

}