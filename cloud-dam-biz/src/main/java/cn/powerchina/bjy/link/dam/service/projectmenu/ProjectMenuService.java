package cn.powerchina.bjy.link.dam.service.projectmenu;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.bo.ProjectMenuBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectmenu.ProjectMenuDO;
import jakarta.validation.Valid;

/**
 * 项目菜单 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectMenuService {

    /**
     * 创建项目菜单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveProjectMenu(@Valid ProjectMenuSaveReqVO createReqVO);

    /**
     * 获得项目菜单
     *
     * @param id 编号
     * @return 项目菜单
     */
    ProjectMenuDO getProjectMenu(Long id);

    /**
     * 获得项目菜单分页
     *
     * @param pageReqVO 分页查询
     * @return 项目菜单分页
     */
    PageResult<ProjectMenuDO> getProjectMenuPage(ProjectMenuPageReqVO pageReqVO);

    /**
     * 根据项目id获取菜单id
     *
     * @param projectId
     * @return
     */
    ProjectMenuBO getProjectMenuBOByProjectId(Long projectId, Long roleId);
}