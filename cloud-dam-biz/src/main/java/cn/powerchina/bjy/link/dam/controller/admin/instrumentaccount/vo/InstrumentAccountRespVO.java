package cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器台账 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentAccountRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13637")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "4758")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "生产编号")
    @ExcelProperty("生产编号")
    private String productionNumber;

    @Schema(description = "仪器型号")
    @ExcelProperty("仪器型号")
    private String instrumentModel;

    @Schema(description = "仪器类型id", example = "28404")
    @ExcelProperty("仪器类型id")
    private Long instrumentId;

    @Schema(description = "生产厂商")
    @ExcelProperty("生产厂商")
    private String instrumentManufacturer;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remarks;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}