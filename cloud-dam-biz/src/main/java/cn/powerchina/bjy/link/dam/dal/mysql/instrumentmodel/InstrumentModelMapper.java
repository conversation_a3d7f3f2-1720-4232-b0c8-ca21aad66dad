package cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo.InstrumentModelPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仪器类型-测量分量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentModelMapper extends BaseMapperX<InstrumentModelDO> {

    default PageResult<InstrumentModelDO> selectPage(InstrumentModelPageReqVO reqVO) {
        LambdaQueryWrapper<InstrumentModelDO> wrapper = new LambdaQueryWrapperX<InstrumentModelDO>()
                .eqIfPresent(InstrumentModelDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(InstrumentModelDO::getInstrumentId, reqVO.getInstrumentId())
                .likeIfPresent(InstrumentModelDO::getThingName, reqVO.getThingName());
        if (reqVO.getCalcFormula() != null && reqVO.getCalcFormula()) {
            wrapper.in(InstrumentModelDO::getThingType, reqVO.getThingTypeList());
        }
        wrapper.orderByAsc(InstrumentModelDO::getThingWeight);
        wrapper.orderByDesc(InstrumentModelDO::getCreateTime);
        return selectPage(reqVO, wrapper);
    }

}