package cn.powerchina.bjy.link.dam.controller.admin.projectmenu;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.bo.ProjectMenuBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectmenu.vo.ProjectMenuSaveReqVO;
import cn.powerchina.bjy.link.dam.service.projectmenu.ProjectMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 项目菜单")
@RestController
@RequestMapping("/dam/project/menu")
@Validated
public class ProjectMenuController {

    @Resource
    private ProjectMenuService projectMenuService;

    @PostMapping("/save")
    @Operation(summary = "保存项目菜单")
//    @PreAuthorize("@ss.hasPermission('dam:project-menu:create')")
    public CommonResult<Long> createProjectMenu(@Valid @RequestBody ProjectMenuSaveReqVO createReqVO) {
        return success(projectMenuService.saveProjectMenu(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目菜单")
//    @PreAuthorize("@ss.hasPermission('dam:project-menu:query')")
    @Parameter(name = "projectId", description = "项目id", required = true)
    @Parameter(name = "roleId", description = "角色id，在项目角色管理时才传", required = false)
    public CommonResult<ProjectMenuRespVO> getProjectMenu(@RequestParam("projectId") Long projectId,
                                                          @RequestParam(value = "roleId", required = false) Long roleId) {
        ProjectMenuBO projectMenu = projectMenuService.getProjectMenuBOByProjectId(projectId, roleId);
        return success(BeanUtils.toBean(projectMenu, ProjectMenuRespVO.class));
    }

//    @GetMapping("/page")
//    @Operation(summary = "获得项目菜单分页")
////    @PreAuthorize("@ss.hasPermission('dam:project-menu:query')")
//    public CommonResult<PageResult<ProjectMenuRespVO>> getProjectMenuPage(@Valid ProjectMenuPageReqVO pageReqVO) {
//        PageResult<ProjectMenuDO> pageResult = projectMenuService.getProjectMenuPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, ProjectMenuRespVO.class));
//    }

}