package cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备采集策略分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceStrategyPageReqVO extends PageParam {

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "策略名称")
    private String strategyName;

    @Schema(description = "采集方式，1：定点采集，2：间隔采集，3：跨天采集")
    private Integer strategyType;

    @Schema(description = "时间间隔")
    private String timeInterval;

    @Schema(description = "时间点（英文逗号分隔）")
    private String timePoint;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}