package cn.powerchina.bjy.link.dam.service.projectinfo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.bo.ProjectInfoBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo.ProjectInfoSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectinfo.ProjectInfoDO;
import cn.powerchina.bjy.link.dam.dal.mysql.projectinfo.ProjectInfoMapper;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.PROJECT_INFO_NOT_EXISTS;

/**
 * 项目工程信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectInfoServiceImpl implements ProjectInfoService {

    @Resource
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Override
    public Long saveProjectInfo(ProjectInfoSaveReqVO reqVO) {
        //校验项目是否存在
        projectService.validateProjectExists(reqVO.getProjectId());
        //查询当前项目是否存在项目信息
        ProjectInfoDO projectInfo = getProjectInfoByProjectId(reqVO.getProjectId());
        if (Objects.nonNull(projectInfo)) {
            projectInfo.setProjectInfo(reqVO.getProjectInfo());
            projectInfoMapper.updateById(projectInfo);
        } else {
            // 插入
            projectInfo = BeanUtils.toBean(reqVO, ProjectInfoDO.class);
            projectInfoMapper.insert(projectInfo);
        }
        return projectInfo.getProjectId();
    }

    @Override
    public void deleteProjectInfo(Long id) {
        // 校验存在
        validateProjectInfoExists(id);
        // 删除
        projectInfoMapper.deleteById(id);
    }

    private void validateProjectInfoExists(Long id) {
        if (projectInfoMapper.selectById(id) == null) {
            throw exception(PROJECT_INFO_NOT_EXISTS);
        }
    }

    @Override
    public ProjectInfoDO getProjectInfo(Long id) {
        return projectInfoMapper.selectById(id);
    }

    @Override
    public ProjectInfoDO getProjectInfoByProjectId(Long projectId) {
        return projectInfoMapper.selectOne(new LambdaQueryWrapperX<ProjectInfoDO>().eq(ProjectInfoDO::getProjectId, projectId));
    }

    @Override
    public ProjectInfoBO getProjectInfoBO(Long projectId) {
        ProjectInfoDO projectInfoDO = getProjectInfoByProjectId(projectId);
        if (Objects.nonNull(projectInfoDO)) {
            ProjectInfoBO projectInfoBO = new ProjectInfoBO();
            org.springframework.beans.BeanUtils.copyProperties(projectInfoDO, projectInfoBO);
            ProjectDO projectDO = projectService.getProject(projectInfoDO.getProjectId());
            projectInfoBO.setProjectName(Objects.nonNull(projectDO) ? projectDO.getProjectName() : null);
            return projectInfoBO;
        }
        return null;
    }

    @Override
    public PageResult<ProjectInfoDO> getProjectInfoPage(ProjectInfoPageReqVO pageReqVO) {
        return projectInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public void deleteProjectInfoByProjectId(Long projectId) {
        projectInfoMapper.delete(new LambdaQueryWrapperX<ProjectInfoDO>().eq(ProjectInfoDO::getProjectId, projectId));
    }

}