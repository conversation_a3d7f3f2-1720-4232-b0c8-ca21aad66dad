package cn.powerchina.bjy.link.dam.dal.mysql.indexmiddlebottom;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexmiddlebottom.IndexMiddleBottomDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 首页中间和底部数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IndexMiddleBottomMapper extends BaseMapperX<IndexMiddleBottomDO> {

    default PageResult<IndexMiddleBottomDO> selectPage(IndexMiddleBottomPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<IndexMiddleBottomDO>()
                .eqIfPresent(IndexMiddleBottomDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(IndexMiddleBottomDO::getInstrumentName, reqVO.getInstrumentName())
                .eqIfPresent(IndexMiddleBottomDO::getInstrumentCount, reqVO.getInstrumentCount())
                .eqIfPresent(IndexMiddleBottomDO::getInstrumentRate, reqVO.getInstrumentRate())
                .eqIfPresent(IndexMiddleBottomDO::getPointDataCount, reqVO.getPointDataCount())
                .eqIfPresent(IndexMiddleBottomDO::getPointTimeRecent, reqVO.getPointTimeRecent())
                .eqIfPresent(IndexMiddleBottomDO::getPointTimeFirst, reqVO.getPointTimeFirst())
                .betweenIfPresent(IndexMiddleBottomDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(IndexMiddleBottomDO::getId));
    }

}
