package cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点计算参数分页 Response VO")
@Data
public class PointEvaluatePageRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25970")
    private Long id;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "测点id")
    private Long pointId;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "仪器类型")
    private String instrumentName;

    @Schema(description = "测点状态")
    private Integer pointState;

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "分量单位")
    private String thingUnit;

    @Schema(description = "报警上限")
    private String waringUp;

    @Schema(description = "报警下限")
    private String waringDown;

    @Schema(description = "有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：人自一体，2：自动化，3：人工", example = "1")
    private Integer applyType;

    @Schema(description = "异常上限")
    private String abnormalUp;

    @Schema(description = "异常下限")
    private String abnormalDown;

    @Schema(description = "变化速率")
    private String changeRate;

    @Schema(description = "分量异常上限",hidden = true)
    private String modelAbnormalUp;

    @Schema(description = "分量异常下限", hidden = true)
    private String modelAbnormalDown;

    @Schema(description = "极值公式id")
    private Long evaluateExtremeId;

    @Schema(description = "默认的")
    private Boolean defaultEvaluate;

}
