package cn.powerchina.bjy.link.dam.service.authdevice;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.config.DamConfig;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.bo.AuthDeviceGroupBO;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthBindDeviceReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthDeviceSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authdevice.AuthDeviceDO;
import cn.powerchina.bjy.link.dam.dal.mysql.authdevice.AuthDeviceMapper;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.iot.api.devicegroup.DeviceGroupApi;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceGroupRespDTO;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 设备授权 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AuthDeviceServiceImpl implements AuthDeviceService {

    @Resource
    private AuthDeviceMapper authDeviceMapper;

    @Autowired
    private DeviceGroupApi deviceGroupApi;

    @Autowired
    private DamConfig damConfig;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private AuthProductService authProductService;

    @Override
    @Transactional
    public Long saveAuthDevice(AuthDeviceSaveReqVO reqVO) {
        projectService.validateProjectExists(reqVO.getProjectId());
        //删除当前项目设备分组
        authDeviceMapper.delete(new LambdaQueryWrapperX<AuthDeviceDO>().eq(AuthDeviceDO::getProjectId, reqVO.getProjectId()));
        // 插入新的项目设备分组
        List<AuthDeviceDO> deviceDOList = new ArrayList<>();
        reqVO.getDeviceGroupIds().forEach(item -> {
            AuthDeviceDO authDeviceDO = new AuthDeviceDO();
            authDeviceDO.setResourceSpaceId(damConfig.getResourceSpaceId());
            authDeviceDO.setProjectId(reqVO.getProjectId());
            authDeviceDO.setDeviceGroupId(item);
            deviceDOList.add(authDeviceDO);
        });
        authDeviceMapper.insertBatch(deviceDOList);
        //分组下的设备添加到设备管理，返回产品编码列表
        List<String> productCodeList = deviceService.addDeviceByDeviceGroupIds(reqVO.getProjectId(), reqVO.getDeviceGroupIds());
        //将设备对应的产品添加到产品授权
        authProductService.addAuthProductByProductCode(reqVO.getProjectId(), productCodeList);
        // 返回
        return Long.valueOf(deviceDOList.size() + "");
    }

    @Override
    @Transactional
    public Long bindDevice(AuthBindDeviceReqVO reqVO) {
        projectService.validateProjectExists(reqVO.getProjectId());

        // 绑定设备并将设备所属的产品编码返回
        List<String> productCodeList = deviceService.bindGatewayDevices(reqVO.getProjectId(), reqVO.getDeviceIds());

        // 将设备对应的产品添加到产品授权
        authProductService.addAuthProductByProductCode(reqVO.getProjectId(), productCodeList);

        // 返回
        return Long.valueOf(productCodeList.size() + "");
    }

    @Override
    public List<AuthDeviceGroupBO> getAuthDeviceGroupList(Long resourceSpaceId) {
        List<AuthDeviceGroupBO> groupBOList = new ArrayList<>();
        resourceSpaceId = Objects.isNull(resourceSpaceId) ? damConfig.getResourceSpaceId() : resourceSpaceId;
        CommonResult<List<DeviceGroupRespDTO>> result = deviceGroupApi.getDeviceGroupListByResourceSpaceId(resourceSpaceId);
        if (Objects.nonNull(result) && !CollectionUtils.isEmpty(result.getData())) {
            groupBOList.addAll(result.getData().stream().map(item -> {
                AuthDeviceGroupBO groupBO = new AuthDeviceGroupBO();
                org.springframework.beans.BeanUtils.copyProperties(item, groupBO);
                return groupBO;
            }).toList());
        }
        return groupBOList;
    }

    @Override
    public List<Long> getAuthDeviceGroupIdList(Long resourceSpaceId, Long projectId) {
        List<Long> groupIdList = new ArrayList<>();
        resourceSpaceId = Objects.isNull(resourceSpaceId) ? damConfig.getResourceSpaceId() : resourceSpaceId;
        List<AuthDeviceDO> deviceDOList = authDeviceMapper.selectList(new LambdaQueryWrapperX<AuthDeviceDO>()
                .eq(AuthDeviceDO::getResourceSpaceId, resourceSpaceId)
                .eq(AuthDeviceDO::getProjectId, projectId));
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            groupIdList.addAll(deviceDOList.stream().map(AuthDeviceDO::getDeviceGroupId).toList());
        }
        return groupIdList;
    }

    @Override
    public List<Long> getProjectByDeviceGroupId(Long deviceGroupId) {
        List<Long> projectIdList = new ArrayList<>();
        List<AuthDeviceDO> deviceDOList = authDeviceMapper.selectList(new LambdaQueryWrapperX<AuthDeviceDO>()
                .eq(AuthDeviceDO::getDeviceGroupId, deviceGroupId));
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            projectIdList = deviceDOList.stream().map(AuthDeviceDO::getProjectId).toList();
        }
        return projectIdList;
    }

    @Override
    public void deleteAuthDeviceByProjectId(Long projectId) {
        authDeviceMapper.delete(new LambdaQueryWrapperX<AuthDeviceDO>()
                .eq(AuthDeviceDO::getProjectId, projectId));
    }

}