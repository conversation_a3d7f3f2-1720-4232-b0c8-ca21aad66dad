package cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 设备采集策略 DO
 *
 * <AUTHOR>
 */
@TableName("dam_device_strategy")
@KeySequence("dam_device_strategy_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceStrategyDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 策略名称
     */
    private String strategyName;
    /**
     * 采集方式，1：定点采集，2：间隔采集
     */
    private Integer strategyType;
    /**
     * 时间间隔
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String timeInterval;
    /**
     * 时间点（英文逗号分隔）
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String timePoint;

}