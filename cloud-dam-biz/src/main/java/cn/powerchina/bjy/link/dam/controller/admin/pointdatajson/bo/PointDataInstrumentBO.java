package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 仪器类型相关信息
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Data
public class PointDataInstrumentBO {

    /**
     * 仪器类型id
     */
    private Long instrumentId;

    /**
     * 观测记录数量
     */
    private Long pointDataCount;

    /**
     * 最近观测时间
     */
    private LocalDateTime pointTimeRecent;

    /**
     * 最早观测时间
     */
    private LocalDateTime pointTimeFirst;
}
