package cn.powerchina.bjy.link.dam.dal.mysql.instrument;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrument.vo.InstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仪器类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentMapper extends BaseMapperX<InstrumentDO> {

    default PageResult<InstrumentDO> selectPage(InstrumentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstrumentDO>()
                .eqIfPresent(InstrumentDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(InstrumentDO::getInstrumentName, reqVO.getInstrumentName())
                .eqIfPresent(InstrumentDO::getMeasurePrinciple, reqVO.getMeasurePrinciple())
                .eqIfPresent(InstrumentDO::getMeasureItem, reqVO.getMeasureItem())
                .orderByDesc(InstrumentDO::getCreateTime));
    }

}