package cn.powerchina.bjy.link.dam.controller.admin.authproduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 产品授权 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AuthProductRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24194")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", example = "9616")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "产品编码")
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "产品名称", example = "王五")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "厂商", example = "张三")
    @ExcelProperty("厂商")
    private String firmName;

    @Schema(description = "产品型号")
    @ExcelProperty("产品型号")
    private String productModel;

    @Schema(description = "节点类型（0直连，1网关，2网关子设备）", example = "2")
    @ExcelProperty("节点类型（0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}