package cn.powerchina.bjy.link.dam.service.iotproduct;

import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct.IotProductDO;
import cn.powerchina.bjy.link.dam.dal.mysql.iotproduct.IotProductMapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 物联网平台同步的产品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IotProductServiceImpl implements IotProductService {

    @Autowired
    private IotProductMapper iotProductMapper;

    /**
     * 新增
     * @param iotProductDO 物联网平台库的产品
     */
    @Override
    public void createIotProduct(IotProductDO iotProductDO) {
        iotProductMapper.insert(iotProductDO);
    }

    /**
     * 根据物联网平台库的产品id删除
     * @param iotId 物联网平台库的产品id
     */
    @Override
    public void deleteByIotId(Long iotId) {
       iotProductMapper.delete(new LambdaQueryWrapperX<IotProductDO>().eq(IotProductDO::getIotId, iotId));
    }

    /**
     * 根据物联网平台库的产品id更新
     * @param iotProductDO 物联网平台库的产品id
     */
    @Override
    public void updateByIotId(IotProductDO iotProductDO) {
        iotProductMapper.update(new LambdaUpdateWrapper<IotProductDO>()
                .set(IotProductDO::getResourceSpaceId, iotProductDO.getResourceSpaceId())
                .set(IotProductDO::getProductName, iotProductDO.getProductName())
                .set(IotProductDO::getProductCode, iotProductDO.getProductCode())
                .set(IotProductDO::getProductModel, iotProductDO.getProductModel())
                .set(IotProductDO::getFirmName, iotProductDO.getFirmName())
                .set(IotProductDO::getDescription, iotProductDO.getDescription())
                .set(IotProductDO::getNodeType, iotProductDO.getNodeType())
                .set(IotProductDO::getProtocolCode, iotProductDO.getProtocolCode())
                .set(IotProductDO::getNetworkMethod, iotProductDO.getNetworkMethod())
                .set(IotProductDO::getDataFormat, iotProductDO.getDataFormat())
                .set(IotProductDO::getProductState, iotProductDO.getProductState())
                .set(IotProductDO::getProductSecret, iotProductDO.getProductSecret())
                .set(IotProductDO::getIotCreateTime, iotProductDO.getIotCreateTime())
                .eq(IotProductDO::getIotId, iotProductDO.getIotId())
        );
    }

    /**
     * 根据物联网平台库的产品id获取物联网平台库同步的产品
     * @param iotId 物联网平台库的产品id
     * @return 物联网平台库同步的产品
     */
    @Override
    public IotProductDO getByIotId(Long iotId) {
        List<IotProductDO> iotProductDOList = iotProductMapper.selectList(new LambdaQueryWrapperX<IotProductDO>()
                .eq(IotProductDO::getIotId, iotId));
        return iotProductDOList.stream().findFirst().orElse(null);
    }

    /**
     * 根据产品编码列表获取物联网平台同步的产品列表
     * @param productCodeList 产品编码列表
     * @return 物联网平台同步的产品列表
     */
    @Override
    public List<IotProductDO> listByProductCode(List<String> productCodeList) {
        if (CollectionUtils.isNotEmpty(productCodeList)) {
            List<IotProductDO> iotProductDOList = iotProductMapper.selectList(new LambdaQueryWrapperX<IotProductDO>()
                    .in(IotProductDO::getProductCode, productCodeList));
            return iotProductDOList;
        } else {
            return new ArrayList<>();
        }
    }
}
