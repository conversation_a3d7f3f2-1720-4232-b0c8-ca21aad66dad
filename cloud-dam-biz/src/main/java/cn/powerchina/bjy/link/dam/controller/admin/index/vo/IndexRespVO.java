package cn.powerchina.bjy.link.dam.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 首页信息
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Schema(description = "管理后台 - 首页信息 Response VO")
@Data
public class IndexRespVO {

    @Schema(description = "监测仪器数量")
    private Long pointDeviceTotal;

    @Schema(description = "监测仪器今日新增数量")
    private Long pointDeviceToday;

    @Schema(description = "监测数据数量")
    private Long pointDataTotal;

    @Schema(description = "监测数据今日新增数量")
    private Long pointDataToday;

    @Schema(description = "自动化设备数量")
    private Long deviceMcuTotal;

    @Schema(description = "自动化设备今日新增数量")
    private Long deviceMcuToday;

    @Schema(description = "在测自动化仪器数量")
    private Long deviceMcuRunTotal;

    @Schema(description = "在测自动化仪器今日新增数量")
    private Long deviceMcuRunToday;

    @Schema(description = "自动化监测数据数量")
    private Long pointDataMcuTotal;

    @Schema(description = "自动化监测数据今日新增数量")
    private Long pointDataMcuToday;

    @Schema(description = "仪器数量列表")
    private List<PointDeviceInfo> pointDeviceInfoList;

    @Schema(description = "管理后台 - 仪器类型信息 Response VO")
    @Data
    public static class PointDeviceInfo {

        @Schema(description = "仪器类型名称")
        private String instrumentName;

        @Schema(description = "仪器数量")
        private Long instrumentCount;

        @Schema(description = "仪器占比")
        private String instrumentRate;

        @Schema(description = "观测记录数量")
        private Long pointDataCount;

        @Schema(description = "最近观测时间")
        private LocalDateTime pointTimeRecent;

        @Schema(description = "最早观测时间")
        private LocalDateTime pointTimeFirst;

    }
}
