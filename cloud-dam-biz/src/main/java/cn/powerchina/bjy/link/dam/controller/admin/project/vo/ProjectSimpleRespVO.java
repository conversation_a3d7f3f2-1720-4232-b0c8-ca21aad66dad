package cn.powerchina.bjy.link.dam.controller.admin.project.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/9
 */
@Data
@Schema(description = "管理后台 - 项目管理简单信息 Response VO")
public class ProjectSimpleRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目名称")
    @ExcelProperty("项目名称")
    private String projectName;
}
