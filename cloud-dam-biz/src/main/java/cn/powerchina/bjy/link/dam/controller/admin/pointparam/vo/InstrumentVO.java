package cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型-计算参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentVO {

    @Schema(description = "参数名称", example = "王五")
    @ExcelProperty("参数名称")
    private String thingName;

    @Schema(description = "参数标识符")
    @ExcelProperty("参数标识符")
    private String thingIdentity;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String thingUnit;

    @Schema(description = "小数位")
    @ExcelProperty("小数位")
    private Integer decimalLimit;
}