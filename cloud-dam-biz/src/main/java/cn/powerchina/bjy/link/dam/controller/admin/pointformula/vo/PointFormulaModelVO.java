package cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/11
 */
@Schema(description = "管理后台 - 测点计算公式测量分量 Response VO")
@Data
public class PointFormulaModelVO {

    @Schema(description = "分量主键id", example = "7726")
    private Long id;

    @Schema(description = "公式关联主键id")
    private Long formulaId;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "仪器类型")
    private String instrumentName;

    @Schema(description = "测点状态")
    private Integer pointState;

    @Schema(description = "分量名称", example = "赵六")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "分量单位")
    private String thingUnit;

    @Schema(description = "取值条件，1：无，2：相对测值，3：首次测值：所有测次中第一次测值，4：时间范围内测值")
    private Integer dataCondition;

    @Schema(description = "关联的测点id：为空需要显示‘编辑’")
    private Long pointId;
}
