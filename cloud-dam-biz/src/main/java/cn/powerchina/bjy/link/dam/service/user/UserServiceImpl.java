package cn.powerchina.bjy.link.dam.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.cloud.system.api.user.dto.UserSaveRequestVO;
import cn.powerchina.bjy.cloud.system.api.user.dto.UserUpdatePasswordVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserPasswordVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import cn.powerchina.bjy.link.dam.dal.mysql.projectuser.ProjectUserMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.user.UserMapper;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.enums.UserRoleTypeEnum;
import cn.powerchina.bjy.link.dam.enums.UserTypeEnum;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.dam.service.projectuser.ProjectUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.USER_ADMIN_NAME_EXISTS;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.USER_NOT_EXISTS;

/**
 * 用户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private ProjectUserMapper projectUserMapper;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleApi roleApi;

    @Autowired
    @Lazy
    private ProjectUserService projectUserService;

    @Resource
    @Lazy
    private ProjectService projectService;

    @Override
    @Transactional
    public Long createUser(UserSaveReqVO createReqVO) {
        UserSaveRequestVO createApiVO = new UserSaveRequestVO();
        BeanUtil.copyProperties(createReqVO, createApiVO);
        // 插入
        UserDO user = BeanUtils.toBean(createReqVO, UserDO.class);
        if (Objects.nonNull(userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getMobile, user.getMobile())))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_IPHONE_EXISTS);
        }
        if (Objects.nonNull(userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getUsername, user.getUsername())))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NAME_EXISTS);
        }

        Long userId;
        //system系统已存在，但dam不存在，直接插入
        AdminUserRespDTO userByUsername = userApi.getUserByUsername(createReqVO.getUsername());
        if (null == userByUsername) {
            userId = userApi.saveUser(createApiVO).getCheckedData();
        } else {
            userId = userByUsername.getId();
        }
        user.setId(userId);
        userMapper.insert(user);

        //插入用户角色关系表
        String roleCode = UserRoleTypeEnum.getCodeByType(createReqVO.getUserType());
        Map<String, RoleRespDTO> stringRoleRespDTOMap = roleApi.loadByCodes(Collections.singletonList(roleCode));
        if (Objects.nonNull(stringRoleRespDTOMap)) {
            RoleRespDTO roleRespDTO = stringRoleRespDTOMap.get(roleCode);
            if (Objects.nonNull(roleRespDTO)) {
                Set<Long> userIds = new HashSet<>();
                userIds.add(userId);
                permissionApi.assignUserRole(userIds, Collections.singleton(roleRespDTO.getId()), true);
            }
        }
        // 返回
        return user.getId();
    }

    @Override
    @Transactional
    public void updateUser(UserSaveReqVO updateReqVO) {
        // 校验存在
        UserDO userDO = validateUserExists(updateReqVO.getId());
        UserSaveRequestVO createApiVO = new UserSaveRequestVO();
        BeanUtil.copyProperties(updateReqVO, createApiVO);
        createApiVO.setStatus(userDO.getStatus());
        userApi.updateUser(createApiVO).getCheckedData();
        //用户角色有变更：插入用户角色关系表
        if (!updateReqVO.getUserType().equals(userDO.getUserType())) {
            //绑定项目的项目管理员，不允许切换用户类型
            if (UserRoleTypeEnum.PROJECT_ADMIN.getUserType().equals(userDO.getUserType())) {
                List<ProjectDO> projectListByUserId = projectService.getProjectListByUserId(userDO.getId());
                if (CollectionUtil.isNotEmpty(projectListByUserId)) {
                    throw exception(ErrorCodeConstants.PROJECT_USER_BIND_PROJECT);
                }
            }

            String roleCode = UserRoleTypeEnum.getCodeByType(updateReqVO.getUserType());
            Map<String, RoleRespDTO> stringRoleRespDTOMap = roleApi.loadByCodes(Collections.singletonList(roleCode));
            if (Objects.nonNull(stringRoleRespDTOMap)) {
                RoleRespDTO roleRespDTO = stringRoleRespDTOMap.get(roleCode);
                if (Objects.nonNull(roleRespDTO)) {
                    Set<Long> userIds = new HashSet<>();
                    userIds.add(updateReqVO.getId());
                    permissionApi.assignUserRole(userIds, Collections.singleton(roleRespDTO.getId()), false);
                }
            }
        }
        UserDO UserDOT = userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getMobile, updateReqVO.getMobile()));
        if (Objects.nonNull(UserDOT) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), UserDOT.getId()))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_IPHONE_EXISTS);
        }
        UserDO UserDOName = userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getUsername, updateReqVO.getUsername()));
        if (Objects.nonNull(UserDOName) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), UserDOName.getId()))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NAME_EXISTS);
        }
        // 更新
        UserDO updateObj = BeanUtils.toBean(updateReqVO, UserDO.class);
        userMapper.updateById(updateObj);
    }

    @Override
    public UserDO getUserByUsername(UserSaveReqVO updateReqVO) {
        //先判断dam是否存在同用户
        UserDO UserDOName = userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getUsername, updateReqVO.getUsername()));
        if (Objects.nonNull(UserDOName) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), UserDOName.getId()))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NAME_EXISTS);
        }

        if (null != UserDOName) {
            return UserDOName;
        }

        AdminUserRespDTO userByUsername = userApi.getUserByUsername(updateReqVO.getUsername());
        if (null != userByUsername) {
            UserDO userDO = new UserDO();
            userDO.setUsername(updateReqVO.getUsername());
            userDO.setMobile(userByUsername.getMobile());
            userDO.setName(userByUsername.getName());
            return userDO;
        }
        return null;
    }

    @Override
    @Transactional
    public void updatePassword(UserPasswordVO userPasswordVO) {
        UserUpdatePasswordVO userUpdatePasswordVO = new UserUpdatePasswordVO();
        BeanUtil.copyProperties(userPasswordVO, userUpdatePasswordVO);
        Long userId = userApi.updateUserPassword(userUpdatePasswordVO).getCheckedData();
        if (Objects.nonNull(userPasswordVO.getMobile())) {
            UserDO updateObj = new UserDO();
            updateObj.setId(userId);
            updateObj.setMobile(userPasswordVO.getMobile());
            userMapper.updateById(updateObj);
        }
    }

    @Override
    @Transactional
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        validateUserExists(id);
        UserSaveRequestVO createApiVO = new UserSaveRequestVO();
        createApiVO.setId(id);
        createApiVO.setStatus(status);
        userApi.updateUser(createApiVO).getCheckedData();
        // 更新状态
        UserDO updateObj = new UserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        userMapper.updateById(updateObj);
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        // 校验存在
        UserDO userDO = validateUserExists(id);
        //查看当前用户是否管理员，如果管理员有管理的项目，不允许删除
        if (Objects.equals(userDO.getUserType(), UserTypeEnum.PROJECT_ADMIN.getType())
                && projectUserService.getProjectUserByUserId(userDO.getId()).size() > 0) {
            throw exception(ErrorCodeConstants.USER_ADMIN_PROJECT_EXISTS);
        }
        //rpc删除system库的用户
        userApi.deleteUser(id);
        // 删除dam库的用户
        userMapper.deleteById(id);
    }

    private UserDO validateUserExists(Long id) {
        UserDO userDO = userMapper.selectById(id);
        if (Objects.isNull(userDO)) {
            throw exception(USER_NOT_EXISTS);
        }
        return userDO;
    }

    @Override
    public UserDO getUser(Long id) {
        UserDO userDO = userMapper.selectById(id);
        //rpc获取admin相关数据,大坝没有admin用户
        if (Objects.isNull(userDO)) {
            CommonResult<AdminUserRespDTO> user = userApi.getUser(id);
            if (user.isSuccess()) {
                userDO = new UserDO();
                BeanUtil.copyProperties(user.getCheckedData(), userDO);
            }
        }
        return userDO;
    }

    @Override
    public PageResult<UserBO> getUserPage(UserPageReqVO pageReqVO) {
        //查询所属项目的用户
        if (Objects.nonNull(pageReqVO.getProjectId())) {
            List<Long> userIdList = projectUserMapper.selectUserIdByProjectId(pageReqVO.getProjectId());
            pageReqVO.setUserIdList(userIdList);
        }
        PageResult<UserDO> userDOPageResult = userMapper.selectPage(pageReqVO);
        List<UserBO> userList = new ArrayList<>();
        userDOPageResult.getList().forEach(item -> {
            UserBO userBO = new UserBO();
            BeanUtil.copyProperties(item, userBO);
            List<String> projectNameList = projectUserMapper.selectProjectNameByUserId(item.getId());
            if (CollectionUtil.isNotEmpty(projectNameList)) {
                userBO.setProjectNames(StringUtils.join(projectNameList, ","));
            }
            userList.add(userBO);
        });
        return new PageResult(userList, userDOPageResult.getTotal());
    }

    @Override
    public UserDO getUser(String username, Integer userType) {
        return userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getUsername, username).eq(UserDO::getUserType, userType));
    }

}