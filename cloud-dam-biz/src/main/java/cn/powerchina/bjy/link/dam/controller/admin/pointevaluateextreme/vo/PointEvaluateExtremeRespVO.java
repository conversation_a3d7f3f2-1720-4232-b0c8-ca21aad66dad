package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 测点评价指标极值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointEvaluateExtremeRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22086")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27258")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31317")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "分量id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28174")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间（小于等于当前时间）")
    @ExcelProperty("结束时间（小于等于当前时间）")
    private LocalDateTime endTime;

    @Schema(description = "采集类型(0：全部，1：自动化采集，2：人工录入）", example = "2")
    @ExcelProperty("采集类型(0：全部，1：自动化采集，2：人工录入）")
    private Integer dataType;

    @Schema(description = "最值法(1-极值百分比浮动，2-极值固定值浮动，3-固定数值)", example = "2")
    @ExcelProperty("最值法(1-极值百分比浮动，2-极值固定值浮动，3-固定数值)")
    private Integer extremeType;

    @Schema(description = "异常最小值(1-极小值，2-极大值)", example = "1")
    @ExcelProperty("异常最小值(1-极小值，2-极大值)")
    private Integer abnormalDownType;

    @Schema(description = "异常最小值(1-加，2-减)")
    @ExcelProperty("异常最小值(1-加，2-减)")
    private Integer abnormalDownSymbol;

    @Schema(description = "异常最小值")
    @ExcelProperty("异常最小值")
    private String abnormalDown;

    @Schema(description = "正常最小值(1-极小值，2-极大值)", example = "1")
    @ExcelProperty("正常最小值(1-极小值，2-极大值)")
    private Integer waringDownType;

    @Schema(description = "正常最小值(1-加，2-减)")
    @ExcelProperty("正常最小值(1-加，2-减)")
    private Integer waringDownSymbol;

    @Schema(description = "正常最小值")
    @ExcelProperty("正常最小值")
    private String waringDown;

    @Schema(description = "异常最大值(1-极小值，2-极大值)", example = "2")
    @ExcelProperty("异常最大值(1-极小值，2-极大值)")
    private Integer abnormalUpType;

    @Schema(description = "异常最大值(1-加，2-减)")
    @ExcelProperty("异常最大值(1-加，2-减)")
    private Integer abnormalUpSymbol;

    @Schema(description = "异常最大值")
    @ExcelProperty("异常最大值")
    private String abnormalUp;

    @Schema(description = "正常最大值(1-极小值，2-极大值)", example = "1")
    @ExcelProperty("正常最大值(1-极小值，2-极大值)")
    private Integer waringUpType;

    @Schema(description = "正常最大值(1-加，2-减)")
    @ExcelProperty("正常最大值(1-加，2-减)")
    private Integer waringUpSymbol;

    @Schema(description = "异常最小值")
    @ExcelProperty("异常最小值")
    private String waringUp;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}