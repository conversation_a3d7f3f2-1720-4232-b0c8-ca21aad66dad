package cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo;

import cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo.FormulaModelSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点计算公式新增/修改 Request VO")
@Data
public class PointFormulaSaveReqVO {

    @Schema(description = "主键id:必填，新增调用get返回返回id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7726")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "项目id", example = "1849")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "测点id", example = "23216")
    @NotNull(message = "测点id不能为空")
    private Long pointId;

    @Schema(description = "分量id", example = "22493")
    @NotNull(message = "请选择分量")
    private Long instrumentModelId;

    @Schema(description = "分量名称", example = "赵六")
    private String thingName;

    @Schema(description = "分量标识符")
    private String thingIdentity;

    @Schema(description = "计算公式")
    private String calcFormula;

    @Schema(description = "有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工", example = "1")
    @NotNull(message = "适用类型不能为空")
    private Integer applyType;

}