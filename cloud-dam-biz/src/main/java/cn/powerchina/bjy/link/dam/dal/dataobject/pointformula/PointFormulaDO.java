package cn.powerchina.bjy.link.dam.dal.dataobject.pointformula;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测点计算公式 DO
 *
 * <AUTHOR>
 */
@TableName("dam_point_formula")
@KeySequence("dam_point_formula_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointFormulaDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量名称
     */
    private String thingName;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 计算公式
     */
    private String calcFormula;
    /**
     * 有效开始时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime effectiveStartTime;
    /**
     * 有效结束时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime effectiveEndTime;
    /**
     * 适用类型，1：全部，2：自动化，3：人工
     */
    private Integer applyType;

}