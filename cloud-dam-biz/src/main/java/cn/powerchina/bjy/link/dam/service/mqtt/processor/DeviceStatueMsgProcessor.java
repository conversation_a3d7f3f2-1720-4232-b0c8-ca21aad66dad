package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice.IotDeviceDO;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.iotdevice.IotDeviceService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceSave;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceStatue;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Optional;

/**
 * 设备状态变更消息处理器
 */
@Component
public class DeviceStatueMsgProcessor implements MsgProcessor {

    @Autowired
    private IotDeviceService iotDeviceService;

    @Autowired
    private DeviceService deviceService;

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    public void process(String payload) {
        MqttReceiveData<DeviceStatue> deviceData = JsonUtils.parseObject(payload, new TypeReference<MqttReceiveData<DeviceStatue>>(){});
        DeviceStatue deviceStatue = deviceData.getMessage();

        // 更新物联网平台同步的设备状态
        IotDeviceDO iotDeviceDO = new IotDeviceDO();
        iotDeviceDO.setDeviceCode(deviceStatue.getDeviceCode());
        iotDeviceDO.setProductCode(deviceStatue.getProductCode());
        iotDeviceDO.setLinkState(deviceStatue.getStatus());
        if (StringUtils.isNotBlank(deviceStatue.getReportTime()) && Objects.equals(deviceStatue.getStatus(), 1)) {
            iotDeviceDO.setLastUpTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(deviceStatue.getReportTime())), ZoneId.systemDefault()));
        }
        iotDeviceService.updateLinkState(iotDeviceDO);

        // 更新大坝设备状态
        DeviceDO deviceDO = new DeviceDO();
        deviceDO.setDeviceCode(deviceStatue.getDeviceCode());
        deviceDO.setProductCode(deviceStatue.getProductCode());
        deviceDO.setLinkState(deviceStatue.getStatus());
        if (StringUtils.isNotBlank(deviceStatue.getReportTime()) && Objects.equals(deviceStatue.getStatus(), 1)) {
            deviceDO.setLastUpTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(deviceStatue.getReportTime())), ZoneId.systemDefault()));
        }
        deviceService.updateLinkState(deviceDO);
    }

    /**
     * 获取当前消息处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.DEVICE_STATUE_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.DEVICE_STATUE_UPDATE.getDataType();
    }
}
