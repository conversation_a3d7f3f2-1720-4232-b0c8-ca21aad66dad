package cn.powerchina.bjy.link.dam.controller.admin.point.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 测点信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointPageReqVO extends PageParam {

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "工程分类id")
    @NotNull(message = "请选择左侧仪器类型或分组")
    private Long categoryId;
    
    private List<Long> categoryIdList;

    @Schema(description = "测点编号")
    private String pointCode;

    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @Schema(description = "测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他")
    private Integer pointState;

    @Schema(description = "测量类型，1：人/自一体，2：自动化，3：人工")
    private Integer pointType;

}