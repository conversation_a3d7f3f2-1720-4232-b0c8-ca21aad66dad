package cn.powerchina.bjy.link.dam.util;

import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Map;

/**
 * @Description: 计算引擎
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/19
 */
@Slf4j
public class AviatorUtils {

    public static Object execute(String expression, Map<String, Object> params) {
        try {
            Object result = AviatorEvaluator.execute(expression, params);
            return result;
        } catch (Exception e) {
            log.error("formula expression {} error {} ", expression, e.getMessage());
            return null;
        }
    }

    public static BigDecimal getBigDecimal(Object value) {
        BigDecimal val = null;
        if (value != null) {
            if (value instanceof BigDecimal) {
                val = (BigDecimal) value;
            } else if (value instanceof String) {
                val = new BigDecimal((String) value);
            } else if (value instanceof BigInteger) {
                val = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                val = new BigDecimal(((Number) value).doubleValue());
            } else {
                throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
            }
        }
        return val;
    }
}
