package cn.powerchina.bjy.link.dam.api.index;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.service.index.IndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 首页执行计划
 * @Author: z<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/2/19
 */
@RestController
@Slf4j
public class IndexStatisticsApiImpl implements IndexStatisticsApi {


    @Autowired
    private IndexService indexService;


    @Override
    public CommonResult<Boolean> runIndexStatistics() {
        log.info("start execute index statistics info");
        indexService.readIndexData();
        return CommonResult.success(true);
    }
}
