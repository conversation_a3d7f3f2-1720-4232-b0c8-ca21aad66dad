package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.iotdevice.IotDeviceService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.device.DeviceDelete;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备删除消息处理器
 */
@Component
public class DeviceDeleteMsgProcessor implements MsgProcessor {

    @Autowired
    private IotDeviceService iotDeviceService;

    @Autowired
    private DeviceService deviceService;

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    @Transactional
    public void process(String payload) {
        MqttReceiveData<DeviceDelete> deviceData = JsonUtils.parseObject(payload, new TypeReference<MqttReceiveData<DeviceDelete>>(){});
        DeviceDelete deviceDelete = deviceData.getMessage();

        // 删除物联网平台同步的设备
        iotDeviceService.deleteByIotId(deviceDelete.getId());

        // 获取当前大坝设备
        DeviceDO deviceDO = deviceService.getDeviceDOByDeviceCode(deviceDelete.getDeviceCode());
        // 获取当前大坝设备的子设备
        List<DeviceDO> deviceDOList = deviceService.listByParentCode(deviceDelete.getDeviceCode());

        // 将当前大坝设备与子设备都删除
        List<Long> deleteIdList = new ArrayList<>();
        deleteIdList.add(deviceDO.getId());
        deleteIdList.addAll(deviceDOList.stream().map(DeviceDO::getId).collect(Collectors.toList()));
        deviceService.deleteDeviceList(deleteIdList);
    }

    /**
     * 获取当前消息处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.DEVICE_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.DEVICE_DELETE.getDataType();
    }
}
