package cn.powerchina.bjy.link.dam.controller.admin.pointalarm;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointalarm.PointAlarmDO;
import cn.powerchina.bjy.link.dam.service.pointalarm.PointAlarmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测点报警信息")
@RestController
@RequestMapping("/dam/point/alarm")
@Validated
public class PointAlarmController {

    @Resource
    private PointAlarmService pointAlarmService;

    @PutMapping("/update")
    @Operation(summary = "更新测点报警信息")
//    @PreAuthorize("@ss.hasPermission('dam:point-alarm:update')")
    public CommonResult<Boolean> updatePointAlarm(@Valid @RequestBody PointAlarmUpdateReqVO updateReqVO) {
        pointAlarmService.updatePointAlarm(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测点报警信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:point-alarm:query')")
    public CommonResult<PointAlarmRespVO> getPointAlarm(@RequestParam("id") Long id) {
        PointAlarmDO pointAlarm = pointAlarmService.getPointAlarm(id);
        return success(BeanUtils.toBean(pointAlarm, PointAlarmRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测点报警信息分页")
//    @PreAuthorize("@ss.hasPermission('dam:point-alarm:query')")
    public CommonResult<PageResult<PointAlarmPageRespVO>> getPointAlarmPage(@Valid PointAlarmPageReqVO pageReqVO) {
        PageResult<PointAlarmPageRespVO> pageResult = pointAlarmService.getPointAlarmPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointAlarmPageRespVO.class));
    }

}