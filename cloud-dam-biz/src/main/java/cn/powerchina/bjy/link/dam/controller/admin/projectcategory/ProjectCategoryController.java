package cn.powerchina.bjy.link.dam.controller.admin.projectcategory;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory.ProjectCategoryDO;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 工程分类管理")
@RestController
@RequestMapping("/dam/project/category")
@Validated
public class ProjectCategoryController {

    @Resource
    private ProjectCategoryService projectCategoryService;

    @PostMapping("/create")
    @Operation(summary = "创建工程分类管理")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:create')")
    public CommonResult<Long> createProjectCategory(@Valid @RequestBody ProjectCategorySaveReqVO createReqVO) {
        //校验是否是根节点
        if (Objects.isNull(createReqVO.getParentId())) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_CREATE_PARENT_ERROR);
        }
        return success(projectCategoryService.createProjectCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新工程分类管理")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:update')")
    public CommonResult<Boolean> updateProjectCategory(@Valid @RequestBody ProjectCategorySaveReqVO updateReqVO) {
        projectCategoryService.updateProjectCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除工程分类管理")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:project-category:delete')")
    public CommonResult<Boolean> deleteProjectCategory(@RequestParam("id") Long id) {
        projectCategoryService.deleteProjectCategory(id);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获得工程分类管理数据")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:query')")
    public CommonResult<List<ProjectCategoryRespVO>> getProjectCategoryList(@Valid ProjectCategoryListReqVO respVO) {
        List<ProjectCategoryBO> CategoryList = projectCategoryService.getProjectCategoryListByCategoryTypeAndId(respVO);
        return success(BeanUtils.toBean(CategoryList, ProjectCategoryRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得工程分类管理数据明细")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:query')")
    @Parameter(name = "id", description = "节点id", required = true)
    public CommonResult<ProjectCategoryChildRespVO> getProjectCategory(@RequestParam("id") Long id) {
        return success(BeanUtils.toBean(projectCategoryService.getProjectCategory(id), ProjectCategoryChildRespVO.class));
    }

    @GetMapping("/list/child")
    @Operation(summary = "获得工程分类管理直接子级数据")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:query')")
    @Parameter(name = "categoryId", description = "节点id", required = true)
    public CommonResult<List<ProjectCategoryChildRespVO>> getProjectCategoryChildList(@RequestParam("categoryId") Long categoryId) {
        List<ProjectCategoryDO> CategoryList = projectCategoryService.getProjectCategoryListByParentId(categoryId, false, true);
        return success(BeanUtils.toBean(CategoryList, ProjectCategoryChildRespVO.class));
    }

    @GetMapping("/get/categoryName")
    @Operation(summary = "获取仪器类型和工程结构名称")
    @Parameter(name = "categoryId", description = "节点id", required = true)
    public CommonResult<NameRespVO> getInstrumentOrStructCategoryName(@RequestParam("categoryId") Long categoryId) {
        NameRespVO nameRespVO=projectCategoryService.getProjectCategoryMeasure(categoryId);
        return success(BeanUtils.toBean(nameRespVO, NameRespVO.class));
    }

    @GetMapping("/list/instrumentName")
    @Operation(summary = "获得仪器类型相同名字的节点")
//    @PreAuthorize("@ss.hasPermission('dam:project-category:query')")
    public CommonResult<List<ProjectCategoryRespVO>> getProjectInstrumentList(@Valid ProjectCategoryListReqVO respVO,String instrumentName) {
        List<ProjectCategoryBO> CategoryList = projectCategoryService.getProjectInstrumentListByCategoryTypeAndId(respVO,instrumentName);
        return success(BeanUtils.toBean(CategoryList, ProjectCategoryRespVO.class));
    }

}