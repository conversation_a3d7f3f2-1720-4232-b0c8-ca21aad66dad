package cn.powerchina.bjy.link.dam.service.devicestrategy;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategyPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.devicestrategy.vo.DeviceStrategySaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy.DeviceStrategyDO;
import jakarta.validation.Valid;

/**
 * 设备采集策略 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceStrategyService {

    /**
     * 创建设备采集策略
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceStrategy(@Valid DeviceStrategySaveReqVO createReqVO);

    /**
     * 更新设备采集策略
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceStrategy(@Valid DeviceStrategySaveReqVO updateReqVO);

    /**
     * 删除设备采集策略
     *
     * @param id 编号
     */
    void deleteDeviceStrategy(Long id);

    /**
     * 校验采集策略是否存在
     *
     * @param id
     * @return
     */
    DeviceStrategyDO validateDeviceStrategyExists(Long id);

    /**
     * 获得设备采集策略
     *
     * @param id 编号
     * @return 设备采集策略
     */
    DeviceStrategyDO getDeviceStrategy(Long id);

    /**
     * 获得设备采集策略分页
     *
     * @param pageReqVO 分页查询
     * @return 设备采集策略分页
     */
    PageResult<DeviceStrategyDO> getDeviceStrategyPage(DeviceStrategyPageReqVO pageReqVO);

}