package cn.powerchina.bjy.link.dam.dal.mysql.point;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointInstrumentModelBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointInstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

/**
 * 测点信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointMapper extends BaseMapperX<PointDO> {

    default PageResult<PointDO> selectPage(PointPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PointDO>()
                .eq(PointDO::getProjectId, reqVO.getProjectId())
                .in(PointDO::getCategoryId, reqVO.getCategoryIdList())
                .likeIfPresent(PointDO::getPointCode, reqVO.getPointCode())
                .eqIfPresent(PointDO::getMeasureItem, reqVO.getMeasureItem())
                .eqIfPresent(PointDO::getPointState, reqVO.getPointState())
                .eqIfPresent(PointDO::getPointType, reqVO.getPointType())
                .orderByDesc(PointDO::getId));
    }

    /**
     * 获取测点的测量分量
     *
     * @param reqVO
     * @return
     */
    default PageResult<PointInstrumentModelBO> selectPointInstrumentModelPage(PointInstrumentPageReqVO reqVO) {
        MPJLambdaWrapperX<PointDO> wrapper = (MPJLambdaWrapperX<PointDO>) new MPJLambdaWrapperX<PointDO>()
                .selectAs(PointDO::getProjectId, PointInstrumentModelBO::getProjectId)
                .selectAs(PointDO::getId, PointInstrumentModelBO::getPointId)
                .selectAs(PointDO::getPointCode, PointInstrumentModelBO::getPointCode)
                .eq(PointDO::getProjectId, reqVO.getProjectId())
                .in(PointDO::getId, reqVO.getPointIds())
                .leftJoin(InstrumentModelDO.class, "p", on -> on.eq(PointDO::getInstrumentId, InstrumentModelDO::getInstrumentId))
                .selectAs(InstrumentModelDO::getId, PointInstrumentModelBO::getInstrumentModelId)
                .selectAs(InstrumentModelDO::getThingIdentity, PointInstrumentModelBO::getThingIdentity)
                .selectAs(InstrumentModelDO::getThingName, PointInstrumentModelBO::getThingName)
                .selectAs(InstrumentModelDO::getThingUnit, PointInstrumentModelBO::getThingUnit);
        if (!CollectionUtils.isEmpty(reqVO.getInstrumentModelIds())) {
            wrapper.in(InstrumentModelDO::getId, reqVO.getInstrumentModelIds());
        }
        wrapper.orderByAsc(PointDO::getId)
                .orderByAsc(InstrumentModelDO::getThingIdentity);
        return selectJoinPage(reqVO, PointInstrumentModelBO.class, wrapper);
    }

}