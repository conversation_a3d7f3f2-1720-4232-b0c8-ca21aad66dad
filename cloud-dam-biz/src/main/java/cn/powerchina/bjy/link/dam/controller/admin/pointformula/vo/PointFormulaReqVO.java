package cn.powerchina.bjy.link.dam.controller.admin.pointformula.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/11
 */
@Schema(description = "管理后台 - 查询测点公式分量的参数 Request VO")
@Data
public class PointFormulaReqVO {

    @Schema(description = "计算公式id")
    @NotNull(message = "计算公式id不能为空")
    private Long pointFormulaId;

    @Schema(description = "测点id")
    @NotNull(message = "测点id不能为空")
    private Long pointId;
}
