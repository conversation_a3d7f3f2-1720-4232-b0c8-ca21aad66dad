package cn.powerchina.bjy.link.dam.dal.mysql.importtask;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.importtask.ImportTaskDO;
import org.apache.ibatis.annotations.Mapper;
import cn.powerchina.bjy.link.dam.controller.admin.importtask.vo.*;

/**
 * 导入任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImportTaskMapper extends BaseMapperX<ImportTaskDO> {

    default PageResult<ImportTaskDO> selectPage(ImportTaskPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImportTaskDO>()
                .eqIfPresent(ImportTaskDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(ImportTaskDO::getNodeId, reqVO.getNodeId())
                .eqIfPresent(ImportTaskDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(ImportTaskDO::getFileName, reqVO.getFileName())
                .betweenIfPresent(ImportTaskDO::getUploadTime, reqVO.getUploadTime())
                .betweenIfPresent(ImportTaskDO::getExecuteTime, reqVO.getExecuteTime())
                .eqIfPresent(ImportTaskDO::getUploadNumber, reqVO.getUploadNumber())
                .eqIfPresent(ImportTaskDO::getTotalNumber, reqVO.getTotalNumber())
                .eqIfPresent(ImportTaskDO::getTaskStatus, reqVO.getTaskStatus())
                .betweenIfPresent(ImportTaskDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ImportTaskDO::getExecuteTime));
    }

}