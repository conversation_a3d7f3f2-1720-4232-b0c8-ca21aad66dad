package cn.powerchina.bjy.link.dam.dal.mysql.instrumenttemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo.InstrumentTemplatePageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumenttemplate.InstrumentTemplateDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仪器类型模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentTemplateMapper extends BaseMapperX<InstrumentTemplateDO> {

    default PageResult<InstrumentTemplateDO> selectPage(InstrumentTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstrumentTemplateDO>()
                .likeIfPresent(InstrumentTemplateDO::getInstrumentName, reqVO.getInstrumentName())
                .eqIfPresent(InstrumentTemplateDO::getMeasurePrinciple, reqVO.getMeasurePrinciple())
                .eqIfPresent(InstrumentTemplateDO::getMeasureItem, reqVO.getMeasureItem())
                .eqIfPresent(InstrumentTemplateDO::getIconUrl, reqVO.getIconUrl())
                .eqIfPresent(InstrumentTemplateDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(InstrumentTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InstrumentTemplateDO::getId));
    }

}