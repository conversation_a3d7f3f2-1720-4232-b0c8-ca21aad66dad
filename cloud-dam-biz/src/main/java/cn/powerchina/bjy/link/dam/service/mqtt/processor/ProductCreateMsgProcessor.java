package cn.powerchina.bjy.link.dam.service.mqtt.processor;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct.IotProductDO;
import cn.powerchina.bjy.link.dam.enums.MqttDataTypeEnum;
import cn.powerchina.bjy.link.dam.enums.MqttTopicEnum;
import cn.powerchina.bjy.link.dam.service.iotproduct.IotProductService;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.MqttReceiveData;
import cn.powerchina.bjy.link.dam.service.mqtt.bo.product.Product;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 产品创建消息处理器
 */
@Component
public class ProductCreateMsgProcessor implements MsgProcessor {

    @Autowired
    private IotProductService iotProductService;

    /**
     * 处理消息
     * @param payload 消息
     */
    @Override
    public void process(String payload) {
        MqttReceiveData<Product> productData = JsonUtils.parseObject(payload, new TypeReference<>() {
        });
        Product product = productData.getMessage();

        // 保存物联网平台同步的产品
        LocalDateTime iotCreateTime = LocalDateTime.parse(product.getCreateTime());

        IotProductDO iotProductDO = new IotProductDO();
        iotProductDO.setIotId(product.getId());
        iotProductDO.setResourceSpaceId(product.getResourceSpaceId());
        iotProductDO.setProductName(product.getProductName());
        iotProductDO.setProductCode(product.getProductCode());
        iotProductDO.setProductModel(product.getProductModel());
        iotProductDO.setFirmName(product.getFirmName());
        iotProductDO.setDescription(product.getDescription());
        iotProductDO.setNodeType(product.getNodeType());
        iotProductDO.setProtocolCode(product.getProtocolCode());
        iotProductDO.setNetworkMethod(product.getNetworkMethod());
        iotProductDO.setDataFormat(product.getDataFormat());
        iotProductDO.setProductState(product.getProductState());
        iotProductDO.setProductSecret(product.getProductSecret());
        iotProductDO.setIotCreateTime(iotCreateTime);

        iotProductService.createIotProduct(iotProductDO);
    }

    /**
     * 获取当前处理器对应的topic
     * @return topic
     */
    @Override
    public String getTopic() {
        return MqttTopicEnum.PRODUCT_TOPIC.getTopic();
    }

    /**
     * 获取当前消息处理器对应的dataType
     * @return dataType
     */
    @Override
    public String getDataType() {
        return MqttDataTypeEnum.PRODUCT_CREATE.getDataType();
    }
}
