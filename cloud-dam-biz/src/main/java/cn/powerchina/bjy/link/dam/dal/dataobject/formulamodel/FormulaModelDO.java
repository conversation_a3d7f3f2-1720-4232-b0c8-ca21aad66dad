package cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 公式关联分量 DO
 *
 * <AUTHOR>
 */
@TableName("dam_formula_model")
@KeySequence("dam_formula_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormulaModelDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 测点公式id
     */
    private Long pointFormulaId;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 取值条件，1：无，2：相对测值，3：首次测值：所有测次中第一次测值，4：时间范围内测值
     */
    private Integer dataCondition;
    /**
     * 数值或1：之前，2：之后
     */
    private Integer dataValue;
    /**
     * 第n条测值或单位，1：分钟，2：小时，3：天
     */
    private Integer dataUnit;
    /**
     * 指定时间
     */
    private LocalDateTime specifyTime;

}