package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import java.util.*;

//设置名称映射
public class ExcelSetupmap {
    private static final Map<String, String> MONTH_MAPPING = new HashMap<>();
    private static final Map<String, String> YEAR_MAPPING = new HashMap<>();
    private static final Map<String, String> CUSTOM_MAPPING = new HashMap<>();

    static {
        MONTH_MAPPING.put("id", "主键id");
        MONTH_MAPPING.put("projectId", "项目id");
        MONTH_MAPPING.put("pointId", "测点id");
        MONTH_MAPPING.put("pointCode", "测点");
        MONTH_MAPPING.put("instrumentModelId", "分量id");
        MONTH_MAPPING.put("thingIdentity", "分量标识符");
        MONTH_MAPPING.put("thingName", "分量名称");
        MONTH_MAPPING.put("thingUnit", "分量单位");
        MONTH_MAPPING.put("valueMin", "月最小");
        MONTH_MAPPING.put("valueMinPointTime", "月最小对应日期");
        MONTH_MAPPING.put("valueMax", "月最大");
        MONTH_MAPPING.put("valueMaxPointTime", "月最大对应日期");
        MONTH_MAPPING.put("valueFirst", "首值");
        MONTH_MAPPING.put("valueFirstPointTime", "首值监测时间");
        MONTH_MAPPING.put("valueLast", "尾值");
        MONTH_MAPPING.put("valueLastPointTime", "尾值监测时间");
        MONTH_MAPPING.put("valueAverage", "月平均");
        MONTH_MAPPING.put("valueRange", "月变幅");
        MONTH_MAPPING.put("valueMaxHistory", "历史最大");
        MONTH_MAPPING.put("valueMinHistory", "历史最小");
        MONTH_MAPPING.put("countNum", "测次");
        MONTH_MAPPING.put("yearAndMonth", "月份");
        MONTH_MAPPING.put("valueMinHistoryPointTime", "历史最小日期");
        MONTH_MAPPING.put("valueMaxHistoryPointTime", "历史最大日期");

        YEAR_MAPPING.put("id", "主键id");
        YEAR_MAPPING.put("projectId", "项目id");
        YEAR_MAPPING.put("pointId", "测点id");
        YEAR_MAPPING.put("pointCode", "测点");
        YEAR_MAPPING.put("instrumentModelId", "分量id");
        YEAR_MAPPING.put("thingIdentity", "分量标识符");
        YEAR_MAPPING.put("thingName", "分量名称");
        YEAR_MAPPING.put("thingUnit", "分量单位");
        YEAR_MAPPING.put("valueMin", "最小值");
        YEAR_MAPPING.put("valueMinPointTime", "最小值对应日期");
        YEAR_MAPPING.put("valueMax", "最大值");
        YEAR_MAPPING.put("valueMaxPointTime", "最大值对应日期");
        YEAR_MAPPING.put("valueFirst", "首值");
        YEAR_MAPPING.put("valueFirstPointTime", "首值监测时间");
        YEAR_MAPPING.put("valueLast", "尾值");
        YEAR_MAPPING.put("valueLastPointTime", "尾值监测时间");
        YEAR_MAPPING.put("valueAverage", "年平均");
        YEAR_MAPPING.put("valueRange", "年变幅");
        YEAR_MAPPING.put("valueMaxHistory", "历史最大");
        YEAR_MAPPING.put("valueMinHistory", "历史最小");
        YEAR_MAPPING.put("countNum", "测次");
        YEAR_MAPPING.put("yearAndMonth", "年份");
        YEAR_MAPPING.put("valueMinHistoryPointTime", "历史最小日期");
        YEAR_MAPPING.put("valueMaxHistoryPointTime", "历史最大日期");

        CUSTOM_MAPPING.put("id", "主键id");
        CUSTOM_MAPPING.put("projectId", "项目id");
        CUSTOM_MAPPING.put("pointId", "测点id");
        CUSTOM_MAPPING.put("pointCode", "测点");
        CUSTOM_MAPPING.put("instrumentModelId", "分量id");
        CUSTOM_MAPPING.put("thingIdentity", "分量标识符");
        CUSTOM_MAPPING.put("thingName", "分量名称");
        CUSTOM_MAPPING.put("thingUnit", "分量单位");
        CUSTOM_MAPPING.put("valueMin", "最小值");
        CUSTOM_MAPPING.put("valueMinPointTime", "最小值对应日期");
        CUSTOM_MAPPING.put("valueMax", "最大值");
        CUSTOM_MAPPING.put("valueMaxPointTime", "最大值对应日期");
        CUSTOM_MAPPING.put("valueFirst", "首值");
        CUSTOM_MAPPING.put("valueFirstPointTime", "首值监测日期");
        CUSTOM_MAPPING.put("valueLast", "尾值");
        CUSTOM_MAPPING.put("valueLastPointTime", "尾值监测日期");
        CUSTOM_MAPPING.put("valueAverage", "平均值");
        CUSTOM_MAPPING.put("valueRange", "变幅");
        CUSTOM_MAPPING.put("valueMaxHistory", "历史最大");
        CUSTOM_MAPPING.put("valueMinHistory", "历史最小");
        CUSTOM_MAPPING.put("countNum", "测次");
        CUSTOM_MAPPING.put("yearAndMonth", "年份");
        CUSTOM_MAPPING.put("valueMinHistoryPointTime", "历史最小日期");
        CUSTOM_MAPPING.put("valueMaxHistoryPointTime", "历史最大日期");
    }

    public static String getChineseName(Integer type, String fieldName) {
        if (Objects.equals(type, 1)) {
            return MONTH_MAPPING.getOrDefault(fieldName, fieldName);
        } else if (Objects.equals(type, 2)) {
            return YEAR_MAPPING.getOrDefault(fieldName, fieldName);
        } else if (Objects.equals(type, 3)) {
            return CUSTOM_MAPPING.getOrDefault(fieldName, fieldName);
        }
        return "";
    }

    public static Map<String, Integer> getColumnWidthConfig(){
        Map<String, Integer> columnWidthConfig = new HashMap<>();
        columnWidthConfig.put("id", 20);
        columnWidthConfig.put("projectId", 20);
        columnWidthConfig.put("pointId", 20);
        columnWidthConfig.put("pointCode", 20);
        columnWidthConfig.put("instrumentModelId", 20);
        columnWidthConfig.put("thingIdentity", 20);
        columnWidthConfig.put("thingName", 20);
        columnWidthConfig.put("thingUnit", 20);
        columnWidthConfig.put("valueMin", 20);
        columnWidthConfig.put("valueMinPointTime", 20);
        columnWidthConfig.put("valueMax", 20);
        columnWidthConfig.put("valueMaxPointTime", 20);
        columnWidthConfig.put("valueFirst", 20);
        columnWidthConfig.put("valueFirstPointTime", 20);
        columnWidthConfig.put("valueLast", 20);
        columnWidthConfig.put("valueLastPointTime", 20);
        columnWidthConfig.put("valueAverage", 20);
        columnWidthConfig.put("valueRange", 20);
        columnWidthConfig.put("valueMaxHistory", 20);
        columnWidthConfig.put("valueMinHistory", 20);
        columnWidthConfig.put("countNum", 20);
        columnWidthConfig.put("yearAndMonth", 20);
        columnWidthConfig.put("valueMaxHistoryPointTime", 20);
        columnWidthConfig.put("valueMinHistoryPointTime", 20);
        return columnWidthConfig;
    }

    public static List<String> getMonthCol(){
        List<String> list = new ArrayList<>();
        list.add("pointCode");
        list.add("yearAndMonth");
        list.add("countNum");
        list.add("valueMax");
        list.add("valueMaxPointTime");
        list.add("valueMin");
        list.add("valueMinPointTime");
        list.add("valueRange");
        list.add("valueAverage");
        list.add("valueMaxHistory");
        list.add("valueMaxHistoryPointTime");
        list.add("valueMinHistory");
        list.add("valueMinHistoryPointTime");

        return list;
    }

    public static List<String> getYearCol(){
        List<String> list = new ArrayList<>();
        list.add("pointCode");
        list.add("yearAndMonth");
        list.add("countNum");
        list.add("valueMax");
        list.add("valueMaxPointTime");
        list.add("valueMin");
        list.add("valueMinPointTime");
        list.add("valueRange");
        list.add("valueAverage");
        list.add("valueMaxHistory");
        list.add("valueMaxHistoryPointTime");
        list.add("valueMinHistory");
        list.add("valueMinHistoryPointTime");

        return list;
    }

    public static List<String> getCustomCol(){
        List<String> list = new ArrayList<>();
        list.add("pointCode");
        list.add("valueMax");
        list.add("valueMaxPointTime");
        list.add("valueMin");
        list.add("valueMinPointTime");
        list.add("valueRange");
        list.add("valueAverage");
        list.add("valueFirst");
        list.add("valueFirstPointTime");
        list.add("valueLast");
        list.add("valueLastPointTime");
        list.add("valueMaxHistory");
        list.add("valueMaxHistoryPointTime");
        list.add("valueMinHistory");
        list.add("valueMinHistoryPointTime");

        return list;
    }
}