package cn.powerchina.bjy.link.dam.controller.admin.project.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/29
 */
@Data
public class ProjectBO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 区名称
     */
    private String areaName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 维度
     */
    private String latitude;
    /**
     * 备注
     */
    private String remark;
    /**
     * 项目管理员id
     */
    private Long managerUserId;
    /**
     * 项目管理员名称
     */
    private String managerUserName;
    /**
     * 项目管理员手机号
     */
    private String managerMobile;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
