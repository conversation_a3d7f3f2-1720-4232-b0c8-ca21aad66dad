package cn.powerchina.bjy.link.dam.controller.admin.point.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/24
 */
@Schema(description = "管理后台 - 测点分量分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointInstrumentPageReqVO extends PageParam {

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "测点id集合")
    @NotEmpty(message = "请选择测点")
    private List<Long> pointIds;

    @Schema(description = "分量id集合")
    private List<Long> instrumentModelIds;
}
