package cn.powerchina.bjy.link.dam.service.mqtt;

import cn.powerchina.bjy.link.dam.config.MqttConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: MQTT服务实现类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Service
@Slf4j
public class MqttServiceImpl implements MqttService {

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private MessageChannel mqttOutboundChannel;

    @Autowired
    private MqttPahoMessageDrivenChannelAdapter inboundAdapter;

    @Value("${mqtt.group:dam}")
    private String group;

    @Override
    public void publishMessage(String topic, String message, int qos, boolean retained) {
        try {
            log.info("发布MQTT消息 - Topic: {}, Message: {}, QoS: {}, Retained: {}",
                    topic, message, qos, retained);

            org.springframework.messaging.Message<String> mqttMessage = MessageBuilder
                    .withPayload(message)
                    .setHeader(MqttHeaders.TOPIC, topic)
                    .setHeader(MqttHeaders.QOS, qos)
                    .setHeader(MqttHeaders.RETAINED, retained)
                    .build();

            mqttOutboundChannel.send(mqttMessage);
            log.info("MQTT消息发布成功 - Topic: {}", topic);

        } catch (Exception e) {
            log.error("发布MQTT消息失败 - Topic: {}, Message: {}", topic, message, e);
            throw new RuntimeException("发布MQTT消息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void publishMessage(String topic, String message) {
        publishMessage(topic, message, mqttConfig.getDefaultQos(), mqttConfig.isDefaultRetained());
    }

    @Override
    public void subscribe(String topic, int qos) {
        try {
            log.info("订阅MQTT主题 - Topic: {}, QoS: {}", topic, qos);
            inboundAdapter.addTopic(topic, qos);
            log.info("成功订阅MQTT主题 - Topic: {}", topic);
        } catch (Exception e) {
            log.error("订阅MQTT主题失败 - Topic: {}", topic, e);
            throw new RuntimeException("订阅MQTT主题失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void subscribe(String topic) {
        String sharedTopic = "$share/" + group + "/" + topic;
        subscribe(sharedTopic, mqttConfig.getDefaultSubscribeQos());
    }

    @Override
    public void unsubscribe(String topic) {
        try {
            log.info("取消订阅MQTT主题 - Topic: {}", topic);
            inboundAdapter.removeTopic(topic);
            log.info("成功取消订阅MQTT主题 - Topic: {}", topic);
        } catch (Exception e) {
            log.error("取消订阅MQTT主题失败 - Topic: {}", topic, e);
            throw new RuntimeException("取消订阅MQTT主题失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getAvailableTopics() {
        return new ArrayList<>();
    }

    @Override
    public void subscribeToAllAvailableTopics() {
        try {
            log.info("订阅所有可用的传输目标主题...");

            List<String> topics = getAvailableTopics();

            if (topics.isEmpty()) {
                log.info("没有可用的主题需要订阅");
                return;
            }

            for (String topic : topics) {
                try {
                    subscribe(topic);
                } catch (Exception e) {
                    log.error("订阅主题失败: {}", topic, e);
                }
            }

            log.info("完成订阅所有可用主题，共 {} 个", topics.size());

        } catch (Exception e) {
            log.error("订阅所有可用主题失败", e);
        }
    }

    @Override
    public boolean isConnected() {
        try {
            return inboundAdapter.isRunning();
        } catch (Exception e) {
            log.error("检查MQTT连接状态失败", e);
            return false;
        }
    }
}
