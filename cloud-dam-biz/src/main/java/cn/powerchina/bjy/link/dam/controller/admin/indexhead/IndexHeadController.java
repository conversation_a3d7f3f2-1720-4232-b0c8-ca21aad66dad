package cn.powerchina.bjy.link.dam.controller.admin.indexhead;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexhead.vo.IndexHeadSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexhead.IndexHeadDO;
import cn.powerchina.bjy.link.dam.service.indexhead.IndexHeadService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 大坝首页头部信息")
@RestController
@RequestMapping("/dam/index-head")
@Validated
public class IndexHeadController {

    @Resource
    private IndexHeadService indexHeadService;

    @PostMapping("/create")
    @Operation(summary = "创建大坝首页头部信息")
//    @PreAuthorize("@ss.hasPermission('dam:index-head:create')")
    public CommonResult<Long> createIndexHead(@Valid @RequestBody IndexHeadSaveReqVO createReqVO) {
        return success(indexHeadService.createIndexHead(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新大坝首页头部信息")
//    @PreAuthorize("@ss.hasPermission('dam:index-head:update')")
    public CommonResult<Boolean> updateIndexHead(@Valid @RequestBody IndexHeadSaveReqVO updateReqVO) {
        indexHeadService.updateIndexHead(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除大坝首页头部信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:index-head:delete')")
    public CommonResult<Boolean> deleteIndexHead(@RequestParam("id") Long id) {
        indexHeadService.deleteIndexHead(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得大坝首页头部信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:index-head:query')")
    public CommonResult<IndexHeadRespVO> getIndexHead(@RequestParam("id") Long id) {
        IndexHeadDO indexHead = indexHeadService.getIndexHead(id);
        return success(BeanUtils.toBean(indexHead, IndexHeadRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得大坝首页头部信息分页")
//    @PreAuthorize("@ss.hasPermission('dam:index-head:query')")
    public CommonResult<PageResult<IndexHeadRespVO>> getIndexHeadPage(@Valid IndexHeadPageReqVO pageReqVO) {
        PageResult<IndexHeadDO> pageResult = indexHeadService.getIndexHeadPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndexHeadRespVO.class));
    }

}
