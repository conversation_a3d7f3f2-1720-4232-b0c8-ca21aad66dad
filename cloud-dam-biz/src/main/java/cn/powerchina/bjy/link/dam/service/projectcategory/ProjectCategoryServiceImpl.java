package cn.powerchina.bjy.link.dam.service.projectcategory;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo.ProjectCategoryLevelBO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.NameRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategoryListReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.projectcategory.vo.ProjectCategorySaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumenttemplate.InstrumentTemplateDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.projectcategory.ProjectCategoryDO;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumenttemplate.InstrumentTemplateMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.projectcategory.ProjectCategoryMapper;
import cn.powerchina.bjy.link.dam.enums.*;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 工程分类管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectCategoryServiceImpl implements ProjectCategoryService {

    @Resource
    private ProjectCategoryMapper projectCategoryMapper;

    @Resource
    private InstrumentTemplateMapper instrumentTemplateMapper;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Autowired
    @Lazy
    private PointService pointService;

    @Autowired
    @Lazy
    private InstrumentService instrumentService;

    @Autowired
    @Lazy
    private DeviceService deviceService;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Override
    @Transactional
    public Long createProjectCategory(ProjectCategorySaveReqVO createReqVO) {
        //校验项目存在
        projectService.validateProjectExists(createReqVO.getProjectId());
        //名称不能重复
        validateProjectCategoryNameExists(createReqVO.getId(), createReqVO.getParentId(), createReqVO.getProjectId(), createReqVO.getCategoryName(), createReqVO.getCategoryType(), createReqVO.getBusinessId());
        ProjectCategoryDO projectCategory = BeanUtils.toBean(createReqVO, ProjectCategoryDO.class);
        projectCategory.setId(snowFlakeUtil.snowflakeId());
        //如果是仪器类型，则需要判断businessId
        if (Objects.equals(createReqVO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            InstrumentDO instrumentDO = instrumentService.getInstrument(createReqVO.getBusinessId());
            if (Objects.isNull(instrumentDO)) {
                throw exception(ErrorCodeConstants.INSTRUMENT_NOT_EXISTS);
            }
            projectCategory.setCategoryName(instrumentDO.getInstrumentName());
            projectCategory.setBusinessId(instrumentDO.getId());
        }
        //如果是根节点，则需要设置level=1
        if (Objects.isNull(projectCategory.getParentId())) {
            projectCategory.setCategoryLevel(1);
        } else {
            //如果是子节点
            ProjectCategoryDO categoryDO = validateProjectCategoryExists(projectCategory.getParentId());
            //如果是分组，则不能再创建子节点
            if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())) {
                throw exception(ErrorCodeConstants.PROJECT_GROUP_CHILDREN_ADD_ERROR);
            }
            //如果是工程结构，需要判断子节点是否有其它类型子节点，没有的话才允许创建
            if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STRUCT.getType())
                    && Objects.equals(createReqVO.getCategoryType(), CategoryTypeEnum.STRUCT.getType())) {
                List<ProjectCategoryDO> categoryDOList = getProjectCategoryListByParentId(categoryDO.getId(), false, false);
                if (!CollectionUtils.isEmpty(categoryDOList) && !Objects.equals(categoryDOList.get(0).getCategoryType(), CategoryTypeEnum.STRUCT.getType())) {
                    throw exception(ErrorCodeConstants.PROJECT_STRUCT_CHILDREN_ADD_ERROR);
                }
            }
            //如果是监测站点，需要判断父监测站点是否绑定了采集仪，绑定了就不允许增加子节点
            if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STATION.getType())
                    && Objects.equals(createReqVO.getCategoryType(), CategoryTypeEnum.STATION.getType())) {
                if (deviceService.countDeviceByStationId(categoryDO.getId()).compareTo(0L) > 0) {
                    throw exception(ErrorCodeConstants.PROJECT_STATION_CHILDREN_ADD_ERROR);
                }
            }
            projectCategory.setCategoryLevel(categoryDO.getCategoryLevel() + 1);
        }
        projectCategoryMapper.insert(projectCategory);
        return projectCategory.getId();
    }

    @Override
    @Transactional
    public void updateProjectCategory(ProjectCategorySaveReqVO updateReqVO) {
        //校验项目存在
        projectService.validateProjectExists(updateReqVO.getProjectId());
        //校验工程目录存在
        ProjectCategoryDO categoryDO = validateProjectCategoryExists(updateReqVO.getId());
        if (Objects.isNull(categoryDO.getParentId())) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_UPDATE_PARENT_ERROR);
        }
        updateReqVO.setParentId(categoryDO.getParentId());
        //名称不能重复
        validateProjectCategoryNameExists(updateReqVO.getId(), updateReqVO.getParentId(), updateReqVO.getProjectId(), updateReqVO.getCategoryName(), updateReqVO.getCategoryType(), updateReqVO.getBusinessId());
        // 更新
        ProjectCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ProjectCategoryDO.class);
        //如果是仪器类型，则需要判断businessId
        if (Objects.equals(updateReqVO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            InstrumentDO instrumentDO = instrumentService.getInstrument(updateReqVO.getBusinessId());
            if (Objects.isNull(instrumentDO)) {
                throw exception(ErrorCodeConstants.INSTRUMENT_NOT_EXISTS);
            }
            updateObj.setCategoryName(instrumentDO.getInstrumentName());
            updateObj.setBusinessId(instrumentDO.getId());
        }
        projectCategoryMapper.updateById(updateObj);
    }

    /**
     * 校验工程结构名称
     *
     * @param id
     * @param parentId
     * @param projectId
     * @param categoryName
     * @param categoryType
     * @param businessId
     */
    private void validateProjectCategoryNameExists(Long id, Long parentId, Long projectId, String categoryName, Integer categoryType, Long businessId) {
        //校验名称是否存在
        if (StringUtils.isBlank(categoryName) && !Objects.equals(categoryType, CategoryTypeEnum.INSTRUMENT.getType())) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_NAME_EMPTY_ERROR);
        }
        //如果是仪器类型，须校验选择的节点
        if (Objects.equals(categoryType, CategoryTypeEnum.INSTRUMENT.getType())) {
            ProjectCategoryDO categoryDO = getProjectCategory(parentId);
            if (Objects.isNull(categoryDO) || !Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STRUCT.getType())) {
                throw exception(ErrorCodeConstants.POINT_SELECT_STRUCT_ERROR);
            }
            List<ProjectCategoryDO> childrenList = getProjectCategoryListByParentId(parentId, false, false);
            if (!CollectionUtils.isEmpty(childrenList) && Objects.equals(childrenList.get(0).getCategoryType(), CategoryTypeEnum.STRUCT.getType())) {
                throw exception(ErrorCodeConstants.PROJECT_STRUCT_INSTRUMENT_ADD_ERROR);
            }
        }
        //如果是分组，须校验选择的节点
        if (Objects.equals(categoryType, CategoryTypeEnum.GROUP.getType())) {
            ProjectCategoryDO categoryDO = validateProjectCategoryExists(parentId);
            if (Objects.isNull(categoryDO) || !Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
                throw exception(ErrorCodeConstants.POINT_SELECT_INSTRUMENT_ERROR);
            }
        }
        //如果是监测站点或工程结构或者分组，直接查询名称不能重复
        if (Objects.equals(categoryType, CategoryTypeEnum.STATION.getType()) || Objects.equals(categoryType, CategoryTypeEnum.STRUCT.getType())
                || Objects.equals(categoryType, CategoryTypeEnum.GROUP.getType())) {
            LambdaQueryWrapperX<ProjectCategoryDO> wrapperX = new LambdaQueryWrapperX<ProjectCategoryDO>()
                    .eq(ProjectCategoryDO::getCategoryName, categoryName).eq(ProjectCategoryDO::getProjectId, projectId);
            if (Objects.equals(categoryType, CategoryTypeEnum.STATION.getType()) || Objects.equals(categoryType, CategoryTypeEnum.STRUCT.getType())) {
                wrapperX.eq(ProjectCategoryDO::getCategoryType, categoryType);
            }
            wrapperX.last("limit 1");
            ProjectCategoryDO categoryDO = projectCategoryMapper.selectOne(wrapperX);
            if (Objects.nonNull(categoryDO) && (Objects.isNull(id) || !Objects.equals(id, categoryDO.getId()))) {
                throw exception(PROJECT_CATEGORY_NAME_EXISTS);
            }
        }
        //如果是仪器类型，则在同一个工程结构下不能重复
        if (Objects.equals(categoryType, CategoryTypeEnum.INSTRUMENT.getType())) {
            ProjectCategoryDO categoryDO = projectCategoryMapper.selectOne(new LambdaQueryWrapperX<ProjectCategoryDO>()
                    .eq(ProjectCategoryDO::getProjectId, projectId).eq(ProjectCategoryDO::getParentId, parentId)
                    .eq(ProjectCategoryDO::getBusinessId, businessId).last("limit 1"));
            if (Objects.nonNull(categoryDO) && (Objects.isNull(id) || !Objects.equals(id, categoryDO.getId()))) {
                throw exception(ErrorCodeConstants.POINT_SELECT_INSTRUMENT_EXISTS);
            }
        }
    }

    @Override
    @Transactional
    public void deleteProjectCategory(Long id) {
        // 校验存在
        ProjectCategoryDO categoryDO = validateProjectCategoryExists(id);
        if (Objects.isNull(categoryDO.getParentId())) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_DELETE_PARENT_ERROR);
        }
        //有子节点时，不允许删除
        List<ProjectCategoryDO> categoryChildren = getProjectCategoryListByParentId(id, false, false);
        if (!CollectionUtils.isEmpty(categoryChildren)) {
            if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STATION.getType())) {
                throw exception(ErrorCodeConstants.PROJECT_CATEGORY_STATION_CHILDREN_EXISTS);
            } else if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STRUCT.getType())
                    && Objects.equals(categoryDO.getCategoryType(), categoryChildren.get(0).getCategoryType())) {
                throw exception(ErrorCodeConstants.PROJECT_CATEGORY_STRUCT_CHILDREN_EXISTS);
            } else if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STRUCT.getType())
                    && !Objects.equals(categoryDO.getCategoryType(), categoryChildren.get(0).getCategoryType())) {
                throw exception(ErrorCodeConstants.PROJECT_CATEGORY_STRUCT_POINT_EXISTS);
            } else {
                throw exception(ErrorCodeConstants.PROJECT_CATEGORY_POINT_EXISTS);
            }
        }
        //工程结构有测点时，不允许删除
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())
                || Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())) {
            if (pointService.getPointListByCategoryId(id).size() > 0) {
                throw exception(ErrorCodeConstants.POINT_INSTRUMENT_STRUCT_EXISTS, Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType()) ? "仪器类型" : "分组");
            }
        }
        //监测站点，有绑定采集仪时，不允许删除
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.STATION.getType()) && deviceService.countDeviceByStationId(id) > 0) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_STATION_DEVICE_EXISTS);
        }
        projectCategoryMapper.deleteById(id);
    }

    @Override
    public ProjectCategoryDO validateProjectCategoryExists(Long id) {
        ProjectCategoryDO categoryDO = projectCategoryMapper.selectById(id);
        if (Objects.isNull(categoryDO)) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_NOT_EXISTS);
        }
        return categoryDO;
    }

    @Override
    public List<ProjectCategoryDO> getProjectCategoryListByCategoryType(Long projectId, Integer categoryType) {
        return projectCategoryMapper.selectList(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getProjectId, projectId)
                .eq(ProjectCategoryDO::getCategoryType, categoryType)
                .orderByAsc(ProjectCategoryDO::getId));
    }

    @Override
    public List<ProjectCategoryDO> getProjectCategoryListByParentId(Long parentId, boolean allChildren, boolean sameCategoryType) {
        ProjectCategoryDO categoryDO = validateProjectCategoryExists(parentId);
        List<ProjectCategoryDO> categoryDOList = projectCategoryMapper.selectList(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getParentId, parentId)
                .eq(sameCategoryType, ProjectCategoryDO::getCategoryType, categoryDO.getCategoryType())
                .orderByAsc(ProjectCategoryDO::getId));
        //如果查询所有子节点，需要递归
        if (allChildren && !CollectionUtils.isEmpty(categoryDOList)) {
            List<ProjectCategoryDO> categoryDOListAll = new ArrayList<>();
            categoryDOList.forEach(item -> {
                categoryDOListAll.addAll(getProjectCategoryListByParentId(item.getId(), true, sameCategoryType));
            });
            categoryDOList.addAll(categoryDOListAll);
        }
        return categoryDOList;
    }

    @Override
    public List<ProjectCategoryBO> getProjectCategoryListByCategoryTypeAndId(ProjectCategoryListReqVO reqVO) {
        Long projectId = reqVO.getProjectId(), categoryId = reqVO.getCategoryId();
        Integer categoryType = reqVO.getCategoryType(), filterPoint = reqVO.getFilterPoint();
        List<Integer> pointTypeList = reqVO.getPointTypeList();
        //如果是监测站点或者工程结构，直接返回
        if (Objects.equals(categoryType, CategoryTypeEnum.STATION.getType()) || Objects.equals(categoryType, CategoryTypeEnum.STRUCT.getType())) {
            return BeanUtils.toBean(getProjectCategoryListByCategoryType(projectId, categoryType), ProjectCategoryBO.class);
        }
        //如果是查询测点信息管理树，需要返回所有的
        if (Objects.equals(categoryType, CategoryTypeEnum.INSTRUMENT.getType())) {
            List<ProjectCategoryDO> categoryDOList = getProjectCategoryListByProjectId(projectId);
            categoryDOList.removeIf(item -> Objects.equals(item.getCategoryType(), CategoryTypeEnum.STATION.getType()));
            return BeanUtils.toBean(categoryDOList, ProjectCategoryBO.class);
        }
        //如果是查询单个仪器类型下的分组树
        if (Objects.equals(categoryType, CategoryTypeEnum.GROUP.getType())) {
            ProjectCategoryDO categoryDO = getProjectCategory(categoryId);
            //没有选择仪器类型，提示请选择仪器类型
            if (Objects.isNull(categoryDO) || !Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
                throw exception(ErrorCodeConstants.POINT_SELECT_INSTRUMENT_ERROR);
            }
            return BeanUtils.toBean(getProjectCategoryListByParentId(categoryId, false, false), ProjectCategoryBO.class);
        }
        //如果是查询测点
        if (Objects.equals(categoryType, CategoryTypeEnum.POINT.getType())) {
            List<ProjectCategoryDO> categoryDOList = getProjectCategoryListByProjectId(projectId);
            categoryDOList.removeIf(item -> Objects.equals(item.getCategoryType(), CategoryTypeEnum.STATION.getType()));
            List<ProjectCategoryBO> categoryBOListAll = BeanUtils.toBean(categoryDOList, ProjectCategoryBO.class);
            for (ProjectCategoryDO categoryDO : categoryDOList) {
                //如果是仪器类型或者分组，就查询测点
                if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())
                        || Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())) {
                    List<PointDO> pointDOList = pointService.getPointListByCategoryId(categoryDO.getId());
                    if (!CollectionUtils.isEmpty(pointDOList)) {
                        //过滤未绑定测点的
                        if (Objects.equals(filterPoint, FilterPointEnum.YES.getType())) {
                            pointDOList = pointDOList.stream().filter(item -> Objects.equals(item.getBindType(), BindTypeEnum.NO.getType())).toList();
                        }
                        //过滤测点的类型
                        if (!CollectionUtils.isEmpty(pointTypeList)) {
                            pointDOList = pointDOList.stream().filter(item -> pointTypeList.contains(item.getPointType())).toList();
                        }
                        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())){
                            InstrumentDO instrument = instrumentService.getInstrument(categoryDO.getBusinessId());
                            if (null != instrument && null != instrument.getTemplateId()){
                                InstrumentTemplateDO instrumentTemplateDO = instrumentTemplateMapper.selectById(instrument.getTemplateId());
                                if(instrumentTemplateDO!=null){
                                    categoryDO.setImagePath(instrumentTemplateDO.getIconUrl()==null?"":instrumentTemplateDO.getIconUrl());
                                }
                            }
                        }
                        //建立测点树
                        categoryBOListAll.addAll(pointDOList.stream().map(item -> {
                            ProjectCategoryBO projectCategoryBO = new ProjectCategoryBO();
                            projectCategoryBO.setId(item.getId() + "");
                            projectCategoryBO.setProjectId(item.getProjectId());
                            projectCategoryBO.setParentId(categoryDO.getId() + "");
                            projectCategoryBO.setCategoryName(item.getPointCode());
                            projectCategoryBO.setCategoryType(CategoryTypeEnum.POINT.getType());
                            projectCategoryBO.setCategoryLevel(categoryDO.getCategoryLevel() + 1);
                            projectCategoryBO.setPointType(item.getPointType());
                            projectCategoryBO.setImagePath(categoryDO.getImagePath());
                            return projectCategoryBO;
                        }).toList());
                    }
                }
            }
            return categoryBOListAll;
        }
        //如果是查询网络拓扑
        if (Objects.equals(categoryType, CategoryTypeEnum.NET_DEVICE_MCU.getType())) {
            //查找当前项目的测站
            List<ProjectCategoryBO> categoryBOList = BeanUtils.toBean(getProjectCategoryListByCategoryType(projectId, CategoryTypeEnum.STATION.getType()), ProjectCategoryBO.class);
            if (!CollectionUtils.isEmpty(categoryBOList)) {
                List<ProjectCategoryBO> categoryDOListAll = new ArrayList<>(categoryBOList);
                categoryBOList.forEach(item -> {
                    //只有子节点才可以查看测站
                    if (Objects.nonNull(item.getParentId()) && countByProjectIdAndParentId(projectId, Long.valueOf(item.getId())).compareTo(0L) == 0) {
                        //查找测站绑定的采集仪
                        List<DeviceDO> parentDeviceDOList = deviceService.getDeviceDOListByProjectIdAndStationId(projectId, Long.valueOf(item.getId()));
                        //网关设备添加到网络拓扑中
                        if (!CollectionUtils.isEmpty(parentDeviceDOList)) {
                            parentDeviceDOList.forEach(item2 -> {
                                ProjectCategoryBO projectCategoryBONet = new ProjectCategoryBO();
                                projectCategoryBONet.setId(item2.getDeviceCode());
                                projectCategoryBONet.setProjectId(item2.getProjectId());
                                projectCategoryBONet.setParentId(item.getId());
                                projectCategoryBONet.setCategoryName(item2.getDeviceName());
                                projectCategoryBONet.setCategoryType(CategoryTypeEnum.NET_DEVICE_MCU.getType());
                                projectCategoryBONet.setCategoryLevel(item.getCategoryLevel() + 1);
                                projectCategoryBONet.setLinkState(item2.getLinkState());
                                categoryDOListAll.add(projectCategoryBONet);
                                //将网关子设备添加到网络拓扑中
                                List<DeviceDO> childrenDeviceDOList = deviceService.getDeviceDOListByProjectIdAndParentCode(projectId, item2.getDeviceCode());
                                if (!CollectionUtils.isEmpty(childrenDeviceDOList)) {
                                    categoryDOListAll.addAll(childrenDeviceDOList.stream().map(item3 -> {
                                        ProjectCategoryBO projectCategoryBOSub = new ProjectCategoryBO();
                                        projectCategoryBOSub.setId(item3.getDeviceCode());
                                        projectCategoryBOSub.setProjectId(item3.getProjectId());
                                        projectCategoryBOSub.setParentId(projectCategoryBONet.getId());
                                        projectCategoryBOSub.setCategoryName(String.format(DamConstant.NET_TOPOLOGY_DEVICE, item3.getMcuChannel(),
                                                item3.getDeviceName(), getBindTypeOrPointName(item3.getPointId(), item3.getBindType())));
                                        if (Objects.equals(item3.getBindType(), BindTypeEnum.YES.getType())) {
                                            projectCategoryBOSub.setBusinessId(item3.getPointId());
                                        }
                                        projectCategoryBOSub.setLinkState(item3.getLinkState());
                                        projectCategoryBOSub.setCategoryType(CategoryTypeEnum.NET_DEVICE_SUB.getType());
                                        projectCategoryBOSub.setCategoryLevel(projectCategoryBONet.getCategoryLevel() + 1);
                                        return projectCategoryBOSub;
                                    }).toList());
                                }
                            });
                        }
                    }
                });
                return categoryDOListAll;
            }
        }
        // 查询的是监测图形所需的数据
        if (Objects.equals(categoryType, CategoryTypeEnum.MONITORING_GRAPH.getType())) {
            List<ProjectCategoryDO> categoryDOList = getProjectCategoryListByProjectId(projectId);
            categoryDOList.removeIf(item -> Objects.equals(item.getCategoryType(), CategoryTypeEnum.STATION.getType()));
            categoryDOList.removeIf(item -> Objects.equals(item.getCategoryType(), CategoryTypeEnum.GROUP.getType()));
            return BeanUtils.toBean(categoryDOList, ProjectCategoryBO.class);
        }
        return null;
    }

    /**
     * 获取网络拓扑显示名称
     *
     * @param pointId
     * @param bindType
     * @return
     */
    private String getBindTypeOrPointName(Long pointId, Integer bindType) {
        if (Objects.isNull(pointId) || Objects.equals(bindType, BindTypeEnum.NO.getType())) {
            return BindTypeEnum.NO.getDesc();
        }
        PointDO pointDO = pointService.getPoint(pointId);
        return Objects.isNull(pointDO) ? "" : pointDO.getPointCode();
    }

    @Override
    public List<ProjectCategoryDO> getProjectCategoryListByProjectId(Long projectId) {
        return projectCategoryMapper.selectList(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getProjectId, projectId).orderByAsc(ProjectCategoryDO::getId));
    }

    @Override
    public Boolean updateProjectCategoryName(String categoryNameOrigin, String categoryName, Integer categoryType) {
        List<ProjectCategoryDO> categoryDO = getProjectCategoryByName(categoryNameOrigin, categoryType);
        if (!CollectionUtils.isEmpty(categoryDO)) {
            categoryDO.forEach(category -> category.setCategoryName(categoryName));
            return projectCategoryMapper.updateBatch(categoryDO);
        }
        return false;
    }

    @Override
    public Boolean updateProjectCategoryName(Long projectId, String categoryNameOrigin, String categoryName, Integer categoryType) {
        List<ProjectCategoryDO> categoryDO = projectCategoryMapper.selectList(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getCategoryName, categoryNameOrigin)
                .eq(ProjectCategoryDO::getCategoryType, categoryType).eq(ProjectCategoryDO::getProjectId, projectId));
        if (!CollectionUtils.isEmpty(categoryDO)) {
            categoryDO.forEach(category -> category.setCategoryName(categoryName));
            return projectCategoryMapper.updateBatch(categoryDO);
        }
        return false;
    }


    @Override
    public ProjectCategoryDO getProjectCategoryParent(Long id) {
        ProjectCategoryDO categoryDO = validateProjectCategoryExists(id);
        return getProjectCategory(categoryDO.getParentId());
    }

    @Override
    public ProjectCategoryDO getProjectCategoryId(Long id) {
        return projectCategoryMapper.selectById(id);
    }

    @Override
    public List<ProjectCategoryDO> getProjectCategoryByName(String categoryName, Integer categoryType) {
        return projectCategoryMapper.selectList(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getCategoryName, categoryName)
                .eq(ProjectCategoryDO::getCategoryType, categoryType));
    }

    @Override
    public ProjectCategoryLevelBO getProjectCategoryLevelBO(Long id) {
        ProjectCategoryLevelBO categoryLevelBO = new ProjectCategoryLevelBO();
        ProjectCategoryDO categoryDO = getProjectCategory(id);
        //如果是测点
        if (Objects.isNull(categoryDO)) {
            PointDO pointDO = pointService.getPoint(id);
            if (Objects.isNull(pointDO)) {
                throw exception(ErrorCodeConstants.POINT_NOT_EXISTS);
            }
            categoryDO = getProjectCategory(pointDO.getCategoryId());
        }
        if (Objects.isNull(categoryDO)) {
            throw exception(ErrorCodeConstants.PROJECT_CATEGORY_NOT_EXISTS);
        }
        //如果是分组
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())) {
            ProjectCategoryDO categoryDOInstrument = getProjectCategoryId(categoryDO.getParentId());
            if (Objects.isNull(categoryDOInstrument)) {
                throw exception(PROJECT_CATEGORY_INSTRUMENT_DELETED);
            }
            categoryLevelBO.setInstrumentCategoryDO(categoryDOInstrument);
            categoryLevelBO.setStructCategoryDO(getProjectCategoryId(categoryDOInstrument.getParentId()));
        } else if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            //如果是仪器类型
            categoryLevelBO.setInstrumentCategoryDO(categoryDO);
            categoryLevelBO.setStructCategoryDO(getProjectCategoryId(categoryDO.getParentId()));
        }
        return categoryLevelBO;
    }

    @Override
    public String getAllPathCategoryName(Long id, String format) {
        ProjectCategoryDO categoryDO = getProjectCategory(id);
        if (Objects.isNull(categoryDO)) {
            return "";
        }
        if (Objects.isNull(categoryDO.getParentId())) {
            return categoryDO.getCategoryName();
        }
        return getAllPathCategoryName(categoryDO.getParentId(), format) + format + categoryDO.getCategoryName();
    }

    @Override
    public List<Long> getPointListByCategoryId(Long id) {
        List<Long> pointIdList = new ArrayList<>();
        //查找工程分类
        ProjectCategoryDO categoryDO = getProjectCategoryId(id);
        if (Objects.isNull(categoryDO)) {
            //如果工程分类是空，则id是测点id，进行测点查找
            PointBO pointBO = pointService.getPointBO(id);
            if (Objects.isNull(pointBO)) {
                throw exception(PROJECT_CATEGORY_NOT_EXISTS);
            }
            //将测点加入集合，证明点击的是测点
            pointIdList.add(id);
            return pointIdList;
        }
        //获取工程分类下挂靠的测点
        getPointList(categoryDO, pointIdList);
        return pointIdList;
    }

    private void getPointList(ProjectCategoryDO categoryDO, List<Long> pointIdList) {
        //如果节点是分组或仪器类型，那么查找相应节点下的测点
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.GROUP.getType())
                || Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            List<PointDO> pointBOList = pointService.getPointListByCategoryId(categoryDO.getId());
            if (CollectionUtil.isNotEmpty(pointBOList)) {
                pointIdList.addAll(pointBOList.stream().map(PointDO::getId).toList());
            }
        }
        //如果是仪器类型，则查找分组，将分组下的测点加入
        if (Objects.equals(categoryDO.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType())) {
            List<ProjectCategoryDO> projectCategoryDOS = getProjectCategoryListByParentId(categoryDO.getId(), false, false);
            if (CollectionUtil.isNotEmpty(projectCategoryDOS)) {
                projectCategoryDOS.forEach(item -> {
                    getPointList(item, pointIdList);
                });
            }
        }
    }


    @Override
    public ProjectCategoryDO getProjectCategory(Long id) {
        return projectCategoryMapper.selectById(id);
    }

    @Override
    public Long getInstrumentIdByPointId(Long pointId) {
        return getProjectCategoryLevelBO(pointId).getInstrumentCategoryDO().getBusinessId();
    }

    @Override
    public Long countByProjectIdAndParentId(Long projectId, Long parentId) {
        Long countChildren = projectCategoryMapper.selectCount(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getProjectId, projectId).eq(ProjectCategoryDO::getParentId, parentId));
        return Objects.isNull(countChildren) ? 0L : countChildren;
    }

    @Override
    public void deleteInstrumentAndGroup(Long instrumentId) {
        //查询仪器类型下的主键id
        List<ProjectCategoryDO> instrumentDOList = projectCategoryMapper.selectList(new LambdaQueryWrapperX<ProjectCategoryDO>()
                .eq(ProjectCategoryDO::getBusinessId, instrumentId));
        if (!CollectionUtils.isEmpty(instrumentDOList)) {
            List<Long> instrumentIdList = instrumentDOList.stream().map(ProjectCategoryDO::getId).toList();
            //删除仪器类型下的分组
            if (!CollectionUtils.isEmpty(instrumentIdList)) {
                projectCategoryMapper.delete(new LambdaQueryWrapperX<ProjectCategoryDO>()
                        .in(ProjectCategoryDO::getParentId, instrumentIdList));
            }
            projectCategoryMapper.delete(new LambdaQueryWrapperX<ProjectCategoryDO>()
                    .eq(ProjectCategoryDO::getBusinessId, instrumentId));
        }
    }

    @Override
    public NameRespVO getProjectCategoryMeasure(Long categoryId) {
        ProjectCategoryLevelBO categoryLevelBO = getProjectCategoryLevelBO(categoryId);
        InstrumentDO instrumentDO = instrumentService.getInstrument(categoryLevelBO.getInstrumentCategoryDO().getBusinessId());
        return NameRespVO.builder().instrumentName(categoryLevelBO.getInstrumentCategoryDO().getCategoryName())
                .structCategoryName(categoryLevelBO.getStructCategoryDO().getCategoryName()).measureItem(instrumentDO.getMeasureItem()).build();
    }

    @Override
    public List<ProjectCategoryBO> getProjectInstrumentListByCategoryTypeAndId(ProjectCategoryListReqVO reqVO, String instrumentName) {
        List<ProjectCategoryBO> projectCategoryBO = getProjectCategoryListByCategoryTypeAndId(reqVO);
        Iterator<ProjectCategoryBO> iterator = projectCategoryBO.iterator();
        while (iterator.hasNext()) {
            ProjectCategoryBO item = iterator.next();
            //筛选不同仪器类型
            boolean isDirectRemove = !Objects.equals(item.getCategoryName(), instrumentName) && Objects.equals(item.getCategoryType(), CategoryTypeEnum.INSTRUMENT.getType());
            //筛选不同仪器类型的孩子节点
            boolean isChildRemove = isChildOfInstrument(item.getId(), CategoryTypeEnum.INSTRUMENT.getType(), instrumentName);
            if (isDirectRemove || isChildRemove) {
                iterator.remove();
            }
        }
        return projectCategoryBO;
    }

    private boolean isChildOfInstrument(String itemId, Integer instrumentCategoryType, String instrumentName) {
        if (itemId != null) {
            ProjectCategoryBO item = BeanUtils.toBean(getProjectCategory(Long.parseLong(itemId)), ProjectCategoryBO.class);
            if (item != null && item.getParentId() != null) {
                ProjectCategoryBO parent = BeanUtils.toBean(getProjectCategory(Long.parseLong(item.getParentId())), ProjectCategoryBO.class);
                if (parent != null) {
                    return Objects.equals(parent.getCategoryType(), instrumentCategoryType) && !Objects.equals(parent.getCategoryName(), instrumentName);
                }
            }
        }
        return false;
    }


}