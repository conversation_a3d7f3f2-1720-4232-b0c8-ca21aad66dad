package cn.powerchina.bjy.link.dam.dal.dataobject.pointdata;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 测点数据 DO
 *
 * <AUTHOR>
 */
@TableName("dam_point_data")
@KeySequence("dam_point_data_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointDataDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 监测时间
     */
    private LocalDateTime pointTime;
    /**
     * 仪器类型id
     */
    private Long instrumentId;
    /**
     * 分量id
     */
    private Long instrumentModelId;
    /**
     * 分量标识符
     */
    private String thingIdentity;
    /**
     * 分量名称
     */
    private String thingName;
    /**
     * 分量原始值
     */
    private BigDecimal thingValueOrigin;
    /**
     * 分量值
     */
    private BigDecimal thingValue;
    /**
     * 分量值绝对值
     */
    private BigDecimal absoluteValue;
    /**
     * 采集类型(1：自动化采集，2：人工录入）
     */
    private Integer dataType;
    /**
     * 数据状态(0：未审核，1：正常数据，2：异常数据，3：错误数据）
     */
    private Integer dataStatus;
    /**
     * 回滚测点数据导入id
     */
    private Long rollbackImportId;

}