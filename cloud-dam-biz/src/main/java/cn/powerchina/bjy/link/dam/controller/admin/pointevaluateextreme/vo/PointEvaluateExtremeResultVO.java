package cn.powerchina.bjy.link.dam.controller.admin.pointevaluateextreme.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测点评价指标极值计算结果 Request VO")
@Data
public class PointEvaluateExtremeResultVO {

    @Schema(description = "正常最大值")
    @NotNull(message = "请输入正常最大值")
    private String waringUp;

    @Schema(description = "正常最小值")
    @NotNull(message = "请输入正常最小值")
    private String waringDown;

    @Schema(description = "异常最大值")
    @NotNull(message = "请输入异常最大值")
    private String abnormalUp;

    @Schema(description = "异常最小值")
    @NotNull(message = "请输入异常最小值")
    private String abnormalDown;

}