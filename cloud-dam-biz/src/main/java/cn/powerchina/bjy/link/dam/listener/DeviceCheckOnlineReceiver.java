package cn.powerchina.bjy.link.dam.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.GROUP_DEVICE_STATUS_CHANGE;
import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.TOPIC_DEVICE_STATUS_CHANGE;

/**
 * @Description: 设备状态转发
 * @Author: dzj
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = TOPIC_DEVICE_STATUS_CHANGE, consumerGroup = GROUP_DEVICE_STATUS_CHANGE, requestTimeout = 10, consumptionThreadCount = 10)
public class DeviceCheckOnlineReceiver implements RocketMQListener {

    @Autowired
    private DeviceService deviceService;
    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            DeviceCheckOnlineModel deviceCheckOnlineModel = parseMessageBody(messageView);
            if (deviceCheckOnlineModel == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            log.info("receiver message {}", JSONObject.toJSON(deviceCheckOnlineModel));
            deviceService.updateDeviceCheckOnline(deviceCheckOnlineModel);
        } catch (Exception e) {
            log.error("指令下发属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;

    }


    /**
     * 解析消息体为实体类
     */
    private DeviceCheckOnlineModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceCheckOnlineModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
