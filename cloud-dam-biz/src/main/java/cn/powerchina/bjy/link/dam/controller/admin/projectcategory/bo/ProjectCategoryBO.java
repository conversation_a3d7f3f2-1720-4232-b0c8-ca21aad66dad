package cn.powerchina.bjy.link.dam.controller.admin.projectcategory.bo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/20
 */
@Data
public class ProjectCategoryBO {

    /**
     * 主键id
     */
    private String id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 父节点id
     */
    private String parentId;
    /**
     * 节点名称
     */
    private String categoryName;
    /**
     * 节点类型(1：监测站点，2：工程结构，3：仪器类型，4：分组，5：测点，6：网关设备（采集仪），7：网关子设备（传感器））
     */
    private Integer categoryType;
    /**
     * 层级
     */
    private Integer categoryLevel;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 连接状态（0-离线；1-在线；）
     */
    private Integer linkState;
    
    /**
     * 测量类型，1：人/自一体，2：自动化，3：人工
     */
    private Integer pointType;

    /**
     * 图片地址
     */
    private String imagePath;
}
