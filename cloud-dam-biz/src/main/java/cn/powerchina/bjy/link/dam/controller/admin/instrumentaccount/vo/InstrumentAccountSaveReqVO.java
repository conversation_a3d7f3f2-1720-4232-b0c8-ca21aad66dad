package cn.powerchina.bjy.link.dam.controller.admin.instrumentaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 仪器台账新增/修改 Request VO")
@Data
public class InstrumentAccountSaveReqVO {

    @Schema(description = "主键id", example = "13637")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "生产编号")
    private String productionNumber;

    @Schema(description = "仪器型号")
    private String instrumentModel;

    @Schema(description = "仪器类型id", example = "28404")
    private Long instrumentId;

    @Schema(description = "生产厂商")
    private String instrumentManufacturer;

    @Schema(description = "备注")
    private String remarks;

}