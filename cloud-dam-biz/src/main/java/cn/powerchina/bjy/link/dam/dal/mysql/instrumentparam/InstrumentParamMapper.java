package cn.powerchina.bjy.link.dam.dal.mysql.instrumentparam;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.instrumentparam.vo.InstrumentParamPageReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentparam.InstrumentParamDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仪器类型-计算参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstrumentParamMapper extends BaseMapperX<InstrumentParamDO> {

    default PageResult<InstrumentParamDO> selectPage(InstrumentParamPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstrumentParamDO>()
                .eqIfPresent(InstrumentParamDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(InstrumentParamDO::getInstrumentId, reqVO.getInstrumentId())
                .likeIfPresent(InstrumentParamDO::getThingName, reqVO.getThingName())
                .eqIfPresent(InstrumentParamDO::getThingIdentity, reqVO.getThingIdentity())
                .orderByAsc(InstrumentParamDO::getThingWeight)
                .orderByDesc(InstrumentParamDO::getCreateTime));
    }

}