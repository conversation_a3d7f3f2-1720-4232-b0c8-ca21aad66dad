package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 监测图形-分布图的参数
 * @Author: yang<PERSON>gtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图 Request VO")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @ArraySchema(
            arraySchema = @Schema(description = "测点id"),
            schema = @Schema(implementation = Long.class)
    )
    @NotNull(message = "测点id不能为空")
    @NotEmpty(message = "测点id不能为空")
    private List<Long> pointIdList;

    @ArraySchema(
            arraySchema = @Schema(description = "分量id"),
            schema = @Schema(implementation = Long.class)
    )
    @NotNull(message = "分量id不能为空")
    @NotEmpty(message = "分量id不能为空")
    private List<Long> instrumentModelIdList;

    @Schema(description = "时间间隔")
    @NotNull(message = "时间间隔不能为空")
    private Integer timeInterval;

    @Schema(description = "时间间隔单位（1：日，2：小时）")
    @NotNull(message = "时间间隔单位不能为空")
    private Integer timeIntervalUnit;

    @ArraySchema(
            arraySchema = @Schema(description = "读取日期，yyyy-MM-dd HH:mm:ss格式"),
            schema = @Schema(implementation = String.class)
    )
    @NotNull(message = "读取日期不能为空")
    @NotEmpty(message = "读取日期不能为空")
    private List<String> pointTimeList;

    @ArraySchema(
            arraySchema = @Schema(description = "采集类型(1：自动化采集，2：人工录入）"),
            schema = @Schema(implementation = Integer.class)
    )
    private List<Integer> dataTypeList;

    @ArraySchema(
            arraySchema = @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）"),
            schema = @Schema(implementation = Integer.class)
    )
    private List<Integer> dataStatusList;

    @ArraySchema(
            arraySchema = @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）"),
            schema = @Schema(implementation = Integer.class)
    )
    private List<Integer> reviewStatusList;
}
