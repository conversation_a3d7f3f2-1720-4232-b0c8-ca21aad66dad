package cn.powerchina.bjy.link.dam.framework.rpc.aop;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.USER_ADMIN_NAME_EXISTS;

/**
 * @Description: 描述
 * @Author: zhaoqiang
 * @CreateDate: 2024/9/2
 */
@Aspect
@Component
@Slf4j
public class RpcAdvice {

    @Pointcut("execution(public * cn.powerchina.bjy.cloud.system.api.user.AdminUserApi.*(..)) || " +
            "execution(public * cn.powerchina.bjy.cloud.system.api.permission.RoleApi.*(..)) || " +
            "execution(public * cn.powerchina.bjy.link.iot.api.product.ProductApi.*(..)) || " +
            "execution(public * cn.powerchina.bjy.cloud.system.api.permission.PermissionApi.*(..))")
    public void log() {
    }

    @Around("log()")
    public Object around(ProceedingJoinPoint point) {
        Object result;
        try {
//            log.info("begin call cloud-system  {} method", point.getSignature().getName());
            result = point.proceed();
//            log.info("call cloud-system  {} method end", point.getSignature().getName());
        } catch (Throwable e) {
            throw exception(ErrorCodeConstants.RPC_EXCEPTION, e.getMessage());
        }

        if (result instanceof CommonResult) {
            if (((CommonResult<?>) result).isSuccess()) {
                return result;
            } else {
                CommonResult commonResult = (CommonResult) result;
                String msg = commonResult.getMsg();
                try {
                    if (Objects.equals(((CommonResult<?>) result).getCode(), cn.powerchina.bjy.cloud.system.enums.ErrorCodeConstants.ROLE_NAME_DUPLICATE.getCode())) {
                        String[] msgArr = msg.split("-");
                        msg = msgArr[0] + "】" + msgArr[1].split("】")[1];
                    }
                } catch (Exception e) {

                }
                log.error("call other server  {} method error {}", point.getSignature().getName(), commonResult.getMsg());
                throw exception(ErrorCodeConstants.RPC_ERROR, msg);
            }
        }
        return result;
    }
}
