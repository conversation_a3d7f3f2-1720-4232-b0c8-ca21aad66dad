package cn.powerchina.bjy.link.dam.dal.dataobject.iotdevice;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 物联网平台同步的设备 DO
 *
 * <AUTHOR>
 */
@TableName("dam_iot_device")
@KeySequence("dam_iot_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotDeviceDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 物联网平台库的设备id
     */
    private Long iotId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 父设备号（网关）
     */
    private String parentCode;

    /**
     * 父设备名称（网关）
     */
    private String parentName;

    /**
     * 设备唯一标识
     */
    private String deviceSerial;

    /**
     * 是否启用设备影子
     * 0:禁用; 1:启用
     * 默认启用
     */
    private Integer shadow;

    /**
     * 通道编码
     */
    private String channelCode;

    /**
     * mcu通道编码
     */
    private String mcuChannel;

    /**
     * 从站号
     */
    private String slaveId;

    /**
     * 下发状态
     * 0:未下发; 1:已下发
     */
    private Integer distributeState;

    /**
     * 连接状态
     * 0:离线; 1:在线
     */
    private Integer linkState;

    /**
     * 注册状态
     * 0:未注册; 1:已注册
     */
    private Integer registerState;

    /**
     * 节点类型
     * 0:直连; 1:网关; 2:网关子设备
     */
    private Integer nodeType;

    /**
     * 差异化扩展
     */
    private String extra;

    /**
     * 最后上线时间
     */
    private LocalDateTime lastUpTime;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 网关实例编码
     */
    private String edgeCode;

    /**
     * 驱动编码
     */
    private String driverCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 描述
     */
    private String remark;

    /**
     * 厂商
     */
    private String firmName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;

    /**
     * 空间名称
     */
    private String spaceName;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 物联网平台库的设备创建时间
     */
    private LocalDateTime iotCreateTime;

}
