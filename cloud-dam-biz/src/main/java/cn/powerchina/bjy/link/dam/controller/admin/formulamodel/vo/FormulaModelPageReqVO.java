package cn.powerchina.bjy.link.dam.controller.admin.formulamodel.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 公式关联分量分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FormulaModelPageReqVO extends PageParam {

    @Schema(description = "测点公式id", example = "10774")
    private Long pointFormulaId;

    @Schema(description = "分量id", example = "31788")
    private Long instrumentModelId;

    @Schema(description = "取值条件，1：无，2：相对测值，3：首次测值：所有测次中第一次测值，4：时间范围内测值")
    private Integer dataCondition;

    @Schema(description = "数值或1：之前，2：之后")
    private Integer dataValue;

    @Schema(description = "第n条测值或单位，1：分钟，2：小时，3：天")
    private Integer dataUnit;

    @Schema(description = "指定时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] specifyTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}