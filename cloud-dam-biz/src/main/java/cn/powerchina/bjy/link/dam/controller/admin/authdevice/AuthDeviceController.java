package cn.powerchina.bjy.link.dam.controller.admin.authdevice;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthBindDeviceReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthDeviceGroupRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.authdevice.vo.AuthDeviceSaveReqVO;
import cn.powerchina.bjy.link.dam.service.authdevice.AuthDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备授权")
@RestController
@RequestMapping("/dam/auth/device/group")
@Validated
public class AuthDeviceController {

    @Resource
    private AuthDeviceService authDeviceService;

    @PostMapping("/save")
    @Operation(summary = "保存设备分组授权")
//    @PreAuthorize("@ss.hasPermission('dam:auth-device:create')")
    public CommonResult<Long> saveAuthDevice(@Valid @RequestBody AuthDeviceSaveReqVO reqVO) {
        return success(authDeviceService.saveAuthDevice(reqVO));
    }
    @PostMapping("/bind")
    @Operation(summary = "添加绑定设备")
    public CommonResult<Long> bind(@Valid @RequestBody AuthBindDeviceReqVO reqVO) {
        return success(authDeviceService.bindDevice(reqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得已授权分组")
    @Parameter(name = "projectId", description = "项目id", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:auth-device:query')")
    public CommonResult<List<Long>> getAuthDeviceGroupIdList(@RequestParam("projectId") Long projectId) {
        return success(authDeviceService.getAuthDeviceGroupIdList(null, projectId));
    }

    @GetMapping("/list")
    @Operation(summary = "获得设备分组列表")
//    @PreAuthorize("@ss.hasPermission('dam:auth-device:query')")
    public CommonResult<List<AuthDeviceGroupRespVO>> getAuthDeviceGroupList() {
        return success(BeanUtils.toBean(authDeviceService.getAuthDeviceGroupList(null), AuthDeviceGroupRespVO.class));
    }

}