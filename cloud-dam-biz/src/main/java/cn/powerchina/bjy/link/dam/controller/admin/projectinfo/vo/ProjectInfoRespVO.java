package cn.powerchina.bjy.link.dam.controller.admin.projectinfo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 项目工程信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectInfoRespVO {

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "项目名称")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "工程信息")
    @ExcelProperty("工程信息")
    private String projectInfo;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}