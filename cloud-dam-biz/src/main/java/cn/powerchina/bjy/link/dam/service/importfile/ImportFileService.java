package cn.powerchina.bjy.link.dam.service.importfile;

import java.util.*;
import jakarta.validation.*;
import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importfile.ImportFileDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * 导入信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ImportFileService {

    /**
     * 创建导入信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImportFile(@Valid ImportFileSaveReqVO createReqVO);

    /**
     * 更新导入信息
     *
     * @param updateReqVO 更新信息
     */
    void updateImportFile(@Valid ImportFileSaveReqVO updateReqVO)throws Exception;

    /**
     * 删除导入信息
     *
     * @param id 编号
     */
    void deleteImportFile(Long id);

    /**
    * 批量删除导入信息
    *
    * @param ids 编号
    */
    void deleteImportFileListByIds(List<Long> ids);

    /**
     * 获得导入信息
     *
     * @param id 编号
     * @return 导入信息
     */
    ImportFileDO getImportFile(Long id);

    /**
     * 获得导入信息分页
     *
     * @param pageReqVO 分页查询
     * @return 导入信息分页
     */
    PageResult<ImportFileDO> getImportFilePage(ImportFilePageReqVO pageReqVO);

    Boolean importExcel( @Valid UploadFileVO uploadFileVO);

    List<ImportFileRespVO> getFIleList(Long projectId, Long nodeId);


    Boolean importFile(@Valid ImportFileVO importFileVO);

    void moveFileExcel(@Valid ImportFileMoveVO importFileMoveVO);
}