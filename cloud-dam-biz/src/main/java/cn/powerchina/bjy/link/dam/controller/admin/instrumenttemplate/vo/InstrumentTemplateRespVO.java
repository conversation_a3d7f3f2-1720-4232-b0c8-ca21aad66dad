package cn.powerchina.bjy.link.dam.controller.admin.instrumenttemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 仪器类型模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstrumentTemplateRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4056")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "仪器类型模板名称", example = "王五")
    @ExcelProperty("仪器类型模板名称")
    private String instrumentName;

    @Schema(description = "测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它")
    @ExcelProperty("测量原理，1：差阻式，2：振弦式，3：电容式，4：电感式，5：其它")
    private Integer measurePrinciple;

    @Schema(description = "监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    @ExcelProperty("监测项目，1：变形，2：渗流渗压，3：应力应变及温度，4：环境量")
    private Integer measureItem;

    @Schema(description = "图标地址")
    @ExcelProperty("图标地址")
    private String iconUrl;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}