package cn.powerchina.bjy.link.dam.controller.admin.instrumentmodel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 仪器类型-测量分量新增/修改 Request VO")
@Data
public class InstrumentModelSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "项目id")
    @NotNull(message = "项目不能为空")
    private Long projectId;

    @Schema(description = "仪器类型id")
    private Long instrumentId;

    @Schema(description = "分量名称")
    @Length(max = 32)
    private String thingName;

    @Schema(description = "标识符")
    @Length(max = 32)
    private String thingIdentity;

    @Schema(description = "单位")
    @Length(max = 32)
    private String thingUnit;

    @Schema(description = "数据类型，1：整数型，2：浮点型，3：双精度")
    private Integer dataType;

    @Schema(description = "下限")
    private String downLimit;

    @Schema(description = "上限")
    private String upLimit;

    @Schema(description = "小数位")
    @Min(0)
    @Max(15)
    private Integer decimalLimit;

    @Schema(description = "分量类型，1：原始值，2：中间值，3：成果值")
    private Integer thingType;

    @Schema(description = "权重，数字越小越靠前")
    @Min(0)
    private Integer thingWeight;

    @Schema(description = "iot属性标识符")
    private String thingIdentityIot;

    @Schema(description = "计算公式")
    private String calcFormula;

    @Schema(description = "公式有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "公式有效结束时间")
    private LocalDateTime effectiveEndTime;

}