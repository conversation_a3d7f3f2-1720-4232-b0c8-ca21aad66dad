package cn.powerchina.bjy.link.dam.service.user;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.user.bo.UserBO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserPasswordVO;
import cn.powerchina.bjy.link.dam.controller.admin.user.vo.UserSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.user.UserDO;
import jakarta.validation.Valid;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface UserService {

    /**
     * 创建用户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUser(@Valid UserSaveReqVO createReqVO);

    /**
     * 更新用户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateUser(UserSaveReqVO updateReqVO);

    /**
     * 更新密码
     *
     * @param userPasswordVO
     */
    void updatePassword(@Valid UserPasswordVO userPasswordVO);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 删除用户信息
     *
     * @param id 编号
     */
    void deleteUser(Long id);

    /**
     * 获得用户信息
     *
     * @param id 编号
     * @return 用户信息
     */
    UserDO getUser(Long id);

    /**
     * 获得用户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户信息分页
     */
    PageResult<UserBO> getUserPage(UserPageReqVO pageReqVO);

    /**
     * 根据用户名和用户类型查找用户
     *
     * @param name
     * @param userType
     * @return
     */
    UserDO getUser(String name, Integer userType);

    UserDO getUserByUsername(UserSaveReqVO updateReqVO);
}