package cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 首页中间和底部数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndexMiddleBottomPageReqVO extends PageParam {

    @Schema(description = "项目id", example = "15150")
    private Long projectId;

    @Schema(description = "仪器类型名称", example = "赵六")
    private String instrumentName;

    @Schema(description = "仪器数量", example = "6337")
    private Long instrumentCount;

    @Schema(description = "仪器占比%")
    private String instrumentRate;

    @Schema(description = "观测记录数量", example = "20780")
    private Long pointDataCount;

    @Schema(description = "最近观测时间")
    private LocalDateTime pointTimeRecent;

    @Schema(description = "最早观测时间")
    private LocalDateTime pointTimeFirst;

    @Schema(description = "生成时间")
    private String generateTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
