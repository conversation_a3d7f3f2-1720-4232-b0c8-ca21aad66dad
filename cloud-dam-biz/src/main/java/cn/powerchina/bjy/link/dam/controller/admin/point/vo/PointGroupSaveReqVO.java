package cn.powerchina.bjy.link.dam.controller.admin.point.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/10
 */
@Schema(description = "管理后台 - 分组测点信息新增/修改 Request VO")
@Data
public class PointGroupSaveReqVO {

    @Schema(description = "项目id")
    @NotNull(message = "请选择一个项目")
    private Long projectId;

    @Schema(description = "工程分类id,即分组id")
    @NotNull(message = "请选择分组")
    private Long categoryId;

    @Schema(description = "测点id集合")
    private List<Long> pointIds;
}
