package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 测点数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointDataPageReqVO extends PageParam {

    @Schema(description = "项目id")
    @NotNull(message = "请选择项目")
    private Long projectId;

    @Schema(description = "测点id集合")
    @NotEmpty(message = "请选择测点")
    private List<Long> pointIds;

    @Schema(description = "时间类型(1：月统计，2：年统计，3：自定义时间段）")
    private Integer timeType;

    @Schema(description = "监测时间：yyyy-MM-dd")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @NotEmpty(message = "请选择日期")
    private LocalDate[] pointTime;

    @Schema(description = "分量id集合")
    @NotEmpty(message = "请选择分量")
    private List<Long> instrumentModelIds;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）")
    private Integer dataType;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）")
    private List<Integer> dataTypeList;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private List<Integer> dataStatusList;

    @Schema(description = "使用绝对值,0:不使用，1：使用")
    private Integer useValueAbsolute = 0;

}