package cn.powerchina.bjy.link.dam.controller.admin.monitorchart.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "dataZoom")
@Data
public class PointDataProcessLineTooboxFeatureDataZoomVO {

    @Schema(description = "show")
    private Boolean show;

    @Schema(description = "yAxisIndex")
    @JsonProperty(value = "yAxisIndex")
    private String yAxisIndex;

}