package cn.powerchina.bjy.link.dam.service.iotproductmodel;

import cn.powerchina.bjy.link.dam.dal.dataobject.iotproduct.IotProductDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.iotproductmodel.IotProductModelDO;

import java.util.List;

/**
 * 物联网平台同步的产品物模型 Service 接口
 *
 * <AUTHOR>
 */
public interface IotProductModelService {

    /**
     * 新增
     * @param iotProductModelDO 物联网平台库的产品物模型
     */
    void createIotProductModel(IotProductModelDO iotProductModelDO);

    /**
     * 根据物联网平台库的产品id删除
     * @param iotProductIotId 物联网平台库的产品id
     */
    void deleteByIotProductIotId(Long iotProductIotId);

    /**
     * 根据物联网平台库的产品物模型id删除
     * @param iotId 物联网平台库的产品物模型id
     */
    void deleteByIotId(Long iotId);

    /**
     * 根据物联网平台库的产品物模型id更新
     * @param iotProductModelDO 物联网平台库的产品物模型
     */
    void updateByIotId(IotProductModelDO iotProductModelDO);

    /**
     * 根据物联网平台库的产品物模型id获取
     * @param iotId 物联网平台库的产品物模型id
     * @return 物联网平台库同步的产品物模型
     */
    IotProductModelDO getByIotId(Long iotId);

    /**
     * 根据产品编码和物模型类型获取
     * @param productCode 产品编码
     * @param thingType 物模型类型
     * @return 物联网平台同步的产品物模型
     */
    List<IotProductModelDO> listByProductCodeThingType(String productCode, Integer thingType);
}
