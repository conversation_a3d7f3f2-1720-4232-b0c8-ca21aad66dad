package cn.powerchina.bjy.link.dam.controller.admin.project;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.project.bo.ProjectBO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.project.vo.ProjectSimpleRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 项目管理")
@RestController
@RequestMapping("/dam/project")
@Validated
public class ProjectController {

    @Resource
    private ProjectService projectService;

    @PostMapping("/create")
    @Operation(summary = "创建项目管理")
//    @PreAuthorize("@ss.hasPermission('dam:project:create')")
    public CommonResult<Long> createProject(@Valid @RequestBody ProjectSaveReqVO createReqVO) {
        return success(projectService.createProject(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新项目管理")
//    @PreAuthorize("@ss.hasPermission('dam:project:update')")
    public CommonResult<Boolean> updateProject(@Valid @RequestBody ProjectSaveReqVO updateReqVO) {
        projectService.updateProject(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除项目管理")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:project:delete')")
    public CommonResult<Boolean> deleteProject(@RequestParam("id") Long id) {
        projectService.deleteProject(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得项目管理")
    @Parameter(name = "id", description = "编号")
//    @PreAuthorize("@ss.hasPermission('dam:project:query')")
    public CommonResult<ProjectRespVO> getProject(@RequestParam("id") Long id) {
        ProjectBO project = projectService.getProjectBO(id);
        return success(BeanUtils.toBean(project, ProjectRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得项目管理分页")
//    @PreAuthorize("@ss.hasPermission('dam:project:query')")
    public CommonResult<PageResult<ProjectRespVO>> getProjectPage(@Valid ProjectPageReqVO pageReqVO) {
        PageResult<ProjectBO> pageResult = projectService.getProjectBOPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProjectRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得项目管理列表")
//    @PreAuthorize("@ss.hasPermission('dam:project:query')")
    public CommonResult<List<ProjectSimpleRespVO>> getProjectList() {
        List<ProjectDO> projectDOList = projectService.getProjectList();
        return success(BeanUtils.toBean(projectDOList, ProjectSimpleRespVO.class));
    }

}