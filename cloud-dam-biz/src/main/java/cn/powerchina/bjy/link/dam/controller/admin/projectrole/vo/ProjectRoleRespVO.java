package cn.powerchina.bjy.link.dam.controller.admin.projectrole.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 项目角色信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectRoleRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "角色名称")
    @ExcelProperty("角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    @ExcelProperty("角色描述")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "角色用户数")
    @ExcelProperty("角色用户数")
    private int userCount;

}