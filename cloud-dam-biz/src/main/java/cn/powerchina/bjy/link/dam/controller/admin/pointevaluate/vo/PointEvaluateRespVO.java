package cn.powerchina.bjy.link.dam.controller.admin.pointevaluate.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点评价指标 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointEvaluateRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "项目id")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "测点id")
    @ExcelProperty("测点id")
    private Long pointId;

    @Schema(description = "分量id")
    @ExcelProperty("分量id")
    private Long instrumentModelId;

    @Schema(description = "分量名称")
    @ExcelProperty("分量名称")
    private String thingName;

    @Schema(description = "分量标识符")
    @ExcelProperty("分量标识符")
    private String thingIdentity;

    @Schema(description = "报警上限")
    @ExcelProperty("报警上限")
    private Integer waringUp;

    @Schema(description = "报警下限")
    @ExcelProperty("报警下限")
    private Integer waringDown;

    @Schema(description = "有效开始时间")
    @ExcelProperty("有效开始时间")
    private LocalDateTime effectiveStartTime;

    @Schema(description = "有效结束时间")
    @ExcelProperty("有效结束时间")
    private LocalDateTime effectiveEndTime;

    @Schema(description = "适用类型，1：全部，2：自动化，3：人工")
    @ExcelProperty("适用类型，1：全部，2：自动化，3：人工")
    private Integer applyType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "下限")
    @ExcelProperty("下限")
    private String downLimit;

    @Schema(description = "上限")
    @ExcelProperty("上限")
    private String upLimit;

    @Schema(description = "小数位")
    @ExcelProperty("小数位")
    private Integer decimalLimit;

    @Schema(description = "异常上限")
    private String abnormalUp;

    @Schema(description = "异常下限")
    private String abnormalDown;

}