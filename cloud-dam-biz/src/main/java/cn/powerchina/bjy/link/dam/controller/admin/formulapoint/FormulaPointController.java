package cn.powerchina.bjy.link.dam.controller.admin.formulapoint;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointListVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.formulapoint.vo.FormulaPointSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulapoint.FormulaPointDO;
import cn.powerchina.bjy.link.dam.service.formulapoint.FormulaPointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 公式关联测点")
@RestController
@RequestMapping("/dam/formula/point")
@Validated
public class FormulaPointController {

    @Resource
    private FormulaPointService formulaPointService;

    @PostMapping("/create")
    @Operation(summary = "创建公式关联测点")
//    @PreAuthorize("@ss.hasPermission('dam:formula-point:create')")
    public CommonResult<Long> createFormulaPoint(@Valid @RequestBody FormulaPointSaveReqVO createReqVO) {
        return success(formulaPointService.createFormulaPoint(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新公式关联测点")
//    @PreAuthorize("@ss.hasPermission('dam:formula-point:update')")
    public CommonResult<Boolean> updateFormulaPoint(@Valid @RequestBody FormulaPointSaveReqVO updateReqVO) {
        formulaPointService.updateFormulaPoint(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除公式关联测点")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:formula-point:delete')")
    public CommonResult<Boolean> deleteFormulaPoint(@RequestParam("id") Long id) {
        formulaPointService.deleteFormulaPoint(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得公式关联测点")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:formula-point:query')")
    public CommonResult<FormulaPointRespVO> getFormulaPoint(@RequestParam("id") Long id) {
        FormulaPointDO formulaPoint = formulaPointService.getFormulaPoint(id);
        return success(BeanUtils.toBean(formulaPoint, FormulaPointRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得公式关联测点列表")
    @Parameter(name = "pointFormulaId", description = "测点公式id", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:formula-point:query')")
    public CommonResult<List<FormulaPointListVO>> getFormulaPointList(@RequestParam("pointFormulaId") long pointFormulaId) {
        List<FormulaPointListVO> formulaPointList = formulaPointService.getFormulaPointList(pointFormulaId);
        return success(formulaPointList);
    }

}