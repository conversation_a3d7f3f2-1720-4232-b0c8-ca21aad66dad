package cn.powerchina.bjy.link.dam.controller.admin.index.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/11/22
 */
@Data
public class IndexPointDeviceBO {

    /**
     * 仪器类型名称
     */
    private String instrumentName;

    /**
     * 仪器数量
     */
    private Long instrumentCount;

    /**
     * 仪器占比%
     */
    private String instrumentRate;

    /**
     * 观测记录数量
     */
    private Long pointDataCount;

    /**
     * 最近观测时间
     */
    private LocalDateTime pointTimeRecent;

    /**
     * 最早观测时间
     */
    private LocalDateTime pointTimeFirst;
}
