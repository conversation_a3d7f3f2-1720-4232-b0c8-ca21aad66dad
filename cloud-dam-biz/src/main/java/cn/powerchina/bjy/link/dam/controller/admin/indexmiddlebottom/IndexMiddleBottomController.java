package cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomRespVO;
import cn.powerchina.bjy.link.dam.controller.admin.indexmiddlebottom.vo.IndexMiddleBottomSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.indexmiddlebottom.IndexMiddleBottomDO;
import cn.powerchina.bjy.link.dam.service.indexmiddlebottom.IndexMiddleBottomService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 首页中间和底部数据")
@RestController
@RequestMapping("/dam/index-middle-bottom")
@Validated
public class IndexMiddleBottomController {

    @Resource
    private IndexMiddleBottomService indexMiddleBottomService;

    @PostMapping("/create")
    @Operation(summary = "创建首页中间和底部数据")
//    @PreAuthorize("@ss.hasPermission('dam:index-middle-bottom:create')")
    public CommonResult<Long> createIndexMiddleBottom(@Valid @RequestBody IndexMiddleBottomSaveReqVO createReqVO) {
        return success(indexMiddleBottomService.createIndexMiddleBottom(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新首页中间和底部数据")
//    @PreAuthorize("@ss.hasPermission('dam:index-middle-bottom:update')")
    public CommonResult<Boolean> updateIndexMiddleBottom(@Valid @RequestBody IndexMiddleBottomSaveReqVO updateReqVO) {
        indexMiddleBottomService.updateIndexMiddleBottom(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除首页中间和底部数据")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('dam:index-middle-bottom:delete')")
    public CommonResult<Boolean> deleteIndexMiddleBottom(@RequestParam("id") Long id) {
        indexMiddleBottomService.deleteIndexMiddleBottom(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得首页中间和底部数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('dam:index-middle-bottom:query')")
    public CommonResult<IndexMiddleBottomRespVO> getIndexMiddleBottom(@RequestParam("id") Long id) {
        IndexMiddleBottomDO indexMiddleBottom = indexMiddleBottomService.getIndexMiddleBottom(id);
        return success(BeanUtils.toBean(indexMiddleBottom, IndexMiddleBottomRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得首页中间和底部数据分页")
//    @PreAuthorize("@ss.hasPermission('dam:index-middle-bottom:query')")
    public CommonResult<PageResult<IndexMiddleBottomRespVO>> getIndexMiddleBottomPage(@Valid IndexMiddleBottomPageReqVO pageReqVO) {
        PageResult<IndexMiddleBottomDO> pageResult = indexMiddleBottomService.getIndexMiddleBottomPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndexMiddleBottomRespVO.class));
    }


}
