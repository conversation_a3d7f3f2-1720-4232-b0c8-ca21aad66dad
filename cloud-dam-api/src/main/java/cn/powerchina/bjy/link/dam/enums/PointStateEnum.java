package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测点状态，1：在测，2：一般性检查，3：停测，4：封存，5：损坏，6：报废，7：其他
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum PointStateEnum {

    NORMAL(1, "在测"),
    GENERAL(2, "一般性检查"),
    STOP(3, "停测"),
    SEAL(4, "封存"),
    DAMAGE(5, "损坏"),
    SCRAP(6, "报废"),
    OTHER(7, "其他");

    private final Integer type;
    private final String desc;

}
