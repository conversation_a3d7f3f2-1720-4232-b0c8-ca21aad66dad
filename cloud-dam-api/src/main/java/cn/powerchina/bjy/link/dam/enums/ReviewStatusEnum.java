package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测点数据类型
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum ReviewStatusEnum {

    NOT_REVIEWED(0, "未审核"),
    APPROVED(1, "审核通过"),
    REVIEW_REJECTION(2, "审核不通过");

    private final Integer type;
    private final String desc;

    public static String getNameByCode(Integer code) {
        for (ReviewStatusEnum typeEnum : ReviewStatusEnum.values()) {
            if (typeEnum.getType().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }
}
