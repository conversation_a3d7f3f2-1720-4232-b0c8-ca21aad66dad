package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/9
 */
@Getter
@AllArgsConstructor
public enum PointParamsTableEnum {

    POINT_CODE("测点编号", "pointCode"),
    POINT_TIME("监测时间", "pointTime"),
    INSTRUMENT_NAME("仪器类型", "instrumentName"),
    POINT_STATE("测点状态", "pointState"),
    EFFECTIVE_START_TIME("起始时间", "effectiveStartTime"),
    EFFECTIVE_END_TIME("截止时间", "effectiveEndTime"),
    APPLY_TYPE("适用类型", "applyType"),
    DATA_STATUS("数据状态", "dataStatus"),
    REVIEW_STATUS("审核状态", "reviewStatus"),
    REVIEW_NAME("审核人", "reviewName"),
    CREATOR("录入人", "creatorName"),
    CREATE_TIME("录入时间", "createTime"),
    DATA_TYPE("采集类型", "dataType");

    private final String labelName;

    private final String fieldName;

}
