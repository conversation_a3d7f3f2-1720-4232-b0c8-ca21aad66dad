package cn.powerchina.bjy.link.dam.api.devicecollectlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Description: 采集仪执行计划
 * @Author: yhx
 * @CreateDate: 2024/9/23
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 边缘网关采集执行计划信息")
public interface DeviceCollectLogApi {

    String PREFIX = ApiConstants.PREFIX + "/xxljob/deviceCollectLog";

    @GetMapping(PREFIX + "/run")
    @Operation(summary = "执行采集仪的执行计划")
    CommonResult<Boolean> runDeviceCollectLog();
}
