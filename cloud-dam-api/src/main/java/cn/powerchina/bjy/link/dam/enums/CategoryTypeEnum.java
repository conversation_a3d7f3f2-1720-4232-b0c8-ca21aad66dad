package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 工程分类枚举
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum CategoryTypeEnum {

    STATION(1, "监测站点"),
    STRUCT(2, "工程结构"),
    INSTRUMENT(3, "仪器类型"),
    GROUP(4, "分组"),
    POINT(5, "测点"),
    NET_DEVICE_MCU(6, "网关设备"),
    NET_DEVICE_SUB(7, "网关子设备"),
    MONITORING_GRAPH(8, "监测图形");

    private final Integer type;
    private final String desc;

    /**
     * 根据类型获取描述
     *
     * @param type
     * @return
     */
    public static String getDescByType(Integer type) {
        for (CategoryTypeEnum category : CategoryTypeEnum.values()) {
            if (Objects.equals(type, category.type)) {
                return category.getDesc();
            }
        }
        return null;
    }

}
