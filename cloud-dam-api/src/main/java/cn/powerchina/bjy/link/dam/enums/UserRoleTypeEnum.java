package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/4
 */
@Getter
@AllArgsConstructor
public enum UserRoleTypeEnum {
    SYSTEM_ADMIN(1, "system_admin", "系统管理员"),
    PROJECT_ADMIN(2, "project_admin", "项目管理员");

    private final Integer userType;
    private final String code;
    private final String desc;

    public static String getCodeByType(Integer roleType) {
        for (UserRoleTypeEnum roleTypeEnum : UserRoleTypeEnum.values()) {
            if (roleTypeEnum.getUserType().equals(roleType)) {
                return roleTypeEnum.getCode();
            }
        }
        return null;
    }
}
