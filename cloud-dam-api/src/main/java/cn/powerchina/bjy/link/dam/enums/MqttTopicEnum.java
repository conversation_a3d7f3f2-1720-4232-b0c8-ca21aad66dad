package cn.powerchina.bjy.link.dam.enums;

/**
 * mqtt的topic名称枚举
 */
public enum MqttTopicEnum {

    /**
     * 产品topic
     */
    PRODUCT_TOPIC("/iot/sys/products/dataforward"),

    /**
     * 设备topic
     */
    DEVICE_TOPIC("/iot/sys/devices/dataforward"),

    /**
     * 设备状态变更topic
     */
    DEVICE_STATUE_TOPIC("/iot/sys/devices/statue"),

    /**
     * 设备属性上报
     */
    PROPERTIES_REPORT_TOPIC("/iot/sys/properties/report");


    private final String topic;

    MqttTopicEnum(String topic) {
        this.topic = topic;
    }

    public String getTopic() {
        return topic;
    }
}
