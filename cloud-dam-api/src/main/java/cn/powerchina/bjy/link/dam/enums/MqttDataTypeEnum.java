package cn.powerchina.bjy.link.dam.enums;

/**
 * mqtt数据类型枚举
 */
public enum MqttDataTypeEnum {

    /**
     * 产品创建
     */
    PRODUCT_CREATE("products_create"),

    /**
     * 产品更新
     */
    PRODUCT_UPDATE("products_update"),

    /**
     * 产品删除
     */
    PRODUCT_DELETE("products_delete"),

    /**
     * 设备创建
     */
    DEVICE_CREATE("devices_create"),

    /**
     * 设备更新
     */
    DEVICE_UPDATE("devices_update"),

    /**
     * 设备删除
     */
    DEVICE_DELETE("devices_delete"),

    /**
     * 设备状态变更
     */
    DEVICE_STATUE_UPDATE("devices_statue"),

    /**
     * 设备属性上报
     */
    PROPERTIES_REPORT("properties_report")
    ;

    private final String dataType;

    MqttDataTypeEnum(String dataType) {
        this.dataType = dataType;
    }

    public String getDataType() {
        return dataType;
    }
}
