package cn.powerchina.bjy.link.dam.enums;

import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;

/**
 * iot 错误码枚举类
 * <p>
 * iot 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== RPC调用 ==========
    ErrorCode RPC_EXCEPTION = new ErrorCode(1_003_100_000, "RPC调用异常:{}");
    ErrorCode RPC_ERROR = new ErrorCode(1_003_101_000, "{}");

    // ========== 项目管理 ==========
    ErrorCode PROJECT_NOT_EXISTS = new ErrorCode(1_003_000_000, "项目不存在");
    ErrorCode PROJECT_NAME_EXISTS = new ErrorCode(1_003_000_001, "项目名称已存在");

    // ========== 项目工程信息 ==========
    ErrorCode PROJECT_INFO_NOT_EXISTS = new ErrorCode(1_003_001_000, "项目工程信息不存在");
    ErrorCode PROJECT_INFO_POINT_EXISTS = new ErrorCode(1_003_001_001, "删除错误，项目下存在测点，请删除所有测点后重试");

    // ========== 大坝设备 ==========
    ErrorCode DEVICE_NOT_EXISTS = new ErrorCode(1_003_003_000, "设备不存在");
    ErrorCode DEVICE_OFFLINE = new ErrorCode(1_003_003_001, "设备已离线");
    ErrorCode DEVICE_FORBID_DELETED = new ErrorCode(1_003_003_002, "已绑定测点设备不能被删除！");

    // ========== 用户信息 ==========
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_003_005_000, "用户信息不存在");
    ErrorCode USER_ADMIN_NOT_EXISTS = new ErrorCode(1_003_005_001, "项目管理员不存在");
    ErrorCode USER_ADMIN_PROJECT_EXISTS = new ErrorCode(1_003_005_002, "删除错误，该用户有管理的项目，请取消其管理员权限后再重试");
    ErrorCode USER_ADMIN_NAME_EXISTS = new ErrorCode(1_003_005_003, "用户名已经存在");
    ErrorCode USER_ADMIN_IPHONE_EXISTS = new ErrorCode(1_003_005_003, "手机号已经存在");

    // ========== 项目用户  ==========
    ErrorCode PROJECT_USER_NOT_EXISTS = new ErrorCode(1_003_006_000, "项目用户不存在");
    ErrorCode PROJECT_USER_NOT_BIND_PROJECT = new ErrorCode(1_003_006_001, "该账号没有项目权限，请联系系统管理员");
    ErrorCode PROJECT_USER_BIND_PROJECT = new ErrorCode(1_003_006_002, "该用户已绑定项目，请解绑后再修改");

    // ========== 项目角色信息 ==========
    ErrorCode PROJECT_ROLE_NOT_EXISTS = new ErrorCode(1_003_007_000, "项目角色信息不存在");
    ErrorCode PROJECT_ROLE_USER_EXISTS = new ErrorCode(1_003_007_001, "删除错误，角色下包含用户，请移除用户后重试");
    ErrorCode PROJECT_ROLE_NAME_EXISTS = new ErrorCode(1_003_007_002, "角色名称已存在");

    // ========== 项目菜单 ==========
    ErrorCode PROJECT_MENU_NOT_EXISTS = new ErrorCode(1_003_008_000, "项目菜单不存在");

    // ========== 工程分类管理  ==========
    ErrorCode PROJECT_CATEGORY_NOT_EXISTS = new ErrorCode(1_003_009_000, "工程分类管理不存在");
    ErrorCode PROJECT_CATEGORY_STRUCT_CHILDREN_EXISTS = new ErrorCode(1_003_009_001, "删除错误，建筑物下包含子建筑物，请删除子建筑物后重试");
    ErrorCode PROJECT_CATEGORY_STRUCT_POINT_EXISTS = new ErrorCode(1_003_009_005, "删除错误，建筑物下已创建测点，请删除所有测点后重试");
    ErrorCode PROJECT_CATEGORY_STATION_CHILDREN_EXISTS = new ErrorCode(1_003_009_004, "删除错误，测站下包含子测站，请删除子测站后重试");
    ErrorCode PROJECT_CATEGORY_STATION_DEVICE_EXISTS = new ErrorCode(1_003_009_006, "删除错误，测站下已绑定mcu采集仪，请解除绑定后重试");
    ErrorCode PROJECT_CATEGORY_POINT_EXISTS = new ErrorCode(1_003_009_003, "删除错误，仪器类型下包含测点，请删除所有测点后重试");
    ErrorCode PROJECT_CATEGORY_NAME_EXISTS = new ErrorCode(1_003_009_002, "节点名称已存在");
    ErrorCode PROJECT_GROUP_CHILDREN_ADD_ERROR = new ErrorCode(1_003_009_007, "分组下不能再创建子节点");
    ErrorCode PROJECT_STRUCT_CHILDREN_ADD_ERROR = new ErrorCode(1_003_009_008, "当前工程目录下已创建仪器类型,不能再创建子节点");
    ErrorCode PROJECT_STRUCT_INSTRUMENT_ADD_ERROR = new ErrorCode(1_003_009_010, "只能在工程目录子节点创建");
    ErrorCode PROJECT_CATEGORY_NAME_EMPTY_ERROR = new ErrorCode(1_003_009_011, "节点名称不能为空");
    ErrorCode PROJECT_STATION_CHILDREN_ADD_ERROR = new ErrorCode(1_003_009_012, "当前监测站点已关联采集设备,不能再创建子测站");
    ErrorCode PROJECT_CATEGORY_UPDATE_PARENT_ERROR = new ErrorCode(1_003_009_13, "根节点不能修改");
    ErrorCode PROJECT_CATEGORY_DELETE_PARENT_ERROR = new ErrorCode(1_003_009_14, "根节点不能删除");
    ErrorCode PROJECT_CATEGORY_CREATE_PARENT_ERROR = new ErrorCode(1_003_009_13, "根节点不能创建");
    ErrorCode PROJECT_CATEGORY_INSTRUMENT_DELETED = new ErrorCode(1_003_009_15, "仪器类型被删除");


    // ========== 设备授权 ==========
    ErrorCode AUTH_DEVICE_NOT_EXISTS = new ErrorCode(1_003_010_000, "设备授权不存在");
    ErrorCode AUTH_DEVICE_POINT_EXISTS = new ErrorCode(1_003_010_001, "当前设备已绑定测点，请先解绑");

    // ========== 产品授权 ==========
    ErrorCode AUTH_PRODUCT_NOT_EXISTS = new ErrorCode(1_003_011_000, "产品授权不存在");

    // ========== 设备采集策略 ==========
    ErrorCode DEVICE_STRATEGY_NOT_EXISTS = new ErrorCode(1_003_012_000, "设备采集策略不存在");
    ErrorCode DEVICE_STRATEGY_USE_EXISTS = new ErrorCode(1_003_012_001, "删除错误，采集策略已被mcu采集仪引用，请取消引用后重试");
    ErrorCode DEVICE_STRATEGY_NAME_EXISTS = new ErrorCode(1_003_012_002, "设备采集策略名称重复");

    // ========== 仪器类型 ==========
    ErrorCode INSTRUMENT_NOT_EXISTS = new ErrorCode(1_003_014_000, "仪器类型不存在");
    ErrorCode INSTRUMENT_POINT_EXISTS = new ErrorCode(1_003_014_001, "仪器类型下存在测点，无法删除");
    ErrorCode INSTRUMENT_ACCOUNT_EXISTS = new ErrorCode(1_003_014_001, "仪器类型下存在仪器台账，无法删除");
    ErrorCode INSTRUMENT_NAME_EXISTS = new ErrorCode(1_003_014_002, "仪器类型名称已存在");
    ErrorCode INSTRUMENT_USING_POINT = new ErrorCode(1_003_014_003, "删除错误，仪器类型已被测点引用");
    ErrorCode PARAM_MODEL_REPEAT = new ErrorCode(1_003_014_003, "本次提交存在重复参数,请检查");

    // ========== 仪器类型-测量分量 ==========
    ErrorCode INSTRUMENT_MODEL_NOT_EXISTS = new ErrorCode(1_003_015_000, "仪器类型-测量分量/公式不存在");
    ErrorCode INSTRUMENT_MODEL_NAME_EXISTS = new ErrorCode(1_003_015_001, "测量分量名称已存在");
    ErrorCode INSTRUMENT_MODEL_THING_IDENTITY_EXISTS = new ErrorCode(1_003_015_002, "仪器类型-测量分量标识符已存在");
    ErrorCode INSTRUMENT_MODEL_THING_IDENTITY_IOT_BIND = new ErrorCode(1_003_015_003, "IOT属性标识已绑定");
    ErrorCode INSTRUMENT_MODEL_USING = new ErrorCode(1_003_015_004, "测量分量被公式使用，不允许删除");
    ErrorCode INSTRUMENT_MODEL_NOT_EXISTS_BY_ID = new ErrorCode(1_003_015_005, "仪器类型-测量分量不存在");


    // ========== 仪器类型-计算参数 ==========
    ErrorCode INSTRUMENT_PARAM_NOT_EXISTS = new ErrorCode(1_003_016_000, "仪器类型-计算参数不存在");
    ErrorCode INSTRUMENT_PARAMS_NAME_EXISTS = new ErrorCode(1_003_015_001, "仪器类型-计算参数名称已存在");
    ErrorCode INSTRUMENT_PARAMS_THING_IDENTITY_EXISTS = new ErrorCode(1_003_015_002, "仪器类型-计算参数标识符已存在");
    ErrorCode INSTRUMENT_PARAMS_USING = new ErrorCode(1_003_015_002, "计算参数被公式使用，不允许删除");
    ErrorCode INSTRUMENT_MODEL_THING_REQUIRED = new ErrorCode(1_003_015_003, "分量类型参数必填");

    // ========== 仪器类型-计算公式 ==========
    ErrorCode INSTRUMENT_FORMULA_NOT_EXISTS = new ErrorCode(1_003_017_000, "仪器类型-计算公式不存在");
    ErrorCode INSTRUMENT_FORMULA_ERROR = new ErrorCode(1_003_017_001, "公式{}解析失败:{}");

    // ========== 测点信息 ==========
    ErrorCode POINT_NOT_EXISTS = new ErrorCode(1_003_013_000, "测点信息不存在");
    ErrorCode POINT_SELECT_INSTRUMENT_ERROR = new ErrorCode(1_003_013_001, "请选择一个仪器类型");
    ErrorCode POINT_SELECT_STRUCT_ERROR = new ErrorCode(1_003_013_002, "请选择一个工程结构");
    ErrorCode POINT_SELECT_INSTRUMENT_EXISTS = new ErrorCode(1_003_013_003, "仪器类型已存在");
    ErrorCode POINT_SELECT_LEAF_ERROR = new ErrorCode(1_003_013_004, "请选择一个仪器类型或分组");
    ErrorCode POINT_SELECT_GROUP_ERROR = new ErrorCode(1_003_013_005, "请选择一个分组");
    ErrorCode POINT_SELECT_STATION_ERROR = new ErrorCode(1_003_013_006, "请选择一个测站");
    ErrorCode POINT_CODE_EXISTS = new ErrorCode(1_003_013_007, "测点编号已存在");
    ErrorCode POINT_DELETE_EXISTS = new ErrorCode(1_003_013_009, "操作错误，仪器台账下包含测点，请删除所有测点后重试");
    ErrorCode POINT_INSTRUMENT_STRUCT_EXISTS = new ErrorCode(1_003_013_008, "删除错误，{}下包含测点，请删除所有测点后重试");
    ErrorCode POINT_DELETE_DATA_EXISTS = new ErrorCode(1_003_013_009, "删除错误，测点下已有监测数据");
    ErrorCode POINT_DELETE_BIND_EXISTS = new ErrorCode(1_003_013_010, "删除错误，测点已绑定监测设备，请解绑后再重试");

    // ========== 测点计算参数 ==========
    ErrorCode POINT_PARAM_NOT_EXISTS = new ErrorCode(1_003_018_000, "测点计算参数不存在");
    ErrorCode POINT_CODE_NAME_EXISTS = new ErrorCode(1_003_018_001, "测点编码重复");
    ErrorCode POINT_CODE_LENGTH_EXISTS = new ErrorCode(1_003_018_002, "测点编码长度不符合规范");
    ErrorCode POINT_INSTRUMENT_NOT_EXISTS = new ErrorCode(1_003_018_003, "测点绑定的仪器类型不存在");
    ErrorCode POINT_PARAM_TIME_LAP = new ErrorCode(1_003_018_004, "有效时段重合");
    ErrorCode POINT_PARAM_TIME_ERROR = new ErrorCode(1_003_018_005, "截止时间必须大于起始时间");

    ErrorCode POINT_PARAM_SIZE_ERROR = new ErrorCode(1_003_018_006, "测点计算参数不允许少于一条");

    // ========== 测点计算公式 ==========
    ErrorCode POINT_FORMULA_NOT_EXISTS = new ErrorCode(1_003_019_000, "测点计算公式不存在");

    // ========== 公式关联分量 ==========
    ErrorCode FORMULA_MODEL_NOT_EXISTS = new ErrorCode(1_003_020_000, "公式关联分量不存在");

    // ========== 公式关联测点 ==========
    ErrorCode FORMULA_POINT_NOT_EXISTS = new ErrorCode(1_003_021_000, "公式关联测点不存在");

    // ========== 测点评价指标 ==========
    ErrorCode POINT_EVALUATE_NOT_EXISTS = new ErrorCode(1_003_022_000, "测点评价指标不存在");
    ErrorCode POINT_EVALUATE_DUPLICATE_EXISTS = new ErrorCode(1_003_022_001, "测点评价指标重复");
    ErrorCode POINT_EVALUATE_TIME_LAP = new ErrorCode(1_003_018_004, "有效时段重合");
    ErrorCode POINT_EVALUATE_SIZE_ERROR = new ErrorCode(1_003_022_005, "测点评价指标不允许少于一条");

    // ========== 测点报警信息 ==========
    ErrorCode POINT_ALARM_NOT_EXISTS = new ErrorCode(1_003_023_000, "测点报警信息不存在");

    // ========== 测点数据导入 ==========
    ErrorCode POINT_DATA_IMPORT_NOT_EXISTS = new ErrorCode(1_003_024_000, "测点数据导入不存在");
    ErrorCode POINT_DATA_IMPORT_TIME_NOT_NULL = new ErrorCode(1_003_024_001, "覆盖时段不能为空");
    ErrorCode POINT_DATA_IMPORT_START_END_TIME = new ErrorCode(1_003_024_002, "开始时间不能小于结束时间");
    ErrorCode POINT_DATA_IMPORT_TIME_CHECK = new ErrorCode(1_003_024_003, "监测时间必须在覆盖时间段内");
    ErrorCode POINT_DATA_IMPORT_DATA_ERROR = new ErrorCode(1_003_024_004, "测点数据导入异常:{}");
    ErrorCode POINT_DATA_IMPORT_DATA_TEMPLATE = new ErrorCode(1_003_024_005, "模板文件中第{}列，对应的分量不存在");
    ErrorCode POINT_DATA_IMPORT_TEMPLATE_ERROR = new ErrorCode(1_003_024_006, "下载模板异常:{}");
    ErrorCode POINT_DATA_IMPORT_TIME_FORMATTER = new ErrorCode(1_003_024_007, "excel中监测时间必须是yyyy-MM-dd HH:mm:ss格式");
    ErrorCode POINT_DATA_IMPORT_TIME_ERROR = new ErrorCode(1_003_024_008, "监测时间不能小于最后一条监测数据时间 {}");
    ErrorCode POINT_CODE_NOT_EXISTS = new ErrorCode(1_003_024_009, " 该仪器类型下不存在 {} 测点编号，请先新建测点");
    ErrorCode POINT_DATA_IMPORT_MODEL_NOT_EXISTS = new ErrorCode(1_003_024_010, " 导入的excel数据，缺少仪器类型测量分量(单位) {}");

    // ========== 网关设备采集时间 ==========
    ErrorCode DEVICE_COLLECT_TIME_NOT_EXISTS = new ErrorCode(1_003_025_000, "网关设备采集时间不存在");

    // ========== 网关设备采集日志 ==========
    ErrorCode DEVICE_COLLECT_LOG_NOT_EXISTS = new ErrorCode(1_003_026_000, "网关设备采集日志不存在");

    // ========== 测点数据 ==========
    ErrorCode POINT_DATA_NOT_EXISTS = new ErrorCode(1_003_027_000, "测点数据不存在");

    // ========== 测点数据json==========
    ErrorCode POINT_DATA_JSON_NOT_EXISTS = new ErrorCode(1_003_028_000, "测点数据json不存在");
    ErrorCode POINT_DATA_MODEL_NO_FORMULA = new ErrorCode(1_003_028_001, " [{}] 未匹配到有效计算公式，请检查计算公式后重试");
    ErrorCode POINT_DATA_MODEL_FORMULA_ERROR = new ErrorCode(1_003_028_002, "计算错误，[{}] 配置的计算公式不可用，请检查公式配置后重试");
    ErrorCode EXCEL_EXPORT_ERROR = new ErrorCode(1_003_028_003, "excel导出失败");
    ErrorCode POINT_DATA_JSON_NULL = new ErrorCode(1_003_028_004, "当前时间范围内没有采集数据");

    // ========== 仪器台账==========
    ErrorCode INSTRUMENT_ACCOUNT_NOT_EXISTS = new ErrorCode(1_003_029_000, "仪器台账不存在");

    // ========== 大坝首页头部信息 ==========
    ErrorCode INDEX_HEAD_NOT_EXISTS = new ErrorCode(1_003_030_000, "大坝首页头部信息不存在");

    // ========== 首页中间和底部数据 ==========
    ErrorCode INDEX_MIDDLE_BOTTOM_NOT_EXISTS = new ErrorCode(1_003_031_000, "首页中间和底部数据不存在");

    // ========== 仪器类型模板-计算参数 ==========
    ErrorCode INSTRUMENT_PARAM_TEMPLATE_NOT_EXISTS = new ErrorCode(1_003_032_000, "仪器类型模板-计算参数不存在");

    // ========== 仪器类型模板-测量分量  ==========
    ErrorCode INSTRUMENT_MODEL_TEMPLATE_NOT_EXISTS = new ErrorCode(1_003_033_000, "仪器类型模板-测量分量不存在");

    // ========== 仪器类型模板 ==========
    ErrorCode INSTRUMENT_TEMPLATE_NOT_EXISTS = new ErrorCode(1_003_034_000, "仪器类型模板不存在");

    ErrorCode INSTRUMENT_TEMPLATE_HAS_EXISTS = new ErrorCode(1_003_034_000, "仪器类型模板名称已存在");

    // ========== 测点评价指标极值  ==========
    ErrorCode POINT_EVALUATE_EXTREME_NOT_EXISTS = new ErrorCode(1_003_035_000, "测点评价指标极值不存在");
    ErrorCode POINT_EVALUATE_EXTREME_ERROR = new ErrorCode(1_003_035_001, "不支持该计算方式");


}
