package cn.powerchina.bjy.link.dam.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 适用类型
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum ApplyTypeEnum {

    ALL(1, "人/自一体"),
    AUTO(2, "自动化"),
    MANUAL(3, "人工");

    private final Integer code;
    private final String desc;

    public static ApplyTypeEnum getByCode(Integer code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }
}
