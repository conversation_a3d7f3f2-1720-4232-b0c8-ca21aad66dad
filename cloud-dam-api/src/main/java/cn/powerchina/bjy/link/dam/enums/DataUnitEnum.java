package cn.powerchina.bjy.link.dam.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/23
 */
@Getter
@AllArgsConstructor
public enum DataUnitEnum {
    MINUTE(1, "分钟"),
    HOUR(2, "小时"),
    DAY(3, "天");

    private final Integer type;
    private final String desc;

    public static DataUnitEnum getByType(Integer type) {
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }
}
