package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测点数据类型
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum DataStatusEnum {

    UNDETERMINED(0, "未判定"),
    NORMAL(1, "正常数据"),
    ANOMALOUS(2, "异常数据"),
    ERROR_DATA(3, "错误数据");

    private final Integer type;
    private final String desc;

    public static String getNameByCode(Integer code) {
        for (DataStatusEnum typeEnum : DataStatusEnum.values()) {
            if (typeEnum.getType().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }
}
