package cn.powerchina.bjy.link.dam.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/23
 */
@Getter
@AllArgsConstructor
public enum PointConditionEnum {
    TIME_FRAME_DATA(1, "时间范围内测值"),
    CURRENT_DATA(2, "当前时间的测值"),
    BEFORE_RECENT_DATA(3, "之前最近的测值"),
    CUMULATIVE_DATA(4, "前几个小时的累计值"),
    STATISTICS_DATA(5, "统计值");

    private final Integer type;
    private final String desc;

    public static PointConditionEnum getByType(Integer type) {
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }
}
