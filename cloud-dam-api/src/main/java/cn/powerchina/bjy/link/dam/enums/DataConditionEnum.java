package cn.powerchina.bjy.link.dam.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/9/23
 */
@Getter
@AllArgsConstructor
public enum DataConditionEnum {
    NONE(1, "无"),
    RELATIVE_DATA(2, "相对测值"),
    FIRST_DATA(3, "首次测值"),
    TIME_FRAME_DATA(4, "时间范围内测值");

    private final Integer type;
    private final String desc;

    public static DataConditionEnum getByType(Integer type) {
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }
}
