package cn.powerchina.bjy.link.dam.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测点数据采集类型
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum DataTypeEnum {

    AUTO_COLLECTION(1, "自动化采集"),
    MANUAL(2, "人工录入");

    private final Integer type;
    private final String desc;

    public static String getNameByCode(Integer code) {
        for (DataTypeEnum typeEnum : DataTypeEnum.values()) {
            if (typeEnum.getType().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }
}
