package cn.powerchina.bjy.link.dam.enums;

/**
 * mqtt产品物模型数据类型枚举
 */
public enum MqttModelDataTypeEnum {

    /**
     * 产品物模型创建
     */
    PRODUCT_MODEL_CREATE("products_model_create"),

    /**
     * 产品物模型更新
     */
    PRODUCT_MODEL_UPDATE("products_model_update"),

    /**
     * 产品物模型删除
     */
    PRODUCT_MODEL_DELETE("products_model_delete");

    private final String modelDataType;

    MqttModelDataTypeEnum(String modelDataType) {
        this.modelDataType = modelDataType;
    }

    public String getModelDataType() {
        return modelDataType;
    }
}
