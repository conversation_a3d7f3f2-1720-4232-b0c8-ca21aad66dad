package cn.powerchina.bjy.link.dam.enums;

import cn.powerchina.bjy.cloud.framework.common.enums.RpcConstants;

/**
 * API 相关的枚举
 *
 * <AUTHOR>
 */
public class ApiConstants {

    /**
     * 服务名
     * <p>
     * 注意，需要保证和 spring.application.name 保持一致
     */
    public static final String NAME = "dam-server";

    public static final String PREFIX = RpcConstants.RPC_API_PREFIX + "/dam";

    public static final String VERSION = "1.0.0";

    /**
     * 公式函数枚举
     */
    public static final String DICT_FORMULA = "dam_formula_function";

}
