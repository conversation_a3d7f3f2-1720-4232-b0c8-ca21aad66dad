package cn.powerchina.bjy.link.dam.api.index;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Description: 首页执行计划
 * @Author: zhaoqi<PERSON>
 * @CreateDate: 2025/2/19
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 定时统计首页信息")
public interface IndexStatisticsApi {

    String PREFIX = ApiConstants.PREFIX + "/xxljob/indexStatistics";

    @GetMapping(PREFIX + "/run")
    @Operation(summary = "统计首页信息的执行计划")
    CommonResult<Boolean> runIndexStatistics();
}
